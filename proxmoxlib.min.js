if(Ext.ns("Proxmox"),Ext.ns("Proxmox.Setup"),!Ext.isDefined(Proxmox.Setup.auth_cookie_name))throw"Proxmox library not initialized";if(!Ext.isDefined(Ext.global.console)){let e={dir:function(){},log:function(){},warn:function(){}};Ext.global.console=e}Ext.Ajax.defaultHeaders={Accept:"application/json"},Ext.Ajax.on("beforerequest",function(e,t){Proxmox.CSRFPreventionToken&&(t.headers||(t.headers={}),t.headers.CSRFPreventionToken=Proxmox.CSRFPreventionToken);var a=Proxmox.Utils.getStoredAuth();a.token&&(t.headers.Authorization=a.token)}),Ext.define("Proxmox.Utils",{utilities:{yesText:gettext("Yes"),noText:gettext("No"),enabledText:gettext("Enabled"),disabledText:gettext("Disabled"),noneText:gettext("none"),NoneText:gettext("None"),errorText:gettext("Error"),warningsText:gettext("Warnings"),unknownText:gettext("Unknown"),defaultText:gettext("Default"),daysText:gettext("days"),dayText:gettext("day"),runningText:gettext("running"),stoppedText:gettext("stopped"),neverText:gettext("never"),totalText:gettext("Total"),usedText:gettext("Used"),directoryText:gettext("Directory"),stateText:gettext("State"),groupText:gettext("Group"),language_map:{ar:"العربية - "+gettext("Arabic"),bg:"Български - "+gettext("Bulgarian"),ca:"Català - "+gettext("Catalan"),cs:"Czech - "+gettext("Czech"),da:"Dansk - "+gettext("Danish"),de:"Deutsch - "+gettext("German"),en:"English - "+gettext("English"),es:"Español - "+gettext("Spanish"),eu:"Euskera (Basque) - "+gettext("Euskera (Basque)"),fa:"فارسی - "+gettext("Persian (Farsi)"),fr:"Français - "+gettext("French"),hr:"Hrvatski - "+gettext("Croatian"),he:"עברית - "+gettext("Hebrew"),it:"Italiano - "+gettext("Italian"),ja:"日本語 - "+gettext("Japanese"),ka:"ქართული - "+gettext("Georgian"),ko:"한국어 - "+gettext("Korean"),nb:"Bokmål - "+gettext("Norwegian (Bokmal)"),nl:"Nederlands - "+gettext("Dutch"),nn:"Nynorsk - "+gettext("Norwegian (Nynorsk)"),pl:"Polski - "+gettext("Polish"),pt_BR:"Português Brasileiro - "+gettext("Portuguese (Brazil)"),ru:"Русский - "+gettext("Russian"),sl:"Slovenščina - "+gettext("Slovenian"),sv:"Svenska - "+gettext("Swedish"),tr:"Türkçe - "+gettext("Turkish"),ukr:"Українська - "+gettext("Ukrainian"),zh_CN:"中文（简体）- "+gettext("Chinese (Simplified)"),zh_TW:"中文（繁體）- "+gettext("Chinese (Traditional)")},render_language:function(e){if(!e||"__default__"===e)return Proxmox.Utils.defaultText+" (English)";"kr"===e&&(e="ko");var t=Proxmox.Utils.language_map[e];return t?t+" ("+e+")":e},renderEnabledIcon:e=>`<i class="fa fa-${e?"check":"minus"}"></i>`,language_array:function(){let a=[["__default__",Proxmox.Utils.render_language("")]];return Ext.Object.each(Proxmox.Utils.language_map,function(e,t){a.push([e,Proxmox.Utils.render_language(t)])}),a},theme_map:{crisp:"Light theme","proxmox-dark":"Proxmox Dark"},render_theme:function(e){return e&&"__default__"!==e?Proxmox.Utils.theme_map[e]||e:Proxmox.Utils.defaultText+" (auto)"},theme_array:function(){let a=[["__default__",Proxmox.Utils.render_theme("")]];return Ext.Object.each(Proxmox.Utils.theme_map,function(e,t){a.push([e,Proxmox.Utils.render_theme(t)])}),a},bond_mode_gettext_map:{"802.3ad":"LACP (802.3ad)","lacp-balance-slb":"LACP (balance-slb)","lacp-balance-tcp":"LACP (balance-tcp)"},render_bond_mode:e=>Proxmox.Utils.bond_mode_gettext_map[e]||e||"",bond_mode_array:function(e){return e.map(e=>[e,Proxmox.Utils.render_bond_mode(e)])},getNoSubKeyHtml:function(e){e=Ext.String.format('<a target="_blank" href="{0}">www.proxmox.com</a>',e||"https://www.proxmox.com");return Ext.String.format(gettext("You do not have a valid subscription for this server. Please visit {0} to get a list of available options."),e)},format_boolean_with_default:function(e){return Ext.isDefined(e)&&"__default__"!==e?e?Proxmox.Utils.yesText:Proxmox.Utils.noText:Proxmox.Utils.defaultText},format_boolean:function(e){return e?Proxmox.Utils.yesText:Proxmox.Utils.noText},format_neg_boolean:function(e){return e?Proxmox.Utils.noText:Proxmox.Utils.yesText},format_enabled_toggle:function(e){return e?Proxmox.Utils.enabledText:Proxmox.Utils.disabledText},format_expire:function(e){return e?Ext.Date.format(e,"Y-m-d"):Proxmox.Utils.neverText},format_duration_human:function(e){let t,a=0,i=0,o=0,n=0;if(e<=.1)return"<0.1s";t=Number((e%60).toFixed(1)),0<(e=Math.trunc(e/60))&&(a=e%60,0<(e=Math.trunc(e/60)))&&(i=e%24,0<(e=Math.trunc(e/24)))&&(o=e%365,0<(e=Math.trunc(e/365)))&&(n=e);let r=[];var e=(e,t)=>(0<e&&r.push(e+t),0<e),l=!e(n,"y"),s=!e(o,"d");return e(i,"h"),l&&(e(a,"m"),s)&&e(t,"s"),r.join(" ")},format_duration_long:function(e){var t=Math.floor(e/86400),a=(e-=86400*t,Math.floor(e/3600)),i=(e-=3600*a,Math.floor(e/60));e-=60*i;let o="00"+a.toString(),n=(o=o.substr(o.length-2),"00"+i.toString()),r=(n=n.substr(n.length-2),"00"+e.toString());return r=r.substr(r.length-2),t?(a=1<t?Proxmox.Utils.daysText:Proxmox.Utils.dayText,t.toString()+" "+a+" "+o+":"+n+":"+r):o+":"+n+":"+r},format_subscription_level:function(e){return"c"===e?"Community":"b"===e?"Basic":"s"===e?"Standard":"p"===e?"Premium":Proxmox.Utils.noneText},compute_min_label_width:function(e,t){void 0===t&&(t=100);e=(new Ext.util.TextMetrics).getWidth(e+":");return e<t?t:e},parse_userid:function(e){return Ext.isString(e)&&null!==(e=e.match(/^(.+)@([^@]+)$/))?[e[1],e[2]]:[void 0,void 0]},render_username:function(e){e=Proxmox.Utils.parse_userid(e)[0]||"";return Ext.htmlEncode(e)},render_realm:function(e){e=Proxmox.Utils.parse_userid(e)[1]||"";return Ext.htmlEncode(e)},getStoredAuth:function(){return JSON.parse(window.localStorage.getItem("ProxmoxUser"))||{}},setAuthData:function(e){Proxmox.UserName=e.username,Proxmox.LoggedOut=e.LoggedOut,e.ticket&&(Proxmox.CSRFPreventionToken=e.CSRFPreventionToken,Ext.util.Cookies.set(Proxmox.Setup.auth_cookie_name,e.ticket,null,"/",null,!0,"lax")),e.token&&window.localStorage.setItem("ProxmoxUser",JSON.stringify(e))},authOK:function(){var e,t;if(!Proxmox.LoggedOut)return e=Proxmox.Utils.getStoredAuth(),t=Ext.util.Cookies.get(Proxmox.Setup.auth_cookie_name),!!(""!==Proxmox.UserName&&t&&!t.startsWith("PVE:tfa!")||e.token)&&(t||e.token)},authClear:function(){Proxmox.LoggedOut||(Ext.util.Cookies.set(Proxmox.Setup.auth_cookie_name,"",new Date(0),null,null,!0,"lax"),window.localStorage.removeItem("ProxmoxUser"))},getOpenIDRedirectionAuthorization:function(){var e=Ext.Object.fromQueryString(window.location.search);if(void 0!==e.state&&void 0!==e.code)return e},setErrorMask:function(e,t){e=e.el;e&&(t?!0===t?e.mask(gettext("Loading...")):e.mask(t):e.unmask())},getResponseErrorMessage:t=>{if(!t.statusText)return gettext("Connection error");var a=[`${t.statusText} (${t.status})`];if(t.response&&t.response.responseText){t=t.response.responseText;try{var e=JSON.parse(t);if(e.errors&&"object"==typeof e.errors)for(var[i,o]of Object.entries(e.errors))a.push(Ext.String.htmlEncode(i+": "+o))}catch(e){a.push(Ext.String.htmlEncode(t))}}return a.join("<br>")},monStoreErrors:function(i,e,t,o){t?i.mon(e,"beforeload",function(e,t,a){Proxmox.Utils.setErrorMask(i,!1)}):i.mon(e,"beforeload",function(e,t,a){i.loadCount||(i.loadCount=0,Proxmox.Utils.setErrorMask(i,!0))}),i.mon(e.proxy,"afterload",function(e,t,a){i.loadCount++,a?Proxmox.Utils.setErrorMask(i,!1):(a=t._operation.getError(),t=Proxmox.Utils.getResponseErrorMessage(a),o&&o(a,t)||Proxmox.Utils.setErrorMask(i,Ext.htmlEncode(t)))})},extractRequestError:function(e,t){let a=gettext("Successful");return e.success||(a=gettext("Unknown error"),e.message&&(a=Ext.htmlEncode(e.message),e.status)&&(a+=` (${e.status})`),t&&Ext.isObject(e.errors)&&(a+="<br>",Ext.Object.each(e.errors,(e,t)=>{a+=`<br><b>${Ext.htmlEncode(e)}</b>: `+Ext.htmlEncode(t)}))),a},API2Request:function(e){let t=Ext.apply({waitMsg:gettext("Please wait...")},e),i=e.autoErrorAlert??("function"!=typeof e.failure&&"function"!=typeof e.callback),o=(t.url.match(/^\/api2/)||(t.url="/api2/extjs"+t.url),delete t.callback,e=>{(void 0===e.waitMsgTargetCount||--e.waitMsgTargetCount<=0)&&(e.setLoading(!1),delete e.waitMsgTargetCount)});var n,r,l;n=e.success,r=e.callback,l=e.failure,Ext.apply(t,{success:function(e,t){t.waitMsgTarget&&("touch"===Proxmox.Utils.toolkit?t.waitMsgTarget.setMasked(!1):o(t.waitMsgTarget));var a=Ext.decode(e.responseText);(e.result=a).success?(Ext.callback(r,t.scope,[t,!0,e]),Ext.callback(n,t.scope,[e,t])):(e.htmlStatus=Proxmox.Utils.extractRequestError(a,!0),Ext.callback(r,t.scope,[t,!1,e]),Ext.callback(l,t.scope,[e,t]),i&&Ext.Msg.alert(gettext("Error"),e.htmlStatus))},failure:function(e,t){t.waitMsgTarget&&("touch"===Proxmox.Utils.toolkit?t.waitMsgTarget.setMasked(!1):o(t.waitMsgTarget)),e.result={};try{e.result=Ext.decode(e.responseText)}catch(e){}let a=gettext("Connection error")+" - server offline?";e.aborted?a=gettext("Connection error")+" - aborted.":e.timedout?a=gettext("Connection error")+" - Timeout.":e.status&&e.statusText&&(a=gettext("Connection error")+" "+e.status+": "+e.statusText),e.htmlStatus=Ext.htmlEncode(a),Ext.callback(r,t.scope,[t,!1,e]),Ext.callback(l,t.scope,[e,t])}});let a=t.waitMsgTarget;a&&("touch"===Proxmox.Utils.toolkit?a.setMasked({xtype:"loadmask",message:t.waitMsg}):a.rendered?(a.waitMsgTargetCount=(a.waitMsgTargetCount??0)+1,a.setLoading(t.waitMsg)):(a.waitMsgTargetCount=(a.waitMsgTargetCount??0)+1,a.on("afterlayout",function(){0<(a.waitMsgTargetCount??0)&&a.setLoading(t.waitMsg)},a,{single:!0}))),Ext.Ajax.request(t)},alertResponseFailure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus||e.result.message),checked_command:function(a){Proxmox.Utils.API2Request({url:"/nodes/localhost/subscription",method:"GET",failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)},success:function(e,t){e=e.result;null!=e&&e&&"active"===e.data.status.toLowerCase()?a():Ext.Msg.show({title:gettext("No valid subscription"),icon:Ext.Msg.WARNING,message:Proxmox.Utils.getNoSubKeyHtml(e.data.url),buttons:Ext.Msg.OK,callback:function(e){"ok"===e&&a()}})}})},assemble_field_data:function(i,e){Ext.isObject(e)&&Ext.Object.each(e,function(t,a){if(Object.prototype.hasOwnProperty.call(i,t)){let e=i[t];Ext.isArray(e)||(e=i[t]=[e]),Ext.isArray(a)?i[t]=e.concat(a):e.push(a)}else i[t]=a})},updateColumnWidth:function(e,t){var a=Ext.state.Manager.get("summarycolumns")||"auto";let i;"auto"!==a?(i=parseInt(a,10),Number.isNaN(i)&&(i=1)):(t=(t||1400)+1,i=Math.ceil(e.getSize().width/t)),e.oldFactor!==i&&(a=e.query(">"),i=Math.min(i,a.length),e.oldFactor=i,a.forEach(e=>{e.columnWidth=1/i}),e.updateLayout(),e.updateLayout())},updateColumns:e=>Proxmox.Utils.updateColumnWidth(e),dialog_title:function(e,t,a){return t?a?gettext("Add")+": "+e:gettext("Create")+": "+e:gettext("Edit")+": "+e},network_iface_types:{eth:gettext("Network Device"),bridge:"Linux Bridge",bond:"Linux Bond",vlan:"Linux VLAN",OVSBridge:"OVS Bridge",OVSBond:"OVS Bond",OVSPort:"OVS Port",OVSIntPort:"OVS IntPort"},render_network_iface_type:function(e){return Proxmox.Utils.network_iface_types[e]||Proxmox.Utils.unknownText},notificationFieldName:{type:gettext("Notification type"),hostname:gettext("Hostname")},formatNotificationFieldName:e=>Proxmox.Utils.notificationFieldName[e]||e,overrideNotificationFieldName:function(e){for(var[t,a]of Object.entries(e))Proxmox.Utils.notificationFieldName[t]=a},notificationFieldValue:{"system-mail":gettext("Forwarded mails to the local root user")},formatNotificationFieldValue:e=>Proxmox.Utils.notificationFieldValue[e]||e,overrideNotificationFieldValue:function(e){for(var[t,a]of Object.entries(e))Proxmox.Utils.notificationFieldValue[t]=a},task_desc_table:{aptupdate:["",gettext("Update package database")],diskinit:["Disk",gettext("Initialize Disk with GPT")],spiceshell:["",gettext("Shell")+" (Spice)"],srvreload:["SRV",gettext("Reload")],srvrestart:["SRV",gettext("Restart")],srvstart:["SRV",gettext("Start")],srvstop:["SRV",gettext("Stop")],termproxy:["",gettext("Console")+" (xterm.js)"],vncshell:["",gettext("Shell")]},override_task_descriptions:function(e){for(var[t,a]of Object.entries(e))Proxmox.Utils.task_desc_table[t]=a},format_task_description:function(e,t){var a,i=Proxmox.Utils.task_desc_table[e];let o;return i?Ext.isFunction(i)?i(e,t):(a=i[0],o=i[1],a&&void 0!==t?a+" "+t+" - "+o:o):(o=e,t&&(e+=" "+t),o)},format_size:function(e,t){var a=[gettext("B"),gettext("KB"),gettext("MB"),gettext("GB"),gettext("TB"),gettext("PB"),gettext("EB"),gettext("ZB"),gettext("YB")],i=[gettext("B"),gettext("KiB"),gettext("MiB"),gettext("GiB"),gettext("TiB"),gettext("PiB"),gettext("EiB"),gettext("ZiB"),gettext("YiB")];let o=0,n=2;for(var r=t?1e3:1024;r<=e&&o<a.length;)e/=r,o++;t=(t?a:i)[o];return 0===o&&(n=0),e.toFixed(n)+" "+t},SizeUnits:{B:1,KiB:1024,MiB:1048576,GiB:1073741824,TiB:1099511627776,PiB:0x4000000000000,KB:1e3,MB:1e6,GB:1e9,TB:1e12,PB:1e15},parse_size_unit:function(e){var e=e.match(/(\d+(?:\.\d+)?)\s?([KMGTP]?)(i?)B?\s*$/i),t=parseFloat(e[1]),a=e[2].toUpperCase(),e=e[3].toLowerCase(),a=""+a+e+"B";return{size:t,factor:Proxmox.Utils.SizeUnits[a],unit:a,binary:e}},size_unit_to_bytes:function(e){var{size:e,factor:t}=Proxmox.Utils.parse_size_unit(e);return e*t},autoscale_size_unit:function(e){var{size:e,factor:t,binary:a}=Proxmox.Utils.parse_size_unit(e);return Proxmox.Utils.format_size(e*t,"i"!==a)},size_unit_ratios:function(e,t){return t=void 0!==t?t:1/0,("number"==typeof(e=void 0!==e?e:0)?e:Proxmox.Utils.size_unit_to_bytes(e))/(("number"==typeof t?t:Proxmox.Utils.size_unit_to_bytes(t))||1/0)},render_upid:function(e,t,a){var a=a.data,i=a.type||a.worker_type,a=a.id||a.worker_id;return Ext.htmlEncode(Proxmox.Utils.format_task_description(i,a))},render_uptime:function(e){return void 0===e?"":e<=0?"-":Proxmox.Utils.format_duration_long(e)},systemd_unescape:function(e){const t="0".charCodeAt(0),a="9".charCodeAt(0),i="A".charCodeAt(0),o="F".charCodeAt(0),n="a".charCodeAt(0),r="f".charCodeAt(0);function l(e){if(e>=t&&e<=a)return e-t;if(e>=i&&e<=o)return e-i+10;if(e>=n&&e<=r)return e-n+10;throw"got invalid hex digit"}var s="x".charCodeAt(0),d="-".charCodeAt(0),u="/".charCodeAt(0),c="\\".charCodeAt(0),x=(new TextEncoder).encode(e),m=new Uint8Array(x.length);let p=0,f=0;for(;p<x.length;){var g=x[p];if(g===d)m.set([u],f);else{if(p+4<x.length){var h=x[p+1];if(g===c&&h===s){var h=l(x[p+2]),b=l(x[p+3]);m.set([16*h+b],f),f+=1,p+=4;continue}}m.set([g],f)}f+=1,p+=1}return(new TextDecoder).decode(m.slice(0,m.len))},parse_task_upid:function(e){var t={},a=e.match(/^UPID:([^\s:]+):([0-9A-Fa-f]{8}):([0-9A-Fa-f]{8,9}):(([0-9A-Fa-f]{8,16}):)?([0-9A-Fa-f]{8}):([^:\s]+):([^:\s]*):([^:\s]+):$/);if(a)return t.node=a[1],t.pid=parseInt(a[2],16),t.pstart=parseInt(a[3],16),void 0!==a[5]&&(t.task_id=parseInt(a[5],16)),t.starttime=parseInt(a[6],16),t.type=a[7],t.id=Proxmox.Utils.systemd_unescape(a[8]),t.user=a[9],t.desc=Proxmox.Utils.format_task_description(t.type,t.id),t;throw"unable to parse upid '"+e+"'"},parse_task_status:function(e){return"OK"===e?"ok":"unknown"===e?"unknown":e.match(/^WARNINGS: (.*)$/)?"warning":"error"},format_task_status:function(e){switch(Proxmox.Utils.parse_task_status(e)){case"unknown":return Proxmox.Utils.unknownText;case"error":return Proxmox.Utils.errorText+": "+Ext.htmlEncode(e);case"warning":return e.replace("WARNINGS",Proxmox.Utils.warningsText);default:return e}},render_duration:function(e){return void 0===e?"-":Proxmox.Utils.format_duration_human(e)},render_timestamp:function(e,t,a,i,o,n){e=new Date(1e3*e);return Ext.Date.format(e,"Y-m-d H:i:s")},render_zfs_health:function(e){if(void 0===e)return"";var t="question-circle";switch(e){case"AVAIL":case"ONLINE":t="check-circle good";break;case"REMOVED":case"DEGRADED":t="exclamation-circle warning";break;case"UNAVAIL":case"FAULTED":case"OFFLINE":t="times-circle critical"}return'<i class="fa fa-'+t+'"></i> '+e},get_help_info:function(e){let t;if("undefined"!=typeof proxmoxOnlineHelpInfo)t=proxmoxOnlineHelpInfo;else{if("undefined"==typeof pveOnlineHelpInfo)throw"no global OnlineHelpInfo map declared";t=pveOnlineHelpInfo}var a;return t[e]||(a=e.replace(/_/g,"-"),t[a])||(a=e.replace(/-/g,"_"),t[a])},get_help_link:function(e){e=Proxmox.Utils.get_help_info(e);if(e)return window.location.origin+e.link},openXtermJsViewer:function(e,t,a,i,o){e=Ext.Object.toQueryString({console:e,xtermjs:1,vmid:t,vmname:i,node:a,cmd:o}),t=window.open("?"+e,"_blank","toolbar=no,location=no,status=no,menubar=no,resizable=yes,width=800,height=420");t&&t.focus()},render_optional_url:function(e){return e&&null!==e.match(/^https?:\/\//)?'<a target="_blank" href="'+e+'">'+e+"</a>":e},render_san:function(e){var t=[];return Ext.isArray(e)?(e.forEach(function(e){Ext.isNumber(e)||t.push(e)}),t.join("<br>")):e},render_usage:e=>(100*e).toFixed(2)+"%",render_cpu_usage:function(e,t){return Ext.String.format(gettext("{0}% of {1}")+" "+gettext("CPU(s)"),(100*e).toFixed(2),t)},render_size_usage:function(e,t,a){var i;return 0===t?gettext("N/A"):(i=e=>Proxmox.Utils.format_size(e,a),(100*e/t).toFixed(2)+"% ("+Ext.String.format(gettext("{0} of {1}"),i(e),i(t))+")")},render_cpu:function(e,t,a,i,o,n){var r;return!a.data.uptime||!Ext.isNumeric(e)||(a=a.data.maxcpu||1,!Ext.isNumeric(a))||a<1?"":(r=1<a?"CPUs":"CPU",`${(100*e).toFixed(1)}% of ${a.toString()} `+r)},render_size:function(e,t,a,i,o,n){return Ext.isNumeric(e)?Proxmox.Utils.format_size(e):""},render_cpu_model:function(e){var t=1<e.sockets?gettext("Sockets"):gettext("Socket");return`${e.cpus} x ${e.model} (${e.sockets.toString()} ${t})`},render_node_cpu_usage:function(e,t){return Proxmox.Utils.render_cpu_usage(e,t.cpus)},render_node_size_usage:function(e){return Proxmox.Utils.render_size_usage(e.used,e.total)},loadTextFromFile:function(e,t,a){e.size>(a||8192)?Ext.Msg.alert(gettext("Error"),gettext("Invalid file size: ")+e.size):((a=new FileReader).onload=e=>t(e.target.result),a.readAsText(e))},parsePropertyString:function(e,t){var a,i={};return"string"!=typeof e||""===e||(Ext.Array.each(e.split(","),function(e){e=e.split("=",2);if(Ext.isDefined(e[1]))i[e[0]]=e[1];else{if(!Ext.isDefined(t))return!(a="invalid propertyString, not a key=value pair and no defaultKey defined");if(Ext.isDefined(i[t]))return!(a="defaultKey may be only defined once in propertyString");i[t]=e[0]}return!0}),void 0===a)?i:void console.error(a)},printPropertyString:function(e,a){var i,o=[],n=!1;return Ext.Object.each(e,function(e,t){void 0!==a&&e===a?(n=!0,i=t):Ext.isArray(t)?o.push(e+"="+t.join(";")):""!==t&&o.push(e+"="+t)}),o=o.sort(),n&&o.unshift(i),o.join(",")},acmedomain_count:5,parseACMEPluginData:function(e){let i={},o=[];return e.split("\n").forEach(e=>{var[t,a]=e.split("=");void 0!==a?i[t]=a:o.push(e)}),[i,o]},delete_if_default:function(e,t,a,i){""!==e[t]&&e[t]!==a||(i||(e.delete?Ext.isArray(e.delete)?e.delete.push(t):e.delete+=","+t:e.delete=t),delete e[t])},printACME:function(e){return Ext.isArray(e.domains)&&(e.domains=e.domains.join(";")),Proxmox.Utils.printPropertyString(e)},parseACME:function(e){var a,i;return e?(a={},Ext.Array.each(e.split(","),function(e){var t=e.split("=",2);return Ext.isDefined(t[1])?(a[t[0]]=t[1],!0):(i="Failed to parse key-value pair: "+e,!1)}),void 0===i?(void 0!==a.domains&&(a.domains=a.domains.split(/;/)),a):void console.error(i)):{}},add_domain_to_acme:function(e,t){return void 0===e.domains?e.domains=[t]:(e.domains.push(t),e.domains=e.domains.filter((e,t,a)=>a.indexOf(e)===t)),e},remove_domain_from_acme:function(e,i){return void 0!==e.domains&&(e.domains=e.domains.filter((e,t,a)=>a.indexOf(e)===t&&e!==i)),e},get_health_icon:function(e,t){void 0===t&&(t=!1);var a="faded fa-question";switch(e=void 0===e?"uknown":e){case"good":a="good fa-check";break;case"upgrade":a="warning fa-upload";break;case"old":a="warning fa-refresh";break;case"warning":a="warning fa-exclamation";break;case"critical":a="critical fa-times"}return t&&(a+="-circle"),a},formatNodeRepoStatus:function(e,t){var a=(e,t)=>`<i class="fa fa-fw fa-lg fa-${t}"></i>`+e,i=Ext.String.format(gettext("{0} updates"),t),t=Ext.String.format(gettext("No {0} repository enabled!"),t);return"ok"===e?a(i,"check-circle good")+" "+a(gettext("Production-ready Enterprise repository enabled"),"check-circle good"):"no-sub"===e?a(gettext("Production-ready Enterprise repository enabled"),"check-circle good")+" "+a(gettext("Enterprise repository needs valid subscription"),"exclamation-circle warning"):"non-production"===e?a(i,"check-circle good")+" "+a(gettext("Non production-ready repository enabled!"),"exclamation-circle warning"):"no-repo"===e?a(t,"exclamation-circle critical"):Proxmox.Utils.unknownText},render_u2f_error:function(e){return"U2F Error: "+{1:gettext("Other Error"),2:gettext("Bad Request"),3:gettext("Configuration Unsupported"),4:gettext("Device Ineligible"),5:gettext("Timeout")}[e]||Proxmox.Utils.unknownText},bytes_to_base64url:function(e){return null===e?null:btoa(Array.from(new Uint8Array(e)).map(e=>String.fromCharCode(e)).join("")).replace(/\+/g,"-").replace(/\//g,"_").replace(/[=]/g,"")},base64url_to_bytes:function(e){return null===e?null:new Uint8Array(atob(e.replace(/-/g,"+").replace(/_/g,"/")).split("").map(e=>e.charCodeAt(0)))},utf8ToBase64:function(e){e=(new TextEncoder).encode(e),e=Array.from(e,e=>String.fromCodePoint(e)).join("");return btoa(e)},base64ToUtf8:function(e){e=atob(e),e=Uint8Array.from(e,e=>e.codePointAt(0));return(new TextDecoder).decode(e)},stringToRGB:function(t){let a=0;if(!t)return a;t+="prox";for(let e=0;e<t.length;e++)a=t.charCodeAt(e)+((a<<5)-a),a&=a;return[.7*(255&a)+255*(1-.7),.7*(a>>8&255)+255*(1-.7),.7*(a>>16&255)+255*(1-.7)]},rgbToCss:function(e){return`rgb(${e[0]}, ${e[1]}, ${e[2]})`},rgbToHex:function(e){return""+Math.round(e[0]).toString(16)+Math.round(e[1]).toString(16)+Math.round(e[2]).toString(16)},hexToRGB:function(e){if(e)return 7===e.length&&(e=e.slice(1)),[parseInt(e.slice(0,2),16),parseInt(e.slice(2,4),16),parseInt(e.slice(4,6),16)]},getTextContrastClass:function(e){var e=.2126729*(e[0]/255)**2.4+.7151522*(e[1]/255)**2.4+.072175*(e[2]/255)**2.4,t=(e=.022<e?e:e+(.022-e)**1.414)**.56-.046134502;return Math.abs(e**.65-1)>=Math.abs(t)?"light":"dark"},getTagElement:function(e,t){t=t?.[e]||Proxmox.Utils.stringToRGB(e);let a=`background-color: ${Proxmox.Utils.rgbToCss(t)};`,i;return`<span class="${i=3<t.length?(a+="color: "+Proxmox.Utils.rgbToCss([t[3],t[4],t[5]]),"proxmox-tag-dark"):"proxmox-tag-"+Proxmox.Utils.getTextContrastClass(t)}" style="${a}">${e}</span>`},downloadAsFile:function(e,t){var a=document.createElement("a");a.href=e,a.target="_blank",t&&(a.download=t),a.click()}},singleton:!0,constructor:function(){var e=this,t=(Ext.apply(e,e.utilities),"(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])"),t="(?:(?:"+t+"\\.){3}"+t+")",a="(?:[0-9a-fA-F]{1,4})",i="(?:(?:"+a+":"+a+")|"+t+")",o="([0-9]{1,2})",n="([0-9]{1,3})",i=(e.IP4_match=new RegExp("^(?:"+t+")$"),e.IP4_cidr_match=new RegExp("^(?:"+t+")/"+o+"$"),"(?:(?:(?:(?:"+a+":){6})"+i+")|(?:(?:::(?:"+a+":){5})"+i+")|(?:(?:(?:"+a+")?::(?:"+a+":){4})"+i+")|(?:(?:(?:(?:"+a+":){0,1}"+a+")?::(?:"+a+":){3})"+i+")|(?:(?:(?:(?:"+a+":){0,2}"+a+")?::(?:"+a+":){2})"+i+")|(?:(?:(?:(?:"+a+":){0,3}"+a+")?::(?:"+a+":){1})"+i+")|(?:(?:(?:(?:"+a+":){0,4}"+a+")?::)"+i+")|(?:(?:(?:(?:"+a+":){0,5}"+a+")?::)"+a+")|(?:(?:(?:(?:"+a+":){0,7}"+a+")?::)))"),a=(e.IP6_match=new RegExp("^(?:"+i+")$"),e.IP6_cidr_match=new RegExp("^(?:"+i+")/"+n+"$"),e.IP6_bracket_match=new RegExp("^\\[("+i+")\\]"),e.IP64_match=new RegExp("^(?:"+i+"|"+t+")$"),e.IP64_cidr_match=new RegExp("^(?:"+i+"/"+n+")|(?:"+t+"/"+o+")$"),"(?:(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9\\-]*[a-zA-Z0-9])?)\\.)*(?:[A-Za-z0-9](?:[A-Za-z0-9\\-]*[A-Za-z0-9])?))");e.DnsName_match=new RegExp("^"+a+"$"),e.DnsName_or_Wildcard_match=new RegExp("^(?:\\*\\.)?"+a+"$"),e.CpuSet_match=/^[0-9]+(?:-[0-9]+)?(?:,[0-9]+(?:-[0-9]+)?)*$/,e.HostPort_match=new RegExp("^("+t+"|"+a+")(?::(\\d+))?$"),e.HostPortBrackets_match=new RegExp("^\\[("+i+"|"+t+"|"+a+")\\](?::(\\d+))?$"),e.IP6_dotnotation_match=new RegExp("^("+i+")(?:\\.(\\d+))?$"),e.Vlan_match=/^vlan(\d+)/,e.VlanInterface_match=/(\w+)\.(\d+)/;e.httpUrlRegex=new RegExp("^https?://(?:(?:(?:(?:(?:(?:(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9\\-]*[a-zA-Z0-9])?)\\.)*(?:[A-Za-z0-9](?:[A-Za-z0-9\\-]*[A-Za-z0-9])?))\\.)*(?:(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9\\-]*[a-zA-Z0-9])?)\\.)*(?:[A-Za-z0-9](?:[A-Za-z0-9\\-]*[A-Za-z0-9])?)))|(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))|\\[(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){6})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:::(?:(?:[0-9a-fA-F]{1,4}):){5})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){4})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,1}(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){3})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,2}(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){2})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,3}(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){1})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,4}(?:[0-9a-fA-F]{1,4}))?::)(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,5}(?:[0-9a-fA-F]{1,4}))?::)(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,7}(?:[0-9a-fA-F]{1,4}))?::))))\\]))(?::(?:[0-9]{1,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?)|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){6})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:::(?:(?:[0-9a-fA-F]{1,4}):){5})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){4})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,1}(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){3})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,2}(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){2})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,3}(?:[0-9a-fA-F]{1,4}))?::(?:(?:[0-9a-fA-F]{1,4}):){1})(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,4}(?:[0-9a-fA-F]{1,4}))?::)(?:(?:(?:[0-9a-fA-F]{1,4}):(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])\\.){3}(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,5}(?:[0-9a-fA-F]{1,4}))?::)(?:[0-9a-fA-F]{1,4}))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4}):){0,7}(?:[0-9a-fA-F]{1,4}))?::))))(?:/[^\0-]*)?$"),e.safeIdRegex=/^(?:[A-Za-z0-9_][A-Za-z0-9._\\-]*)$/}}),Ext.define("Proxmox.Async",{singleton:!0,api2:function(e){return new Promise((t,a)=>{delete e.callback,e.success=e=>t(e),e.failure=e=>a(e),Proxmox.Utils.API2Request(e)})},sleep:function(a){return new Promise((e,t)=>setTimeout(e,a))}}),Ext.override(Ext.data.Store,{onProxyLoad:function(e){"touch"===Proxmox.Utils.toolkit||e.getProxy()===this.getProxy()?this.callParent(arguments):console.log("ignored outdated response: "+e.getRequest().getUrl())}}),Ext.define("Proxmox.Schema",{singleton:!0,authDomains:{pam:{name:"Linux PAM",ipanel:"pmxAuthSimplePanel",onlineHelp:"user-realms-pam",add:!1,edit:!0,pwchange:!0,sync:!1,useTypeInUrl:!1},openid:{name:gettext("OpenID Connect Server"),ipanel:"pmxAuthOpenIDPanel",add:!0,edit:!0,tfa:!1,pwchange:!1,sync:!1,iconCls:"pmx-itype-icon-openid-logo",useTypeInUrl:!0},ldap:{name:gettext("LDAP Server"),ipanel:"pmxAuthLDAPPanel",syncipanel:"pmxAuthLDAPSyncPanel",add:!0,edit:!0,tfa:!0,pwchange:!1,sync:!0,useTypeInUrl:!0},ad:{name:gettext("Active Directory Server"),ipanel:"pmxAuthADPanel",syncipanel:"pmxAuthADSyncPanel",add:!0,edit:!0,tfa:!0,pwchange:!1,sync:!0,useTypeInUrl:!0}},overrideAuthDomains:function(e){for(var[t,a]of Object.entries(e))Proxmox.Schema.authDomains[t]=a},notificationEndpointTypes:{sendmail:{name:"Sendmail",ipanel:"pmxSendmailEditPanel",iconCls:"fa-envelope-o",defaultMailAuthor:"Proxmox VE"},smtp:{name:"SMTP",ipanel:"pmxSmtpEditPanel",iconCls:"fa-envelope-o",defaultMailAuthor:"Proxmox VE"},gotify:{name:"Gotify",ipanel:"pmxGotifyEditPanel",iconCls:"fa-bell-o"},webhook:{name:"Webhook",ipanel:"pmxWebhookEditPanel",iconCls:"fa-bell-o"}},overrideEndpointTypes:function(e){for(var[t,a]of Object.entries(e))Proxmox.Schema.notificationEndpointTypes[t]=a},pxarFileTypes:{b:{icon:"cube",label:gettext("Block Device")},c:{icon:"tty",label:gettext("Character Device")},d:{icon:"folder-o",label:gettext("Directory")},f:{icon:"file-text-o",label:gettext("File")},h:{icon:"file-o",label:gettext("Hardlink")},l:{icon:"link",label:gettext("Softlink")},p:{icon:"exchange",label:gettext("Pipe/Fifo")},s:{icon:"plug",label:gettext("Socket")},v:{icon:"cube",label:gettext("Virtual")}}}),Ext.Ajax.disableCaching=!1,Ext.apply(Ext.form.field.VTypes,{IPAddress:function(e){return Proxmox.Utils.IP4_match.test(e)},IPAddressText:gettext("Example")+": ***********",IPAddressMask:/[\d.]/i,IPCIDRAddress:function(e){e=Proxmox.Utils.IP4_cidr_match.exec(e);return null!==e&&8<=e[1]&&e[1]<=32},IPCIDRAddressText:gettext("Example")+": ***********/24<br>"+gettext("Valid CIDR Range")+": 8-32",IPCIDRAddressMask:/[\d./]/i,IP6Address:function(e){return Proxmox.Utils.IP6_match.test(e)},IP6AddressText:gettext("Example")+": 2001:DB8::42",IP6AddressMask:/[A-Fa-f0-9:]/,IP6CIDRAddress:function(e){e=Proxmox.Utils.IP6_cidr_match.exec(e);return null!==e&&8<=e[1]&&e[1]<=128},IP6CIDRAddressText:gettext("Example")+": 2001:DB8::42/64<br>"+gettext("Valid CIDR Range")+": 8-128",IP6CIDRAddressMask:/[A-Fa-f0-9:/]/,IP6PrefixLength:function(e){return 0<=e&&e<=128},IP6PrefixLengthText:gettext("Example")+": X, where 0 <= X <= 128",IP6PrefixLengthMask:/[0-9]/,IP64Address:function(e){return Proxmox.Utils.IP64_match.test(e)},IP64AddressText:gettext("Example")+": *********** 2001:DB8::42",IP64AddressMask:/[A-Fa-f0-9.:]/,IP64CIDRAddress:function(e){e=Proxmox.Utils.IP64_cidr_match.exec(e);return null!==e&&(void 0!==e[1]?8<=e[1]&&e[1]<=128:void 0!==e[2]&&8<=e[2]&&e[2]<=32)},IP64CIDRAddressText:gettext("Example")+": ***********/24 2001:DB8::42/64",IP64CIDRAddressMask:/[A-Fa-f0-9.:/]/,MacAddress:function(e){return/^([a-fA-F0-9]{2}:){5}[a-fA-F0-9]{2}$/.test(e)},MacAddressMask:/[a-fA-F0-9:]/,MacAddressText:gettext("Example")+": 01:23:45:67:89:ab",MacPrefix:function(e){return/^[a-f0-9][02468ace](?::[a-f0-9]{2}){0,2}:?$/i.test(e)},MacPrefixMask:/[a-fA-F0-9:]/,MacPrefixText:gettext("Example")+": 02:8f - "+gettext("only unicast addresses are allowed"),BridgeName:function(e){return/^[a-zA-Z][a-zA-Z0-9_]{0,9}$/.test(e)},VlanName:function(e){return Proxmox.Utils.VlanInterface_match.test(e)||Proxmox.Utils.Vlan_match.test(e),!0},BridgeNameText:gettext("Format")+": alphanumeric string starting with a character",BondName:function(e){return/^bond\d{1,4}$/.test(e)},BondNameText:gettext("Format")+": bond<b>N</b>, where 0 <= <b>N</b> <= 9999",InterfaceName:function(e){return/^[a-z][a-z0-9_]{1,20}$/.test(e)},InterfaceNameText:gettext("Allowed characters")+": 'a-z', '0-9', '_'<br />"+gettext("Minimum characters")+": 2<br />"+gettext("Maximum characters")+": 21<br />"+gettext("Must start with")+": 'a-z'",StorageId:function(e){return/^[a-z][a-z0-9\-_.]*[a-z0-9]$/i.test(e)},StorageIdText:gettext("Allowed characters")+":  'A-Z', 'a-z', '0-9', '-', '_', '.'<br />"+gettext("Minimum characters")+": 2<br />"+gettext("Must start with")+": 'A-Z', 'a-z'<br />"+gettext("Must end with")+": 'A-Z', 'a-z', '0-9'<br />",ConfigId:function(e){return/^[a-z][a-z0-9_-]+$/i.test(e)},ConfigIdText:gettext("Allowed characters")+": 'A-Z', 'a-z', '0-9', '_'<br />"+gettext("Minimum characters")+": 2<br />"+gettext("Must start with")+": "+gettext("letter"),HttpProxy:function(e){return/^http:\/\/.*$/.test(e)},HttpProxyText:gettext("Example")+": http://username:password&#64;host:port/",CpuSet:function(e){return Proxmox.Utils.CpuSet_match.test(e)},CpuSetText:gettext("This is not a valid CpuSet"),DnsName:function(e){return Proxmox.Utils.DnsName_match.test(e)},DnsNameText:gettext("This is not a valid hostname"),DnsNameOrWildcard:function(e){return Proxmox.Utils.DnsName_or_Wildcard_match.test(e)},DnsNameOrWildcardText:gettext("This is not a valid hostname"),proxmoxMail:function(e){return/^[\w+-~]+(\.[\w+-~]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*$/.test(e)},proxmoxMailText:gettext("Example")+": <EMAIL>",DnsOrIp:function(e){return!(!Proxmox.Utils.DnsName_match.test(e)&&!Proxmox.Utils.IP64_match.test(e))},DnsOrIpText:gettext("Not a valid DNS name or IP address."),HostPort:function(e){return Proxmox.Utils.HostPort_match.test(e)||Proxmox.Utils.HostPortBrackets_match.test(e)||Proxmox.Utils.IP6_dotnotation_match.test(e)},HostPortText:gettext("Host/IP address or optional port is invalid"),HostList:function(e){var t=e.split(/[ ,;]+/);let a;for(a=0;a<t.length;a++)if(""!==t[a]&&!Proxmox.Utils.HostPort_match.test(t[a])&&!Proxmox.Utils.HostPortBrackets_match.test(t[a])&&!Proxmox.Utils.IP6_dotnotation_match.test(t[a]))return!1;return!0},HostListText:gettext("Not a valid list of hosts"),password:function(e,t){return!t.initialPassField||e===t.up("form").down(`[name=${t.initialPassField}]`).getValue()},passwordText:gettext("Passwords do not match"),email:function(e){return/^[\w+~-]+(\.[\w+~-]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*$/.test(e)}}),Ext.define("PVE.form.field.Number",{override:"Ext.form.field.Number",submitLocaleSeparator:!1}),Ext.define("PVE.draw.Container",{override:"Ext.draw.Container",defaultDownloadServerUrl:document.location.origin,applyDownloadServerUrl:function(e){return e=e||this.defaultDownloadServerUrl}}),Ext.define("Proxmox.UnderlayPool",{override:"Ext.dom.UnderlayPool",checkOut:function(){let e=this.cache,t=e.length,a;for(;t--;)e[t].destroyed&&e.splice(t,1);return(a=e.shift())||((a=Ext.Element.create(this.elementConfig)).setVisibilityMode(2),a.dom.setAttribute("data-sticky",!0)),a}}),Ext.define("Proxmox.form.ComboBox",{override:"Ext.form.field.ComboBox",reset:function(){var e=this,t=(e.callParent(),e.getValue());Ext.isArray(e.originalValue)&&Ext.isArray(t)&&!Ext.Array.equals(t,e.originalValue)&&(e.clearValue(),e.setValue(e.originalValue))},initComponent:function(){let t=this;t.callParent(),t.editable&&(Ext.override(t.triggers.picker,{onMouseDown:function(e){"touch"===e.pointerType||this.field.owns(Ext.Element.getActiveElement())||(t.skip_expand_on_focus=!0),this.callParent(arguments)}}),t.on("focus",function(e){e.isExpanded||e.skip_expand_on_focus||e.expand(),e.skip_expand_on_focus=!1}))}}),Ext.define(null,{override:"Ext.view.Table",jumpToFocus:!1,saveFocusState:function(){var a,i,o,n=this,r=n.dataSource,l=n.actionableMode,s=n.getNavigationModel(),d=l?n.actionPosition:s.getPosition(!0),e=Ext.fly(Ext.Element.getActiveElement()),t=d&&d.view===n&&Ext.fly(d.getCell(!0));return n.skipSaveFocusState||!t||!t.contains(e)||(d=d.clone(),e.suspendFocusEvents(),l&&t.dom!==e.dom?n.suspendActionableMode():(l=!1,s.setPosition()),e.resumeFocusEvents(),r.isExpandingOrCollapsing)?Ext.emptyFn:function(){var e,t;(r=n.dataSource).getCount()?(t=n.all,a=Math.min(Math.max(d.rowIdx,t.startIndex),t.endIndex),i=Math.min(d.colIdx,n.getVisibleColumnManager().getColumns().length-1),o=d.record,(d=new Ext.grid.CellContext(n).setPosition(o&&r.contains(o)&&!o.isCollapsedPlaceholder?o:a,i)).getCell(!0)&&(l?n.resumeActionableMode(d):(t=n.getScrollX(),e=n.getScrollY(),s.setPosition(d,null,null,null,!0),s.getPosition()||d.column.focus(),n.jumpToFocus||n.scrollTo(t,e)))):d.column.focus()}}}),Ext.define("Proxmox.form.field.Text",{override:"Ext.form.field.Text",setSubmitValue:function(e){this.submitValue=e}}),Ext.define(null,{override:"Ext.layout.container.boxOverflow.Scroller",wheelIncrement:1,getWheelDelta:function(e){return-e.getWheelDelta(e)},onOwnerRender:function(e){var t={isBoxOverflowScroller:!0,x:!1,y:!1,listeners:{scrollend:this.onScrollEnd,scope:this}};Ext.scrollbar.width()||Ext.platformTags.desktop?this.wheelListener=this.layout.innerCt.on("wheel",this.onMouseWheel,this,{destroyable:!0}):t[e.layout.horizontal?"x":"y"]=!0,e.setScrollable(t)}}),Ext.define("Proxmox.form.field.Spinner",{override:"Ext.form.field.Spinner",onRender:function(){var e=this;e.callParent(),e.mouseWheelEnabled&&(e.mun(e.bodyEl,"mousewheel",e.onMouseWheel,e),e.mon(e.bodyEl,"wheel",e.onMouseWheel,e))},onMouseWheel:function(e){var t;this.hasFocus&&(0<(t=e.getWheelDelta())?this.spinDown():t<0&&this.spinUp(),e.stopEvent(),this.onSpinEnd())}}),Ext.define("Proxmox.validIdReOverride",{override:"Ext.Component",validIdRe:/^[a-z_][a-z0-9\-_@]*$/i}),Ext.define("Proxmox.selection.CheckboxModel",{override:"Ext.selection.CheckboxModel",checkSelector:".x-grid-cell-row-checker",doDeselect:function(e,t){var a,i=this,o=i.selected,n=0;if(i.locked||!i.store)return!1;if("number"==typeof e){if(!(a=i.store.getAt(e)))return!1;e=[a]}else Ext.isArray(e)||(e=[e]);let r=!1;var l=function(){r=!0,a===i.selectionStart&&(i.selectionStart=null)},s=[],d=e.length;for(i.suspendChanges();n<d;n++)if(a=e[n],i.isSelected(a)&&(r=!1,i.onSelectChange(a,!1,t,l),r||s.push(a),i.destroyed))return!1;return 0<s.length&&e.remove(s),o.remove(e),i.lastSelected=o.last(),i.resumeChanges(),i.maybeFireSelectionChange(0<e.length&&!t),e.length},doMultiSelect:function(a,i,o){var n,r,l,s,d=this,u=d.selected,c=!1;if(!d.locked){if(r=(a=Ext.isArray(a)?a:[a]).length,!i&&0<u.getCount()){if(i=d.deselectDuringSelect(a,o),d.destroyed)return;if(i[0])return void d.maybeFireSelectionChange(0<i[1]&&!o);c=0<i[1]}let e,t=[];for(s=function(){u.getCount()||(d.selectionStart=l),e=!1,c=!0},n=0;n<r;n++)if(l=a[n],!d.isSelected(l)){if(e=!0,d.onSelectChange(l,!0,o,s),d.destroyed)return;e&&t.push(l)}0<t.length&&a.remove(t),u.add(a),d.lastSelected=l,d.maybeFireSelectionChange(c&&!o)}},deselectDuringSelect:function(t,e){var a=this,i=a.selected.getRange(),o=0,n=!1,i=(a.suspendChanges(),a.deselectingDuringSelect=!0,i.filter(e=>!Ext.Array.contains(t,e)));return 0<i.length&&((o=a.doDeselect(i,e))||(n=!0),a.destroyed)&&(n=!0,o=0),a.deselectingDuringSelect=!1,a.resumeChanges(),[n,o]}}),Ext.define("Proxmox.Component",{override:"Ext.Component",clearPropertiesOnDestroy:!1}),Ext.define("Proxmox.view.DragZone",{override:"Ext.view.DragZone",onItemMouseDown:function(e,t,a,i,o){"touch"!==o.pointerType&&this.onTriggerGesture(e,t,a,i,o)}}),Ext.define("Proxmox.dd.DragDropManager",{override:"Ext.dd.DragDropManager",stopEvent:function(e){this.stopPropagation&&e.stopPropagation(),this.preventDefault&&e.preventDefault()}}),Ext.define("Proxmox.Cookies",{override:"Ext.util.Cookies",set:function(e,t,a,i,o,n,r){var l=[];a&&l.push("expires="+a.toUTCString()),void 0===i?l.push("path=/"):i&&l.push("path="+i),o&&l.push("domain="+o),!0===n&&l.push("secure"),r&&["lax","none","strict"].includes(r.toLowerCase())&&l.push("samesite="+r),document.cookie=e+"="+escape(t)+"; "+l.join("; ")}}),Ext.onReady(function(){Ext.override(Ext.Msg,{alert:function(e,t,a,i){if(Ext.isString(e))return e={title:e,message:t,icon:this.ERROR,buttons:this.OK,fn:a,scope:i,minWidth:this.minWidth},this.show(e)}})}),Ext.define("Ext.ux.IFrame",{extend:"Ext.Component",alias:"widget.uxiframe",loadMask:"Loading...",src:"about:blank",renderTpl:['<iframe src="{src}" id="{id}-iframeEl" data-ref="iframeEl" name="{frameName}" width="100%" height="100%" frameborder="0" allowfullscreen="true"></iframe>'],childEls:["iframeEl"],initComponent:function(){this.callParent(),this.frameName=this.frameName||this.id+"-frame"},initEvents:function(){this.callParent(),this.iframeEl.on("load",this.onLoad,this)},initRenderData:function(){return Ext.apply(this.callParent(),{src:this.src,frameName:this.frameName})},getBody:function(){var e=this.getDoc();return e.body||e.documentElement},getDoc:function(){try{return this.getWin().document}catch(e){return null}},getWin:function(){var e=this.frameName;return Ext.isIE?this.iframeEl.dom.contentWindow:window.frames[e]},getFrame:function(){return this.iframeEl.dom},onLoad:function(){this.getDoc()?(this.el.unmask(),this.fireEvent("load",this)):this.src&&(this.el.unmask(),this.fireEvent("error",this))},load:function(e){var t=this,a=t.loadMask,i=t.getFrame();!1!==t.fireEvent("beforeload",t,e)&&(a&&t.el&&t.el.mask(a),i.src=t.src=e||t.src)}}),Ext.define("PMX.image.Logo",{extend:"Ext.Img",xtype:"proxmoxlogo",height:30,width:172,src:"/images/proxmox_logo.png",alt:"Proxmox",autoEl:{tag:"a",href:"https://www.proxmox.com",target:"_blank"},initComponent:function(){var e=this,t=void 0!==e.prefix?e.prefix:"/pve2";e.src=t+e.src,e.callParent()}}),Ext.define("Proxmox.Markdown",{alternateClassName:"Px.Markdown",singleton:!0,sanitizeHTML:function(e){if(!e)return e;let l=e=>e.match(/^\s*https?:/i),t;t=a=>{if(3!==a.nodeType)if(1!==a.nodeType||/^(script|style|form|select|option|optgroup|map|area|canvas|textarea|applet|font|iframe|audio|video|object|embed|svg)$/i.test(a.tagName))a.outerHTML=Ext.String.htmlEncode(a.outerHTML);else{for(let t=a.attributes.length;t--;){var i=a.attributes[t].name,o=a.attributes[t].value,n=a.tagName.toLowerCase();if(/^(class|id|name|href|src|alt|align|valign|disabled|checked|start|type|target)$/i.test(i))if("href"!==i&&"src"!==i||l(o))"target"===i&&"a"!==n&&a.attributes.removeNamedItem(i);else{let e=!1;try{var r=new URL(o,window.location.origin);e=l(r.protocol),"img"===n&&"data:"===r.protocol.toLowerCase()?e=!0:"a"===n&&(e="javascript:"!==r.protocol.toLowerCase()),e?a.attributes[t].value=r.href:a.attributes.removeNamedItem(i)}catch(e){a.attributes.removeNamedItem(i)}}else a.attributes.removeNamedItem(i)}for(let e=a.childNodes.length;e--;)t(a.childNodes[e])}};e=(new DOMParser).parseFromString("<!DOCTYPE html><html><body>"+e,"text/html");return e.normalize(),t(e.body),e.body.innerHTML},parse:function(e){e=marked.parse(e);return`<div class="pmx-md">${this.sanitizeHTML(e)}</div>`}}),Ext.define("Proxmox.Mixin.CBind",{extend:"Ext.Mixin",mixinConfig:{before:{initComponent:"cloneTemplates"}},cloneTemplates:function(){let i=this,l=("function"==typeof i.cbindData&&(i.cbindData=i.cbindData(i.initialConfig)),i.cbindData=i.cbindData||{},function(e){if(e in i.initialConfig)return i.initialConfig[e];var t;if(e in i.cbindData)return"function"==typeof(t=i.cbindData[e])?t(i.initialConfig):t;if(e in i)return i[e];throw"unable to get cbind data for '"+e+"'"}),s=function(o){var e,t=o.cbind;if(t)for(const r in t){let a,i;if(e=t[r],i=!1,"function"==typeof e)o[r]=e(l,r),i=!0;else if(a=/^\{(!)?([a-z_][a-z0-9_]*)\}$/i.exec(e)){let e=l(a[2]);a[1]&&(e=!e),o[r]=e,i=!0}else if(a=/^\{(!)?([a-z_][a-z0-9_]*(\.[a-z_][a-z0-9_]*)+)\}$/i.exec(e)){var n=a[2].split(".");let t=l(n.shift());n.forEach(function(e){if(!(e in t))throw"unable to get cbind data for '"+a[2]+"'";t=t[e]}),a[1]&&(t=!t),o[r]=t,i=!0}else o[r]=e.replace(/{([a-z_][a-z0-9_]*)\}/gi,(e,t)=>{t=l(t);return i=!0,t});if(!i)throw"unable to parse cbind template '"+e+"'"}};i.cbind&&s(i);let d,u=function(e){let t,a,i,o,n,r;for(r=e.length,i=!1,a=0;a<r;a++)if((o=e[a]).constructor===Object&&(o.xtype||o.cbind)){i=!0;break}if(!i)return e;for(t=[],a=0;a<r;a++)(o=e[a]).constructor===Object&&(o.xtype||o.cbind)?((n=d(o)).cbind&&s(n),t.push(n)):o.constructor===Array?(n=u(o),t.push(n)):t.push(o);return t};d=function(e){let t={},a,i,o;for(a in e)null==(i=e[a])?t[a]=i:i.constructor===Object&&(i.xtype||i.cbind)?((o=d(i)).cbind&&s(o),t[a]=o):i.constructor===Array?(o=u(i),t[a]=o):t[a]=i;return t};{let e,t,a;for(e in i)null!=(t=i[e])&&("object"==typeof t&&t.constructor===Object?(t.xtype||t.cbind)&&"config"!==e&&(i[e]=d(t)):t.constructor===Array&&(a=u(t),i[e]=a))}}}),Ext.define("Proxmox.data.reader.JsonObject",{extend:"Ext.data.reader.Json",alias:"reader.jsonobject",readArray:!1,responseType:void 0,rows:void 0,constructor:function(e){Ext.apply(this,e||{}),this.callParent([e])},getResponseData:function(t){var e=this;let o=[];try{let a=Ext.decode(t.responseText)[e.getRootProperty()];if(e.readArray){null===a&&(a=[]);let i={};Ext.Array.each(a,function(e){Ext.isDefined(e.key)&&(i[e.key]=e)}),e.rows?Ext.Object.each(e.rows,function(e,t){var a=i[e];Ext.isDefined(a)?(Ext.isDefined(a.value)||(a.value=t.defaultValue),o.push(a)):Ext.isDefined(t.defaultValue)?o.push({key:e,value:t.defaultValue}):t.required&&o.push({key:e,value:void 0})}):Ext.Array.each(a,function(e){Ext.isDefined(e.key)&&o.push(e)})}else null===a?a={}:Ext.isArray(a)&&(a=1===a.length?a[0]:{}),e.rows?Ext.Object.each(e.rows,function(e,t){Ext.isDefined(a[e])?o.push({key:e,value:a[e]}):Ext.isDefined(t.defaultValue)?o.push({key:e,value:t.defaultValue}):t.required&&o.push({key:e,value:void 0})}):Ext.Object.each(a,function(e,t){o.push({key:e,value:t})})}catch(e){Ext.Error.raise({response:t,json:t.responseText,parseError:e,msg:"Unable to parse the JSON returned by the server: "+e.toString()})}return o}}),Ext.define("Proxmox.RestProxy",{extend:"Ext.data.RestProxy",alias:"proxy.proxmox",pageParam:null,startParam:null,limitParam:null,groupParam:null,sortParam:null,filterParam:null,noCache:!1,afterRequest:function(e,t){this.fireEvent("afterload",this,e,t)},constructor:function(e){Ext.applyIf(e,{reader:{responseType:void 0,type:"json",rootProperty:e.root||"data"}}),this.callParent([e])}},function(){Ext.define("KeyValue",{extend:"Ext.data.Model",fields:["key","value"],idProperty:"key"}),Ext.define("KeyValuePendingDelete",{extend:"Ext.data.Model",fields:["key","value","pending","delete"],idProperty:"key"}),Ext.define("proxmox-tasks",{extend:"Ext.data.Model",fields:[{name:"starttime",type:"date",dateFormat:"timestamp"},{name:"endtime",type:"date",dateFormat:"timestamp"},{name:"pid",type:"int"},{name:"duration",sortType:"asInt",calculate:function(e){var t=e.endtime,e=e.starttime;return void 0!==t?(t-e)/1e3:0}},"node","upid","user","tokenid","status","type","id"],idProperty:"upid"}),Ext.define("proxmox-cluster-log",{extend:"Ext.data.Model",fields:[{name:"uid",type:"int"},{name:"time",type:"date",dateFormat:"timestamp"},{name:"pri",type:"int"},{name:"pid",type:"int"},"node","user","tag","msg",{name:"id",convert:function(e,t){t=t.data;return e||t.uid+":"+t.node}}],idProperty:"id"})}),Ext.define("Proxmox.data.UpdateStore",{extend:"Ext.data.Store",alias:"store.update",config:{interval:3e3,isStopped:!0,autoStart:!1},destroy:function(){this.stopUpdate(),this.callParent()},constructor:function(e){let a=this,i=(void 0===(e=e||{}).interval&&delete e.interval,new Ext.util.DelayedTask),o=function(){if(!a.getIsStopped())if(Proxmox.Utils.authOK()){let t=new Date;a.load(function(){var e=new Date-t,e=a.getInterval()+2*e;i.delay(e,o)})}else i.delay(200,o)};Ext.apply(e,{startUpdate:function(){a.setIsStopped(!1),i.delay(1,o)},stopUpdate:function(){a.setIsStopped(!0),i.cancel()}}),a.callParent([e]),a.load_task=i,a.getAutoStart()&&a.startUpdate()}}),Ext.define("Proxmox.data.DiffStore",{extend:"Ext.data.Store",alias:"store.diff",sortAfterUpdate:!1,autoDestroyRstore:!1,doDestroy:function(){var e=this;e.autoDestroyRstore&&(Ext.isFunction(e.rstore.destroy)&&e.rstore.destroy(),delete e.rstore),e.callParent()},constructor:function(e){let o=this;if(!(e=e||{}).rstore)throw"no rstore specified";if(!e.rstore.model)throw"no rstore model specified";let t;if(e.rstore.isInstance)t=e.rstore;else{if(!e.rstore.type)throw'rstore is not an instance, and cannot autocreate without "type"';Ext.applyIf(e.rstore,{autoDestroyRstore:!0}),t=Ext.create("store."+e.rstore.type,e.rstore)}Ext.apply(e,{model:t.model,proxy:{type:"memory"}}),o.callParent([e]),o.rstore=t;let n=!0,i=function(t,e){let a=o.getById(e);var i;a?(a.beginEdit(),Ext.Array.each(o.model.prototype.fields,function(e){a.data[e.name]!==t[e.name]&&a.set(e.name,t[e.name])}),a.endEdit(!0),a.commit()):(e=Ext.create(o.model,t),i=o.appendAtStart&&!n?0:o.data.length,o.insert(i,e))};function a(e,t,a){a&&(o.suspendEvents(),(o.getData().getSource()||o.getData()).each(function(e){o.rstore.getById(e.getId())||o.remove(e)}),o.rstore.each(function(e){i(e.data,e.getId())}),o.filter(),o.sortAfterUpdate&&o.sort(),n=!1,o.resumeEvents(),o.fireEvent("refresh",o),o.fireEvent("datachanged",o))}o.rstore.isLoaded()&&a(o.rstore,0,!0),o.mon(o.rstore,"load",a)}}),Ext.define("Proxmox.data.ObjectStore",{extend:"Proxmox.data.UpdateStore",getRecord:function(){let t=Ext.create("Ext.data.Model");return this.getData().each(function(e){t.set(e.data.key,e.data.value)}),t.commit(!0),t},constructor:function(e){e=e||{},Ext.applyIf(e,{model:"KeyValue",proxy:{type:"proxmox",url:e.url,extraParams:e.extraParams,reader:{type:"jsonobject",rows:e.rows,readArray:e.readArray,rootProperty:e.root||"data"}}}),this.callParent([e])}}),Ext.define("Proxmox.data.RRDStore",{extend:"Proxmox.data.UpdateStore",alias:"store.proxmoxRRDStore",setRRDUrl:function(e,t){e=e||this.timeframe,t=t||this.cf,this.proxy.url=this.rrdurl+"?timeframe="+e+"&cf="+t},proxy:{type:"proxmox"},timeframe:"hour",cf:"AVERAGE",constructor:function(e){let i=this;if((e=e||{}).interval||(e.interval=3e4),!e.rrdurl)throw"no rrdurl specified";let o="proxmoxRRDTypeSelection";var t=Ext.state.Manager.getProvider(),a=t.get(o);!a||a.timeframe===i.timeframe&&a.cf===i.rrdcffn||(i.timeframe=a.timeframe,i.rrdcffn=a.cf),i.callParent([e]),i.setRRDUrl(),i.mon(t,"statechange",function(e,t,a){t===o&&a&&a.id&&(a.timeframe===i.timeframe&&a.cf===i.cf||(i.timeframe=a.timeframe,i.cf=a.cf,i.setRRDUrl(),i.reload()))})}}),Ext.define("Timezone",{extend:"Ext.data.Model",fields:["zone"]}),Ext.define("Proxmox.data.TimezoneStore",{extend:"Ext.data.Store",model:"Timezone",data:[["Africa/Abidjan"],["Africa/Accra"],["Africa/Addis_Ababa"],["Africa/Algiers"],["Africa/Asmara"],["Africa/Bamako"],["Africa/Bangui"],["Africa/Banjul"],["Africa/Bissau"],["Africa/Blantyre"],["Africa/Brazzaville"],["Africa/Bujumbura"],["Africa/Cairo"],["Africa/Casablanca"],["Africa/Ceuta"],["Africa/Conakry"],["Africa/Dakar"],["Africa/Dar_es_Salaam"],["Africa/Djibouti"],["Africa/Douala"],["Africa/El_Aaiun"],["Africa/Freetown"],["Africa/Gaborone"],["Africa/Harare"],["Africa/Johannesburg"],["Africa/Kampala"],["Africa/Khartoum"],["Africa/Kigali"],["Africa/Kinshasa"],["Africa/Lagos"],["Africa/Libreville"],["Africa/Lome"],["Africa/Luanda"],["Africa/Lubumbashi"],["Africa/Lusaka"],["Africa/Malabo"],["Africa/Maputo"],["Africa/Maseru"],["Africa/Mbabane"],["Africa/Mogadishu"],["Africa/Monrovia"],["Africa/Nairobi"],["Africa/Ndjamena"],["Africa/Niamey"],["Africa/Nouakchott"],["Africa/Ouagadougou"],["Africa/Porto-Novo"],["Africa/Sao_Tome"],["Africa/Tripoli"],["Africa/Tunis"],["Africa/Windhoek"],["America/Adak"],["America/Anchorage"],["America/Anguilla"],["America/Antigua"],["America/Araguaina"],["America/Argentina/Buenos_Aires"],["America/Argentina/Catamarca"],["America/Argentina/Cordoba"],["America/Argentina/Jujuy"],["America/Argentina/La_Rioja"],["America/Argentina/Mendoza"],["America/Argentina/Rio_Gallegos"],["America/Argentina/Salta"],["America/Argentina/San_Juan"],["America/Argentina/San_Luis"],["America/Argentina/Tucuman"],["America/Argentina/Ushuaia"],["America/Aruba"],["America/Asuncion"],["America/Atikokan"],["America/Bahia"],["America/Bahia_Banderas"],["America/Barbados"],["America/Belem"],["America/Belize"],["America/Blanc-Sablon"],["America/Boa_Vista"],["America/Bogota"],["America/Boise"],["America/Cambridge_Bay"],["America/Campo_Grande"],["America/Cancun"],["America/Caracas"],["America/Cayenne"],["America/Cayman"],["America/Chicago"],["America/Chihuahua"],["America/Costa_Rica"],["America/Cuiaba"],["America/Curacao"],["America/Danmarkshavn"],["America/Dawson"],["America/Dawson_Creek"],["America/Denver"],["America/Detroit"],["America/Dominica"],["America/Edmonton"],["America/Eirunepe"],["America/El_Salvador"],["America/Fortaleza"],["America/Glace_Bay"],["America/Godthab"],["America/Goose_Bay"],["America/Grand_Turk"],["America/Grenada"],["America/Guadeloupe"],["America/Guatemala"],["America/Guayaquil"],["America/Guyana"],["America/Halifax"],["America/Havana"],["America/Hermosillo"],["America/Indiana/Indianapolis"],["America/Indiana/Knox"],["America/Indiana/Marengo"],["America/Indiana/Petersburg"],["America/Indiana/Tell_City"],["America/Indiana/Vevay"],["America/Indiana/Vincennes"],["America/Indiana/Winamac"],["America/Inuvik"],["America/Iqaluit"],["America/Jamaica"],["America/Juneau"],["America/Kentucky/Louisville"],["America/Kentucky/Monticello"],["America/La_Paz"],["America/Lima"],["America/Los_Angeles"],["America/Maceio"],["America/Managua"],["America/Manaus"],["America/Marigot"],["America/Martinique"],["America/Matamoros"],["America/Mazatlan"],["America/Menominee"],["America/Merida"],["America/Mexico_City"],["America/Miquelon"],["America/Moncton"],["America/Monterrey"],["America/Montevideo"],["America/Montreal"],["America/Montserrat"],["America/Nassau"],["America/New_York"],["America/Nipigon"],["America/Nome"],["America/Noronha"],["America/North_Dakota/Center"],["America/North_Dakota/New_Salem"],["America/Ojinaga"],["America/Panama"],["America/Pangnirtung"],["America/Paramaribo"],["America/Phoenix"],["America/Port-au-Prince"],["America/Port_of_Spain"],["America/Porto_Velho"],["America/Puerto_Rico"],["America/Rainy_River"],["America/Rankin_Inlet"],["America/Recife"],["America/Regina"],["America/Resolute"],["America/Rio_Branco"],["America/Santa_Isabel"],["America/Santarem"],["America/Santiago"],["America/Santo_Domingo"],["America/Sao_Paulo"],["America/Scoresbysund"],["America/Shiprock"],["America/St_Barthelemy"],["America/St_Johns"],["America/St_Kitts"],["America/St_Lucia"],["America/St_Thomas"],["America/St_Vincent"],["America/Swift_Current"],["America/Tegucigalpa"],["America/Thule"],["America/Thunder_Bay"],["America/Tijuana"],["America/Toronto"],["America/Tortola"],["America/Vancouver"],["America/Whitehorse"],["America/Winnipeg"],["America/Yakutat"],["America/Yellowknife"],["Antarctica/Casey"],["Antarctica/Davis"],["Antarctica/DumontDUrville"],["Antarctica/Macquarie"],["Antarctica/Mawson"],["Antarctica/McMurdo"],["Antarctica/Palmer"],["Antarctica/Rothera"],["Antarctica/South_Pole"],["Antarctica/Syowa"],["Antarctica/Vostok"],["Arctic/Longyearbyen"],["Asia/Aden"],["Asia/Almaty"],["Asia/Amman"],["Asia/Anadyr"],["Asia/Aqtau"],["Asia/Aqtobe"],["Asia/Ashgabat"],["Asia/Baghdad"],["Asia/Bahrain"],["Asia/Baku"],["Asia/Bangkok"],["Asia/Beirut"],["Asia/Bishkek"],["Asia/Brunei"],["Asia/Choibalsan"],["Asia/Chongqing"],["Asia/Colombo"],["Asia/Damascus"],["Asia/Dhaka"],["Asia/Dili"],["Asia/Dubai"],["Asia/Dushanbe"],["Asia/Gaza"],["Asia/Harbin"],["Asia/Ho_Chi_Minh"],["Asia/Hong_Kong"],["Asia/Hovd"],["Asia/Irkutsk"],["Asia/Jakarta"],["Asia/Jayapura"],["Asia/Jerusalem"],["Asia/Kabul"],["Asia/Kamchatka"],["Asia/Karachi"],["Asia/Kashgar"],["Asia/Kathmandu"],["Asia/Kolkata"],["Asia/Krasnoyarsk"],["Asia/Kuala_Lumpur"],["Asia/Kuching"],["Asia/Kuwait"],["Asia/Macau"],["Asia/Magadan"],["Asia/Makassar"],["Asia/Manila"],["Asia/Muscat"],["Asia/Nicosia"],["Asia/Novokuznetsk"],["Asia/Novosibirsk"],["Asia/Omsk"],["Asia/Oral"],["Asia/Phnom_Penh"],["Asia/Pontianak"],["Asia/Pyongyang"],["Asia/Qatar"],["Asia/Qyzylorda"],["Asia/Rangoon"],["Asia/Riyadh"],["Asia/Sakhalin"],["Asia/Samarkand"],["Asia/Seoul"],["Asia/Shanghai"],["Asia/Singapore"],["Asia/Taipei"],["Asia/Tashkent"],["Asia/Tbilisi"],["Asia/Tehran"],["Asia/Thimphu"],["Asia/Tokyo"],["Asia/Ulaanbaatar"],["Asia/Urumqi"],["Asia/Vientiane"],["Asia/Vladivostok"],["Asia/Yakutsk"],["Asia/Yekaterinburg"],["Asia/Yerevan"],["Atlantic/Azores"],["Atlantic/Bermuda"],["Atlantic/Canary"],["Atlantic/Cape_Verde"],["Atlantic/Faroe"],["Atlantic/Madeira"],["Atlantic/Reykjavik"],["Atlantic/South_Georgia"],["Atlantic/St_Helena"],["Atlantic/Stanley"],["Australia/Adelaide"],["Australia/Brisbane"],["Australia/Broken_Hill"],["Australia/Currie"],["Australia/Darwin"],["Australia/Eucla"],["Australia/Hobart"],["Australia/Lindeman"],["Australia/Lord_Howe"],["Australia/Melbourne"],["Australia/Perth"],["Australia/Sydney"],["Europe/Amsterdam"],["Europe/Andorra"],["Europe/Athens"],["Europe/Belgrade"],["Europe/Berlin"],["Europe/Bratislava"],["Europe/Brussels"],["Europe/Bucharest"],["Europe/Budapest"],["Europe/Chisinau"],["Europe/Copenhagen"],["Europe/Dublin"],["Europe/Gibraltar"],["Europe/Guernsey"],["Europe/Helsinki"],["Europe/Isle_of_Man"],["Europe/Istanbul"],["Europe/Jersey"],["Europe/Kaliningrad"],["Europe/Kiev"],["Europe/Lisbon"],["Europe/Ljubljana"],["Europe/London"],["Europe/Luxembourg"],["Europe/Madrid"],["Europe/Malta"],["Europe/Mariehamn"],["Europe/Minsk"],["Europe/Monaco"],["Europe/Moscow"],["Europe/Oslo"],["Europe/Paris"],["Europe/Podgorica"],["Europe/Prague"],["Europe/Riga"],["Europe/Rome"],["Europe/Samara"],["Europe/San_Marino"],["Europe/Sarajevo"],["Europe/Simferopol"],["Europe/Skopje"],["Europe/Sofia"],["Europe/Stockholm"],["Europe/Tallinn"],["Europe/Tirane"],["Europe/Uzhgorod"],["Europe/Vaduz"],["Europe/Vatican"],["Europe/Vienna"],["Europe/Vilnius"],["Europe/Volgograd"],["Europe/Warsaw"],["Europe/Zagreb"],["Europe/Zaporozhye"],["Europe/Zurich"],["Indian/Antananarivo"],["Indian/Chagos"],["Indian/Christmas"],["Indian/Cocos"],["Indian/Comoro"],["Indian/Kerguelen"],["Indian/Mahe"],["Indian/Maldives"],["Indian/Mauritius"],["Indian/Mayotte"],["Indian/Reunion"],["Pacific/Apia"],["Pacific/Auckland"],["Pacific/Chatham"],["Pacific/Chuuk"],["Pacific/Easter"],["Pacific/Efate"],["Pacific/Enderbury"],["Pacific/Fakaofo"],["Pacific/Fiji"],["Pacific/Funafuti"],["Pacific/Galapagos"],["Pacific/Gambier"],["Pacific/Guadalcanal"],["Pacific/Guam"],["Pacific/Honolulu"],["Pacific/Johnston"],["Pacific/Kiritimati"],["Pacific/Kosrae"],["Pacific/Kwajalein"],["Pacific/Majuro"],["Pacific/Marquesas"],["Pacific/Midway"],["Pacific/Nauru"],["Pacific/Niue"],["Pacific/Norfolk"],["Pacific/Noumea"],["Pacific/Pago_Pago"],["Pacific/Palau"],["Pacific/Pitcairn"],["Pacific/Pohnpei"],["Pacific/Port_Moresby"],["Pacific/Rarotonga"],["Pacific/Saipan"],["Pacific/Tahiti"],["Pacific/Tarawa"],["Pacific/Tongatapu"],["Pacific/Wake"],["Pacific/Wallis"],["UTC"]]}),Ext.define("proxmox-notification-endpoints",{extend:"Ext.data.Model",fields:["name","type","comment","disable","origin"],proxy:{type:"proxmox"},idProperty:"name"}),Ext.define("proxmox-notification-matchers",{extend:"Ext.data.Model",fields:["name","comment","disable","origin"],proxy:{type:"proxmox"},idProperty:"name"}),Ext.define("proxmox-notification-fields",{extend:"Ext.data.Model",fields:["name","description"],idProperty:"name"}),Ext.define("proxmox-notification-field-values",{extend:"Ext.data.Model",fields:["value","comment","field"],idProperty:"value"}),Ext.define("pmx-domains",{extend:"Ext.data.Model",fields:["realm","type","comment","default",{name:"tfa",allowNull:!0},{name:"descr",convert:function(e,{data:t={}}){if(e)return Ext.String.htmlEncode(e);let a=t.comment||t.realm;return t.tfa&&(a+=` (+ ${t.tfa})`),Ext.String.htmlEncode(a)}}],idProperty:"realm",proxy:{type:"proxmox",url:"/api2/json/access/domains"}}),Ext.define("proxmox-certificate",{extend:"Ext.data.Model",fields:["filename","fingerprint","issuer","notafter","notbefore","subject","san","public-key-bits","public-key-type"],idProperty:"filename"}),Ext.define("proxmox-acme-accounts",{extend:"Ext.data.Model",fields:["name"],proxy:{type:"proxmox"},idProperty:"name"}),Ext.define("proxmox-acme-challenges",{extend:"Ext.data.Model",fields:["id","type","schema"],proxy:{type:"proxmox"},idProperty:"id"}),Ext.define("proxmox-acme-plugins",{extend:"Ext.data.Model",fields:["type","plugin","api"],proxy:{type:"proxmox"},idProperty:"plugin"}),Ext.define("Proxmox.form.SizeField",{extend:"Ext.form.FieldContainer",alias:"widget.pmxSizeField",mixins:["Proxmox.Mixin.CBind"],viewModel:{data:{unit:"MiB",unitPostfix:""},formulas:{unitlabel:e=>e("unit")+e("unitPostfix")}},emptyText:"",layout:"hbox",defaults:{hideLabel:!0},unit:"MiB",unitPostfix:"",backendUnit:void 0,submitAutoScaledSizeUnit:!1,allowZero:!1,emptyValue:null,items:[{xtype:"numberfield",cbind:{name:"{name}",emptyText:"{emptyText}",allowZero:"{allowZero}",emptyValue:"{emptyValue}"},minValue:0,step:1,submitLocaleSeparator:!1,fieldStyle:"text-align: right",flex:1,enableKeyEvents:!0,setValue:function(e){var t,a;return this._transformed||(a=(t=this.up("fieldcontainer")).getViewModel().get("unit"),"string"==typeof e&&(e=Proxmox.Utils.size_unit_to_bytes(e)),e=(e/=Proxmox.Utils.SizeUnits[a])*t.backendFactor,this._transformed=!0),0!==Number(e)||this.allowZero||(e=void 0),Ext.form.field.Text.prototype.setValue.call(this,e)},getSubmitValue:function(){let e=this.processRawValue(this.getRawValue());var t,a;return void 0===(e=e.replace(this.decimalSeparator,"."))||""===e?this.emptyValue:0===Number(e)?this.allowZero?0:null:(a=(t=this.up("fieldcontainer")).getViewModel().get("unit"),e=parseFloat(e)*Proxmox.Utils.SizeUnits[a],t.submitAutoScaledSizeUnit?Proxmox.Utils.format_size(e,!a.endsWith("iB")):String(Math.floor(e/t.backendFactor)))},listeners:{keydown:function(){this._transformed=!0}}},{xtype:"displayfield",name:"unit",submitValue:!1,padding:"0 0 0 10",bind:{value:"{unitlabel}"},listeners:{change:(e,t)=>{e.originalValue=t}},width:40}],initComponent:function(){var e=this;if(e.unit=e.unit||"MiB",!(e.unit in Proxmox.Utils.SizeUnits))throw"unknown unit: "+e.unit;if(e.backendFactor=1,void 0!==e.backendUnit){if(!(e.unit in Proxmox.Utils.SizeUnits))throw"unknown backend unit: "+e.backendUnit;e.backendFactor=Proxmox.Utils.SizeUnits[e.backendUnit]}e.callParent(arguments),e.getViewModel().set("unit",e.unit),e.getViewModel().set("unitPostfix",e.unitPostfix)}}),Ext.define("Proxmox.form.BandwidthField",{extend:"Proxmox.form.SizeField",alias:"widget.pmxBandwidthField",unitPostfix:"/s"}),Ext.define("Proxmox.form.field.DisplayEdit",{extend:"Ext.form.FieldContainer",alias:"widget.pmxDisplayEditField",viewModel:{parent:null,data:{editable:!1,value:void 0}},displayType:"displayfield",editConfig:{},editable:!1,setEditable:function(e){var t=this.getViewModel();this.editable=e,t.set("editable",e)},getEditable:function(){return this.getViewModel().get("editable")},setValue:function(e){var t=this.getViewModel();this.value=e,t.set("value",e)},getValue:function(){this.getViewModel().get("value")},setEmptyText:function(e){this.editField.setEmptyText(e)},getEmptyText:function(){return this.editField.getEmptyText()},layout:"fit",defaults:{hideLabel:!0},initComponent:function(){var e=this,t={xtype:e.displayType,bind:{}},a=(Ext.applyIf(t,e.initialConfig),delete t.editConfig,delete t.editable,Ext.apply({},e.editConfig));Ext.applyIf(a,{xtype:"textfield",bind:{}}),Ext.applyIf(a,t),e.initialConfig&&e.initialConfig.displayConfig&&(Ext.applyIf(t,e.initialConfig.displayConfig),delete t.displayConfig),Ext.applyIf(t,{renderer:e=>Ext.htmlEncode(e)}),Ext.applyIf(t.bind,{hidden:"{editable}",disabled:"{editable}",value:"{value}"}),Ext.applyIf(a.bind,{hidden:"{!editable}",disabled:"{!editable}",value:"{value}"}),a.disabled=a.hidden=!e.editable,t.disabled=t.hidden=!!e.editable,a.name=t.name=e.name,Ext.apply(e,{items:[a,t]}),e.callParent(),e.editField=e.down(a.xtype),e.displayField=e.down(t.xtype),e.getViewModel().set("editable",e.editable)}}),Ext.define("Proxmox.form.field.ExpireDate",{extend:"Ext.form.field.Date",alias:["widget.pmxExpireDate"],name:"expire",fieldLabel:gettext("Expire"),emptyText:"never",format:"Y-m-d",submitFormat:"U",getSubmitValue:function(){let e=this.callParent();return e=e||0},setValue:function(e){Ext.isDefined(e)&&(e?Ext.isDate(e)||(e=new Date(1e3*e)):e=null),this.callParent([e])}}),Ext.define("Proxmox.form.field.Integer",{extend:"Ext.form.field.Number",alias:"widget.proxmoxintegerfield",config:{deleteEmpty:!1},allowDecimals:!1,allowExponential:!1,step:1,getSubmitData:function(){var e,t=this;let a=null;return t.disabled||!t.submitValue||t.isFileUpload()||(null!=(e=t.getSubmitValue())&&""!==e?(a={})[t.getName()]=e:t.getDeleteEmpty()&&((a={}).delete=t.getName())),a}}),Ext.define("Proxmox.form.field.Textfield",{extend:"Ext.form.field.Text",alias:["widget.proxmoxtextfield"],config:{skipEmptyText:!0,deleteEmpty:!1,trimValue:!1},getSubmitData:function(){let e=this,t=null,a;return e.disabled||!e.submitValue||e.isFileUpload()||(null!==(a=e.getSubmitValue())?(t={})[e.getName()]=a:e.getDeleteEmpty()&&((t={}).delete=e.getName())),t},getSubmitValue:function(){let e=this.processRawValue(this.getRawValue());return""===(e=this.getTrimValue()&&"string"==typeof e?e.trim():e)&&this.getSkipEmptyText()?null:e},setAllowBlank:function(e){this.allowBlank=e,this.validate()}}),Ext.define("Proxmox.form.field.Base64TextArea",{extend:"Ext.form.field.TextArea",alias:["widget.proxmoxBase64TextArea"],config:{skipEmptyText:!1,deleteEmpty:!1,trimValue:!1,editable:!0,width:600,height:400,scrollable:"y",emptyText:gettext("You can use Markdown for rich text formatting.")},setValue:function(e){this.callParent([Proxmox.Utils.base64ToUtf8(e)])},processRawValue:function(e){return Proxmox.Utils.utf8ToBase64(e)},getSubmitData:function(){let e=this,t=null,a;return e.disabled||!e.submitValue||e.isFileUpload()||(null!==(a=e.getSubmitValue())?(t={})[e.getName()]=a:e.getDeleteEmpty()&&((t={}).delete=e.getName())),t},getSubmitValue:function(){let e=this.processRawValue(this.getRawValue());return""===(e=this.getTrimValue()&&"string"==typeof e?e.trim():e)&&this.getSkipEmptyText()?null:e},setAllowBlank:function(e){this.allowBlank=e,this.validate()}}),Ext.define("Proxmox.form.field.VlanField",{extend:"Ext.form.field.Number",alias:["widget.proxmoxvlanfield"],deleteEmpty:!1,emptyText:gettext("no VLAN"),fieldLabel:gettext("VLAN Tag"),allowBlank:!0,getSubmitData:function(){var e,t=this,a=null;return!t.disabled&&t.submitValue&&((e=t.getSubmitValue())?(a={})[t.getName()]=e:t.deleteEmpty&&((a={}).delete=t.getName())),a},initComponent:function(){Ext.apply(this,{minValue:1,maxValue:4094}),this.callParent()}}),Ext.define("Proxmox.DateTimeField",{extend:"Ext.form.FieldContainer",alias:["widget.promxoxDateTimeField"],xtype:"proxmoxDateTimeField",layout:"hbox",viewModel:{data:{datetime:null,minDatetime:null,maxDatetime:null},formulas:{date:{get:function(e){return e("datetime")},set:function(e){var t;e?((t=new Date(this.get("datetime"))).setFullYear(e.getFullYear(),e.getMonth(),e.getDate()),this.set("datetime",t)):this.set("datetime",null)}},time:{get:function(e){return e("datetime")},set:function(e){var t;e?((t=new Date(this.get("datetime"))).setHours(e.getHours()),t.setMinutes(e.getMinutes()),t.setSeconds(e.getSeconds()),t.setMilliseconds(e.getMilliseconds()),this.set("datetime",t)):this.set("datetime",null)}},minDate:{get:function(e){e=e("minDatetime");return e?new Date(e):null}},maxDate:{get:function(e){e=e("maxDatetime");return e?new Date(e):null}},minTime:{get:function(e){var t=e("datetime"),e=e("minDatetime");return e&&t&&!this.isSameDay(t,e)?new Date(e).setHours("00","00","00","000"):e}},maxTime:{get:function(e){var t=e("datetime"),e=e("maxDatetime");return e&&t&&!this.isSameDay(t,e)?new Date(e).setHours("23","59","59","999"):e}}},isSameDay:function(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}},config:{value:null,submitFormat:"U",disabled:!1},setValue:function(e){this.getViewModel().set("datetime",e)},getValue:function(){return this.getViewModel().get("datetime")},getSubmitValue:function(){var e=this.getValue();return e?Ext.Date.format(e,this.submitFormat):null},setMinValue:function(e){this.getViewModel().set("minDatetime",e)},getMinValue:function(){return this.getViewModel().get("minDatetime")},setMaxValue:function(e){this.getViewModel().set("maxDatetime",e)},getMaxValue:function(){return this.getViewModel().get("maxDatetime")},initComponent:function(){let t=this;t.callParent();var e=t.getViewModel();e.set("datetime",t.config.value),e.bind("{datetime}",function(e){t.publishState("value",e),t.fireEvent("change",e)})},items:[{xtype:"datefield",editable:!1,flex:1,format:"Y-m-d",bind:{value:"{date}",minValue:"{minDate}",maxValue:"{maxDate}"}},{xtype:"timefield",format:"H:i",width:80,value:"00:00",increment:60,bind:{value:"{time}",minValue:"{minTime}",maxValue:"{maxTime}"}}]}),Ext.define("Proxmox.form.Checkbox",{extend:"Ext.form.field.Checkbox",alias:["widget.proxmoxcheckbox"],config:{defaultValue:void 0,deleteDefaultValue:!1,deleteEmpty:!1,clearOnDisable:!1},inputValue:"1",getSubmitData:function(){let e=this,t=null,a;return!e.disabled&&e.submitValue&&(null!==(a=e.getSubmitValue())?(t={},a===e.getDefaultValue()&&e.getDeleteDefaultValue()?t.delete=e.getName():t[e.getName()]=a):e.getDeleteEmpty()&&((t={}).delete=e.getName())),t},setDisabled:function(e){var t=this.clearOnDisable&&!this.disabled&&e;this.callParent(arguments),t&&this.setValue(!1)},setRawValue:function(e){1===e?this.callParent([!0]):this.callParent([e])}}),Ext.define("Proxmox.form.KVComboBox",{extend:"Ext.form.field.ComboBox",alias:"widget.proxmoxKVComboBox",config:{deleteEmpty:!0},comboItems:void 0,displayField:"value",valueField:"key",queryMode:"local",getSubmitData:function(){let e=this,t=null,a;return!e.disabled&&e.submitValue&&(null!==(a=e.getSubmitValue())&&""!==a&&"__default__"!==a?(t={})[e.getName()]=a:e.getDeleteEmpty()&&((t={}).delete=e.getName())),t},validator:function(e){var a=this;if(a.editable||null===e||""===e)return!0;if(0<a.store.getCount()){var i=a.multiSelect?e.split(a.delimiter):[e];let t=a.store.getData().collect("value","data");if(Ext.Array.every(i,function(e){return Ext.Array.contains(t,e)}))return!0}return"value '"+e+"' not allowed!"},initComponent:function(){var e=this;e.store=Ext.create("Ext.data.ArrayStore",{model:"KeyValue",data:e.comboItems}),void 0===e.initialConfig.editable&&(e.editable=!1),e.callParent()},setComboItems:function(e){this.getStore().setData(e)}}),Ext.define("Proxmox.form.LanguageSelector",{extend:"Proxmox.form.KVComboBox",xtype:"proxmoxLanguageSelector",comboItems:Proxmox.Utils.language_array(),matchFieldWidth:!1,listConfig:{width:300}}),Ext.define("Proxmox.form.ComboGrid",{extend:"Ext.form.field.ComboBox",alias:["widget.proxmoxComboGrid"],preferredValue:void 0,onKeyUp:function(e,t){var a=this,i=e.getKey();a.editable||!a.allowBlank||a.multiSelect||i!==e.BACKSPACE&&i!==e.DELETE||a.setValue(""),a.callParent(arguments)},config:{skipEmptyText:!1,notFoundIsValid:!1,deleteEmpty:!1,errorHeight:100,showClearTrigger:!1},enableKeyEvents:!0,editable:!1,triggers:{clear:{cls:"pmx-clear-trigger",weight:-1,hidden:!0,handler:function(){this.setValue("")}}},setValue:function(e){var t=Ext.isArray(e)?!e.length:!e;return this.triggers.clear.setVisible(!t&&(this.allowBlank||this.showClearTrigger)),this.callParent([e])},getRawValue:function(){return this.multiSelect?this.rawValue:this.callParent()},getSubmitData:function(){var e,t=this;let a=null;return!t.disabled&&t.submitValue&&(null!==(e=t.getSubmitValue())?(a={})[t.getName()]=e:t.getDeleteEmpty()&&((a={}).delete=t.getName())),a},getSubmitValue:function(){var e=this.callParent();return""===e&&this.getSkipEmptyText()?null:e},setAllowBlank:function(e){this.allowBlank=e,this.validate()},onBindStore:function(e,t){var a,i=this,o=i.picker;e&&(e.autoCreated&&(i.queryMode="local",i.valueField=i.displayField="field1",e.expanded||(i.displayField="field2"),i.setDisplayTpl(null)),Ext.isDefined(i.valueField)||(i.valueField=i.displayField),(a={byValue:{rootProperty:"data",unique:!1}}).byValue.property=i.valueField,e.setExtraKeys(a),i.displayField===i.valueField?e.byText=e.byValue:(a.byText={rootProperty:"data",unique:!1},a.byText.property=i.displayField,e.setExtraKeys(a)),a={rootProperty:"data",extraKeys:{byInternalId:{property:"internalId"},byValue:{property:i.valueField,rootProperty:"data"}},listeners:{beginupdate:i.onValueCollectionBeginUpdate,endupdate:i.onValueCollectionEndUpdate,scope:i}},i.valueCollection=new Ext.util.Collection(a),i.pickerSelectionModel=new Ext.selection.RowModel({mode:i.multiSelect?"SIMPLE":"SINGLE",deselectOnContainerClick:!1,enableInitialSelection:!1,pruneRemoved:!1,selected:i.valueCollection,store:e,listeners:{scope:i,lastselectedchanged:i.updateBindSelection}}),t||i.resetToDefault(),o)&&(o.setSelectionModel(i.pickerSelectionModel),o.getStore()!==e)&&o.bindStore(e)},createPicker:function(){let a=this,i;var e=Ext.apply({xtype:"gridpanel",id:a.pickerId,pickerField:a,floating:!0,hidden:!0,store:a.store,displayField:a.displayField,preserveScrollOnRefresh:!0,pageSize:a.pageSize,tpl:a.tpl,selModel:a.pickerSelectionModel,focusOnToFront:!1},a.listConfig,a.defaultListConfig);return(i=a.picker||Ext.widget(e)).getStore()!==a.store&&i.bindStore(a.store),a.pageSize&&i.pagingToolbar.on("beforechange",a.onPageChange,a),i.refresh=function(){i.getSelectionModel().select(a.valueCollection.getRange()),i.getView().refresh()},i.getNodeByRecord=function(){i.getView().getNodeByRecord(arguments)},i.initialConfig.maxHeight||i.on({beforeshow:a.onBeforePickerShow,scope:a}),i.getSelectionModel().on({beforeselect:a.onBeforeSelect,beforedeselect:a.onBeforeDeselect,focuschange:a.onFocusChange,selectionChange:function(e,t){t.length&&(this.setValue(t),this.fireEvent("select",a,t))},scope:a}),a.multiSelect||i.on("itemclick",function(e,t){i.getSelection()[0]===t&&a.collapse()}),i.on("show",function(){a.store.fireEvent("refresh"),a.enableLoadMask&&(a.savedMinHeight=a.savedMinHeight??i.getMinHeight(),i.setMinHeight(a.errorHeight)),a.loadError&&(Proxmox.Utils.setErrorMask(i.getView(),a.loadError),delete a.loadError,i.updateLayout())}),i.getNavigationModel().navigateOnSpace=!1,i},clearLocalFilter:function(){var e=this;e.queryFilter&&(e.changingFilters=!0,e.store.removeFilter(e.queryFilter,!0),e.queryFilter=null,e.changingFilters=!1)},isValueInStore:function(e){let t=this,a=t.store,i=!1;return a&&(t.queryFilter&&"local"===t.queryMode&&t.clearFilterOnBlur&&t.clearLocalFilter(),Ext.isArray(e)?Ext.Array.each(e,function(e){return!a.findRecord(t.valueField,e,0,!1,!0,!0)||!(i=!0)}):i=!!a.findRecord(t.valueField,e,0,!1,!0,!0)),i},validator:function(e){var t=this;return!(e&&((t.valueField&&t.valueField!==t.displayField||t.multiSelect&&!Ext.isArray(e))&&(e=t.getValue()),!t.notFoundIsValid)&&!t.isValueInStore(e))||gettext("Invalid Value")},setDisabled:function(e){this.callParent([e]),this.validate()},initComponent:function(){let o=this;Ext.apply(o,{queryMode:"local",matchFieldWidth:!1}),Ext.applyIf(o,{value:[]}),Ext.applyIf(o.listConfig,{width:400}),o.callParent(),o.picker||o.getPicker(),o.mon(o.store,"beforeload",function(){o.isDisabled()||(o.enableLoadMask=!0)}),o.mon(o.store,"load",function(e,t,a,i){if(a){o.clearInvalid(),delete o.loadError,o.enableLoadMask&&(delete o.enableLoadMask,o.picker)&&(o.picker.setMinHeight(o.savedMinHeight||0),Proxmox.Utils.setErrorMask(o.picker.getView()),delete o.savedMinHeight,o.picker.updateLayout());let e=o.getValue()||o.preferredValue,t=(e&&o.setValue(e,!0),!1);(t=e?o.isValueInStore(e):t)||((Ext.isArray(e)?e.length:e)?o.notFoundIsValid||o.isDisabled()||o.markInvalid(gettext("Invalid Value")):(a=o.store.first(),o.autoSelect&&a&&a.data?(e=a.data[o.valueField],o.setValue(e,!0)):o.allowBlank||(o.setValue(e),o.isDisabled())||o.markInvalid(o.blankText)))}else{a=Proxmox.Utils.getResponseErrorMessage(i.getError());o.picker&&(o.savedMinHeight=o.savedMinHeight??o.picker.getMinHeight(),o.picker.setMinHeight(o.errorHeight),Proxmox.Utils.setErrorMask(o.picker.getView(),a),o.picker.updateLayout()),o.loadError=a}})}}),Ext.define("Proxmox.form.RRDTypeSelector",{extend:"Ext.form.field.ComboBox",alias:["widget.proxmoxRRDTypeSelector"],displayField:"text",valueField:"id",editable:!1,queryMode:"local",value:"hour",stateEvents:["select"],stateful:!0,stateId:"proxmoxRRDTypeSelection",store:{type:"array",fields:["id","timeframe","cf","text"],data:[["hour","hour","AVERAGE",gettext("Hour")+" ("+gettext("average")+")"],["hourmax","hour","MAX",gettext("Hour")+" ("+gettext("maximum")+")"],["day","day","AVERAGE",gettext("Day")+" ("+gettext("average")+")"],["daymax","day","MAX",gettext("Day")+" ("+gettext("maximum")+")"],["week","week","AVERAGE",gettext("Week")+" ("+gettext("average")+")"],["weekmax","week","MAX",gettext("Week")+" ("+gettext("maximum")+")"],["month","month","AVERAGE",gettext("Month")+" ("+gettext("average")+")"],["monthmax","month","MAX",gettext("Month")+" ("+gettext("maximum")+")"],["year","year","AVERAGE",gettext("Year")+" ("+gettext("average")+")"],["yearmax","year","MAX",gettext("Year")+" ("+gettext("maximum")+")"]]},getState:function(){var e=this.getStore().findExact("id",this.getValue()),e=this.getStore().getAt(e);if(e)return{id:e.data.id,timeframe:e.data.timeframe,cf:e.data.cf}},applyState:function(e){e&&e.id&&this.setValue(e.id)}}),Ext.define("Proxmox.form.BondModeSelector",{extend:"Proxmox.form.KVComboBox",alias:["widget.bondModeSelector"],openvswitch:!1,initComponent:function(){this.openvswitch?this.comboItems=Proxmox.Utils.bond_mode_array(["active-backup","balance-slb","lacp-balance-slb","lacp-balance-tcp"]):this.comboItems=Proxmox.Utils.bond_mode_array(["balance-rr","active-backup","balance-xor","broadcast","802.3ad","balance-tlb","balance-alb"]),this.callParent()}}),Ext.define("Proxmox.form.BondPolicySelector",{extend:"Proxmox.form.KVComboBox",alias:["widget.bondPolicySelector"],comboItems:[["layer2","layer2"],["layer2+3","layer2+3"],["layer3+4","layer3+4"]]}),Ext.define("Proxmox.form.NetworkSelectorController",{extend:"Ext.app.ViewController",alias:"controller.proxmoxNetworkSelectorController",init:function(e){if(!e.nodename)throw"missing custom view config: nodename";e.getStore().getProxy().setUrl("/api2/json/nodes/"+e.nodename+"/network")}}),Ext.define("Proxmox.data.NetworkSelector",{extend:"Ext.data.Model",fields:[{name:"active"},{name:"cidr"},{name:"cidr6"},{name:"address"},{name:"address6"},{name:"comments"},{name:"iface"},{name:"slaves"},{name:"type"}]}),Ext.define("Proxmox.form.NetworkSelector",{extend:"Proxmox.form.ComboGrid",alias:"widget.proxmoxNetworkSelector",controller:"proxmoxNetworkSelectorController",nodename:"localhost",setNodename:function(e){this.nodename=e;var t=this.getStore();t.removeAll(),this.getPicker().refresh(),t&&"function"==typeof t.getProxy&&(t.getProxy().setUrl("/api2/json/nodes/"+e+"/network"),t.load())},valueField:"cidr",displayField:"cidr",store:{autoLoad:!0,model:"Proxmox.data.NetworkSelector",proxy:{type:"proxmox"},sorters:[{property:"iface",direction:"ASC"}],filters:[function(e){return e.data.cidr}],listeners:{load:function(a,e,t){t&&e.forEach(function(e){var t;e.data.cidr6&&((t=e.data.cidr?e.copy(null):e).data.cidr=e.data.cidr6,t.data.address=e.data.address6,delete e.data.cidr6,t.data.comments=e.data.comments6,delete e.data.comments6,a.add(t))})}}},listConfig:{width:600,columns:[{header:gettext("CIDR"),dataIndex:"cidr",hideable:!1,flex:1},{header:gettext("IP"),dataIndex:"address",hidden:!0},{header:gettext("Interface"),width:90,dataIndex:"iface"},{header:gettext("Active"),renderer:Proxmox.Utils.format_boolean,width:60,dataIndex:"active"},{header:gettext("Type"),width:80,hidden:!0,dataIndex:"type"},{header:gettext("Comment"),flex:2,dataIndex:"comments",renderer:Ext.String.htmlEncode}]}}),Ext.define("Proxmox.form.RealmComboBox",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxRealmComboBox",controller:{xclass:"Ext.app.ViewController",init:function(e){var t=e.getStore();t.proxy.url="/api2/json"+e.baseUrl,e.storeFilter&&t.setFilters(e.storeFilter),t.on("load",this.onLoad,e),t.load()},onLoad:function(e,a,t){if(t){t=this.getValue();if(!t||!this.store.findRecord("realm",t,0,!1,!0,!0)){let t="pam";Ext.each(a,function(e){e.data&&e.data.default&&(t=e.data.realm)}),this.setValue(t)}}}},storeFilter:void 0,fieldLabel:gettext("Realm"),name:"realm",queryMode:"local",allowBlank:!1,editable:!1,forceSelection:!0,autoSelect:!1,triggerAction:"all",valueField:"realm",displayField:"descr",baseUrl:"/access/domains",getState:function(){return{value:this.getValue()}},applyState:function(e){e&&e.value&&this.setValue(e.value)},stateEvents:["select"],stateful:!0,id:"pveloginrealm",stateID:"pveloginrealm",store:{model:"pmx-domains",autoLoad:!1}}),Ext.define("Proxmox.form.field.PruneKeep",{extend:"Proxmox.form.field.Integer",xtype:"pmxPruneKeepField",allowBlank:!0,minValue:1,listeners:{dirtychange:(e,t)=>e.triggers.clear.setVisible(t)},triggers:{clear:{cls:"pmx-clear-trigger",weight:-1,hidden:!0,handler:function(){this.triggers.clear.setVisible(!1),this.setValue(this.originalValue)}}}}),Ext.define("pmx-roles",{extend:"Ext.data.Model",fields:["roleid","privs"],proxy:{type:"proxmox",url:"/api2/json/access/roles"},idProperty:"roleid"}),Ext.define("Proxmox.form.RoleSelector",{extend:"Proxmox.form.ComboGrid",alias:"widget.pmxRoleSelector",allowBlank:!1,autoSelect:!1,valueField:"roleid",displayField:"roleid",listConfig:{width:560,resizable:!0,columns:[{header:gettext("Role"),sortable:!0,dataIndex:"roleid",flex:2},{header:gettext("Privileges"),dataIndex:"privs",cellWrap:!0,renderer:e=>Ext.isArray(e)?e.join(", "):e.replaceAll(",",", "),flex:5}]},store:{autoLoad:!0,model:"pmx-roles",sorters:"roleid"}}),Ext.define("Proxmox.form.DiskSelector",{extend:"Proxmox.form.ComboGrid",xtype:"pmxDiskSelector",diskType:void 0,includePartitions:!1,typeProperty:"type",valueField:"devpath",displayField:"devpath",emptyText:gettext("No Disks unused"),listConfig:{width:600,columns:[{header:gettext("Device"),flex:3,sortable:!0,dataIndex:"devpath"},{header:gettext("Size"),flex:2,sortable:!1,renderer:Proxmox.Utils.format_size,dataIndex:"size"},{header:gettext("Serial"),flex:5,sortable:!0,dataIndex:"serial"}]},initComponent:function(){var e=this,t=e.nodename;if(!t)throw"no node name specified";var a={},t=(e.diskType&&(a[e.typeProperty]=e.diskType),e.includePartitions&&(a["include-partitions"]=1),Ext.create("Ext.data.Store",{filterOnLoad:!0,model:"pmx-disk-list",proxy:{type:"proxmox",url:`/api2/json/nodes/${t}/disks/list`,extraParams:a},sorters:[{property:"devpath",direction:"ASC"}]}));Ext.apply(e,{store:t}),e.callParent(),t.load()}}),Ext.define("Proxmox.form.MultiDiskSelector",{extend:"Ext.grid.Panel",alias:"widget.pmxMultiDiskSelector",mixins:{field:"Ext.form.field.Field"},selModel:"checkboxmodel",store:{data:[],proxy:{type:"proxmox"}},valueField:"devpath",typeParameter:"type",diskType:"unused",includePartitions:!1,disks:[],allowBlank:!1,getValue:function(){return this.disks},setValue:function(e){var t=this;e??=[],Ext.isArray(e)||(e=e.split(/;, /));let a=t.getStore(),i=[],o=t.valueField;return e.forEach(e=>{e=a.findRecord(o,e,0,!1,!0,!0);e&&i.push(e)}),t.setSelection(i),t.mixins.field.setValue.call(t,e)},getErrors:function(e){return!1===this.allowBlank&&0===this.getSelectionModel().getCount()?(this.addBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[gettext("No Disk selected")]):(this.removeBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[])},update_disklist:function(){var t=this,e=t.getSelection(),a=[];e.sort(function(e,t){return(e.get("order")||0)-(t.get("order")||0)}),e.forEach(function(e){a.push(e.get(t.valueField))}),t.validate(),t.disks=a},columns:[{text:gettext("Device"),dataIndex:"devpath",flex:2},{text:gettext("Model"),dataIndex:"model",flex:2},{text:gettext("Serial"),dataIndex:"serial",flex:2},{text:gettext("Size"),dataIndex:"size",renderer:Proxmox.Utils.format_size,flex:1},{header:gettext("Order"),xtype:"widgetcolumn",dataIndex:"order",sortable:!0,flex:1,widget:{xtype:"proxmoxintegerfield",minValue:1,isFormField:!1,listeners:{change:function(e,t,a){var i=this.up("pmxMultiDiskSelector"),e=e.getWidgetRecord();e.set("order",t),i.update_disklist(e)}}}}],listeners:{selectionchange:function(){this.update_disklist()}},initComponent:function(){var e=this,t={};if(!e.url){if(!e.nodename)throw"no url or nodename given";e.url=`/api2/json/nodes/${e.nodename}/disks/list`,t[e.typeParameter]=e.diskType,e.includePartitions&&(t["include-partitions"]=1)}e.disks=[],e.callParent();var a=e.getStore();a.setProxy({type:"proxmox",url:e.url,extraParams:t}),a.load(),a.sort({property:e.valueField})}}),Ext.define("Proxmox.form.TaskTypeSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxTaskTypeSelector",anyMatch:!0,initComponent:function(){this.store=Object.keys(Proxmox.Utils.task_desc_table).sort(),this.callParent()},listeners:{change:function(e,t,a){t!==this.originalValue&&this.triggers.clear.setVisible(!0)}},triggers:{clear:{cls:"pmx-clear-trigger",weight:-1,hidden:!0,handler:function(){this.triggers.clear.setVisible(!1),this.setValue(this.originalValue)}}}}),Ext.define("Proxmox.form.ACMEApiSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxACMEApiSelector",fieldLabel:gettext("DNS API"),displayField:"name",valueField:"id",store:{model:"proxmox-acme-challenges",autoLoad:!0},triggerAction:"all",queryMode:"local",allowBlank:!1,editable:!0,forceSelection:!0,anyMatch:!0,selectOnFocus:!0,getSchema:function(){var e=this.getValue();if(e){e=this.getStore().findRecord("id",e,0,!1,!0,!0);if(e)return e.data.schema}return{}},initComponent:function(){if(!this.url)throw"no url given";this.callParent(),this.getStore().getProxy().setUrl(this.url)}}),Ext.define("Proxmox.form.ACMEAccountSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxACMEAccountSelector",displayField:"name",valueField:"name",store:{model:"proxmox-acme-accounts",autoLoad:!0},triggerAction:"all",queryMode:"local",allowBlank:!1,editable:!1,forceSelection:!0,isEmpty:function(){return 0===this.getStore().getData().length},initComponent:function(){if(!this.url)throw"no url given";this.callParent(),this.getStore().getProxy().setUrl(this.url)}}),Ext.define("Proxmox.form.ACMEPluginSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxACMEPluginSelector",fieldLabel:gettext("Plugin"),displayField:"plugin",valueField:"plugin",store:{model:"proxmox-acme-plugins",autoLoad:!0,filters:e=>"dns"===e.data.type},triggerAction:"all",queryMode:"local",allowBlank:!1,editable:!1,initComponent:function(){if(!this.url)throw"no url given";this.callParent(),this.getStore().getProxy().setUrl(this.url)}}),Ext.define("Proxmox.form.UserSelector",{extend:"Proxmox.form.ComboGrid",alias:"widget.pmxUserSelector",allowBlank:!1,autoSelect:!1,valueField:"userid",displayField:"userid",editable:!0,anyMatch:!0,forceSelection:!0,store:{model:"pmx-users",autoLoad:!0,params:{enabled:1},sorters:"userid"},listConfig:{columns:[{header:gettext("User"),sortable:!0,dataIndex:"userid",renderer:Ext.String.htmlEncode,flex:1},{header:gettext("Name"),sortable:!0,renderer:(e,t,a)=>Ext.String.htmlEncode(`${e||""} `+(a.data.lastname||"")),dataIndex:"firstname",flex:1},{header:gettext("Comment"),sortable:!1,dataIndex:"comment",renderer:Ext.String.htmlEncode,flex:1}]}}),Ext.define("Proxmox.form.ThemeSelector",{extend:"Proxmox.form.KVComboBox",xtype:"proxmoxThemeSelector",comboItems:Proxmox.Utils.theme_array()}),Ext.define("Proxmox.form.field.FingerprintField",{extend:"Proxmox.form.field.Textfield",alias:["widget.pmxFingerprintField"],config:{fieldLabel:gettext("Fingerprint"),emptyText:gettext("Server certificate SHA-256 fingerprint, required for self-signed certificates"),regex:/[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){31}/,regexText:gettext("Example")+": AB:CD:EF:...",allowBlank:!0}}),Ext.define("Proxmox.button.Button",{extend:"Ext.button.Button",alias:"widget.proxmoxButton",selModel:void 0,enableFn:function(e){},confirmMsg:!1,dangerous:!1,parentXType:"grid",initComponent:function(){let n=this;if(n.handler){let o=n.handler;n.handler=function(t,a){let i,e;(!n.selModel||(i=n.selModel.getSelection()[0])&&!1!==n.enableFn(i))&&(n.confirmMsg?(e=n.confirmMsg,Ext.isFunction(n.confirmMsg)&&(e=n.confirmMsg(i)),Ext.MessageBox.defaultButton=n.dangerous?2:1,Ext.Msg.show({title:gettext("Confirm"),icon:n.dangerous?Ext.Msg.WARNING:Ext.Msg.QUESTION,message:e,buttons:Ext.Msg.YESNO,defaultFocus:n.dangerous?"no":"yes",callback:function(e){"yes"===e&&Ext.callback(o,n.scope,[t,a,i],0,n)}})):Ext.callback(o,n.scope,[t,a,i],0,n))}}var e;if(n.callParent(),n.selModel||null===n.selModel||!1===n.selModel||(e=n.up(n.parentXType))&&e.selModel&&(n.selModel=e.selModel),!0===n.waitMsgTarget){if(!(e=n.up("grid")))throw"unable to find waitMsgTarget";n.waitMsgTarget=e}n.selModel&&n.mon(n.selModel,"selectionchange",function(){var e=n.selModel.getSelection()[0];e&&!1!==n.enableFn(e)?n.setDisabled(!1):n.setDisabled(!0)})}}),Ext.define("Proxmox.button.StdRemoveButton",{extend:"Proxmox.button.Button",alias:"widget.proxmoxStdRemoveButton",text:gettext("Remove"),disabled:!0,delay:void 0,config:{baseurl:void 0,customConfirmationMessage:void 0},getUrl:function(e){return this.selModel?this.baseurl+"/"+e.getId():this.baseurl},callback:function(e,t,a){},getRecordName:e=>e.getId(),confirmMsg:function(e){e=this.getRecordName(e);let t;return t=this.customConfirmationMessage||gettext("Are you sure you want to remove entry {0}"),Ext.String.format(t,Ext.htmlEncode(`'${e}'`))},handler:function(e,t,a){let i=this,o=i.getUrl(a);void 0!==i.delay&&0<=i.delay&&(o+="?delay="+i.delay),Proxmox.Utils.API2Request({url:o,method:"DELETE",waitMsgTarget:i.waitMsgTarget,callback:function(e,t,a){Ext.callback(i.callback,i.scope,[e,t,a],0,i)},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})},initComponent:function(){var e=this;void 0!==e.initialConfig.disabled||null!==e.selModel&&!1!==e.selModel||(e.disabled=!1),e.callParent()}}),Ext.define("Proxmox.button.AltText",{extend:"Proxmox.button.Button",xtype:"proxmoxAltTextButton",defaultText:"",altText:"",listeners:{render:function(e){e.setText(this.altText);var t=e.getSize().width,a=(e.setText(this.defaultText),e.getSize().width);e.setWidth(t<a?a:t)}}}),Ext.define("Proxmox.button.Help",{extend:"Ext.button.Button",xtype:"proxmoxHelpButton",text:gettext("Help"),iconCls:" x-btn-icon-el-default-toolbar-small fa fa-question-circle",cls:"x-btn-default-toolbar-small proxmox-inline-button",hidden:!0,listenToGlobalEvent:!0,controller:{xclass:"Ext.app.ViewController",listen:{global:{proxmoxShowHelp:"onProxmoxShowHelp",proxmoxHideHelp:"onProxmoxHideHelp"}},onProxmoxShowHelp:function(e){var t=this.getView();!0===t.listenToGlobalEvent&&(t.setOnlineHelp(e),t.show())},onProxmoxHideHelp:function(){var e=this.getView();!0===e.listenToGlobalEvent&&e.hide()}},setOnlineHelp:function(t){var a=Proxmox.Utils.get_help_info(t);if(a){this.onlineHelp=t;let e=a.title;a.subtitle&&(e+=" - "+a.subtitle),this.setTooltip(e)}},setHelpConfig:function(e){this.setOnlineHelp(e.onlineHelp)},handler:function(){let e;(e=this.onlineHelp?Proxmox.Utils.get_help_link(this.onlineHelp):e)?window.open(e):Ext.Msg.alert(gettext("Help"),gettext("No Help available"))},initComponent:function(){this.callParent(),this.onlineHelp&&this.setOnlineHelp(this.onlineHelp)}}),Ext.define("Proxmox.grid.ObjectGrid",{extend:"Ext.grid.GridPanel",alias:["widget.proxmoxObjectGrid"],gridRows:[],disabled:!1,hideHeaders:!0,monStoreErrors:!1,add_combobox_row:function(e,t,a){a=a||{},this.rows=this.rows||{},this.rows[e]={required:!0,defaultValue:a.defaultValue,header:t,renderer:a.renderer,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:a.labelWidth||100},items:{xtype:"proxmoxKVComboBox",name:e,comboItems:a.comboItems,value:a.defaultValue,deleteEmpty:!!a.deleteEmpty,emptyText:a.defaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,a.labelWidth),fieldLabel:t}}},a.onlineHelp&&(this.rows[e].editor.onlineHelp=a.onlineHelp)},add_text_row:function(e,t,a){a=a||{},this.rows=this.rows||{},this.rows[e]={required:!0,defaultValue:a.defaultValue,header:t,renderer:a.renderer,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:a.labelWidth||100},items:{xtype:"proxmoxtextfield",name:e,deleteEmpty:!!a.deleteEmpty,emptyText:a.defaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,a.labelWidth),vtype:a.vtype,fieldLabel:t}}},a.onlineHelp&&(this.rows[e].editor.onlineHelp=a.onlineHelp)},add_boolean_row:function(e,t,a){a=a||{},this.rows=this.rows||{},this.rows[e]={required:!0,defaultValue:a.defaultValue||0,header:t,renderer:a.renderer||Proxmox.Utils.format_boolean,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:a.labelWidth||100},items:{xtype:"proxmoxcheckbox",name:e,uncheckedValue:0,defaultValue:a.defaultValue||0,checked:!!a.defaultValue,deleteDefaultValue:!!a.deleteDefaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,a.labelWidth),fieldLabel:t}}},a.onlineHelp&&(this.rows[e].editor.onlineHelp=a.onlineHelp)},add_integer_row:function(e,t,a){a=a||{},this.rows=this.rows||{},this.rows[e]={required:!0,defaultValue:a.defaultValue,header:t,renderer:a.renderer,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:a.labelWidth||100},items:{xtype:"proxmoxintegerfield",name:e,minValue:a.minValue,maxValue:a.maxValue,emptyText:gettext("Default"),deleteEmpty:!!a.deleteEmpty,value:a.defaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,a.labelWidth),fieldLabel:t}}},a.onlineHelp&&(this.rows[e].editor.onlineHelp=a.onlineHelp)},add_textareafield_row:function(e,t,a){a=a||{},this.rows=this.rows||{};var i=a.fieldOpts||{};this.rows[e]={required:!0,defaultValue:"",header:t,renderer:e=>Ext.htmlEncode(Proxmox.Utils.base64ToUtf8(e)),editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:a.labelWidth||600},items:{xtype:"proxmoxBase64TextArea",...i,name:e}}},a.onlineHelp&&(this.rows[e].editor.onlineHelp=a.onlineHelp)},editorConfig:{},run_editor:function(){var a=this,i=a.getSelectionModel().getSelection()[0];if(i){var o=a.rows[i.data.key];if(o.editor){let e,t;Ext.isString(o.editor)?(t=Ext.apply({confid:i.data.key},a.editorConfig),e=Ext.create(o.editor,t)):(t=Ext.apply({confid:i.data.key},a.editorConfig),Ext.apply(t,o.editor),(e=Ext.createWidget(o.editor.xtype,t)).load()),e.show(),e.on("destroy",a.reload,a)}}},reload:function(){this.rstore.load()},getObjectValue:function(e,t){e=this.store.getById(e);return e?e.data.value:t},renderKey:function(e,t,a,i,o,n){var r=this.rows;return(r&&r[e]?r[e]:{}).header||e},renderValue:function(e,t,a,i,o,n){var r=this.rows,l=a.data.key,r=(r&&r[l]?r[l]:{}).renderer;return r?r.call(this,e,t,a,i,o,n):e},listeners:{itemkeydown:function(e,t,a,i,o){o.getKey()===o.ENTER&&(this.pressedIndex=i)},itemkeyup:function(e,t,a,i,o){o.getKey()===o.ENTER&&i===this.pressedIndex&&this.run_editor(),this.pressedIndex=void 0}},initComponent:function(){var e=this;for(const l of e.gridRows||[]){var t=e[`add_${l.xtype}_row`];if("function"!=typeof t)throw`unknown object-grid row xtype '${l.xtype}'`;if("string"!=typeof l.name)throw"object-grid row need a valid name string-property!";t.call(e,l.name,l.text||l.name,l)}let a=e.rows;if(!e.rstore){if(!e.url)throw"no url specified";e.rstore=Ext.create("Proxmox.data.ObjectStore",{url:e.url,interval:e.interval,extraParams:e.extraParams,rows:e.rows})}var i=e.rstore,o=Ext.create("Proxmox.data.DiffStore",{rstore:i,sorters:[],filters:[]});if(a)for(var[n,r]of Object.entries(a))Ext.isDefined(r.defaultValue)?o.add({key:n,value:r.defaultValue}):r.required&&o.add({key:n,value:void 0});e.sorterFn&&o.sorters.add(Ext.create("Ext.util.Sorter",{sorterFn:e.sorterFn})),o.filters.add(Ext.create("Ext.util.Filter",{filterFn:function(e){if(a){e=a[e.data.key];if(!e||!1===e.visible)return!1}return!0}})),Proxmox.Utils.monStoreErrors(e,i),Ext.applyIf(e,{store:o,stateful:!1,columns:[{header:gettext("Name"),width:e.cwidth1||200,dataIndex:"key",renderer:e.renderKey},{flex:1,header:gettext("Value"),dataIndex:"value",renderer:e.renderValue}]}),e.callParent(),e.monStoreErrors&&Proxmox.Utils.monStoreErrors(e,e.store)}}),Ext.define("Proxmox.grid.PendingObjectGrid",{extend:"Proxmox.grid.ObjectGrid",alias:["widget.proxmoxPendingObjectGrid"],getObjectValue:function(t,a,i){t=this.store.getById(t);if(t){let e=t.data.value;return i&&(Ext.isDefined(t.data.pending)&&""!==t.data.pending?e=t.data.pending:1===t.data.delete&&(e=a)),Ext.isDefined(e)&&""!==e?e:a}return a},hasPendingChanges:function(e){let t=this;var a=t.rows,a=(a&&a[e]?a[e]:{}).multiKey||[e];let i=!1;return Ext.Array.each(a,function(e){e=t.store.getById(e);return!(e&&e.data&&(Ext.isDefined(e.data.pending)&&""!==e.data.pending||1===e.data.delete)&&(i=!0))}),i},renderValue:function(e,t,a,i,o,n){let r=this;var l=r.rows,s=a.data.key,l=l&&l[s]?l[s]:{},d=l.renderer;let u="",c="";if(d?(u=d(e,t,a,i,o,n,!1),(c=r.hasPendingChanges(s)?d(a.data.pending,t,a,i,o,n,!0):c)===u&&(c=void 0)):(u=e||"",c=a.data.pending),a.data.delete){let t=!0;l.multiKey&&Ext.Array.each(l.multiKey,function(e){e=r.store.getById(e);return!e||!e.data||1===e.data.delete||(t=!1)}),t&&(c='<div style="text-decoration: line-through;">'+u+"</div>")}return c?u+'<div style="color:darkorange">'+c+"</div>":u},initComponent:function(){var e=this;if(!e.rstore){if(!e.url)throw"no url specified";e.rstore=Ext.create("Proxmox.data.ObjectStore",{model:"KeyValuePendingDelete",readArray:!0,url:e.url,interval:e.interval,extraParams:e.extraParams,rows:e.rows})}e.callParent()}}),Ext.define("Proxmox.panel.AuthView",{extend:"Ext.grid.GridPanel",alias:"widget.pmxAuthView",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,stateful:!0,stateId:"grid-authrealms",viewConfig:{trackOver:!1},baseUrl:"/access/domains",storeBaseUrl:"/access/domains",columns:[{header:gettext("Realm"),width:100,sortable:!0,dataIndex:"realm"},{header:gettext("Type"),width:100,sortable:!0,dataIndex:"type"},{header:gettext("Default"),width:80,sortable:!0,dataIndex:"default",renderer:e=>e?Proxmox.Utils.renderEnabledIcon(!0):"",align:"center",cbind:{hidden:"{!showDefaultRealm}"}},{header:gettext("Comment"),sortable:!1,dataIndex:"comment",renderer:Ext.String.htmlEncode,flex:1}],openEditWindow:function(e,t){let a=this;var{useTypeInUrl:i,onlineHelp:o}=Proxmox.Schema.authDomains[e];Ext.create("Proxmox.window.AuthEditBase",{baseUrl:a.baseUrl,useTypeInUrl:i,onlineHelp:o,authType:e,realm:t,showDefaultRealm:a.showDefaultRealm,listeners:{destroy:()=>a.reload()}}).show()},reload:function(){this.getStore().load()},run_editor:function(){var e=this.getSelection()[0];e&&Proxmox.Schema.authDomains[e.data.type].edit&&this.openEditWindow(e.data.type,e.data.realm)},open_sync_window:function(){var e=this.getSelection()[0];e&&Proxmox.Schema.authDomains[e.data.type].sync&&Ext.create("Proxmox.window.SyncWindow",{type:e.data.type,realm:e.data.realm,listeners:{destroy:()=>this.reload()}}).show()},initComponent:function(){var a=this,e=(a.store={model:"pmx-domains",sorters:{property:"realm",direction:"ASC"},proxy:{type:"proxmox",url:"/api2/json"+a.storeBaseUrl}},[]);for(const[i,o]of Object.entries(Proxmox.Schema.authDomains).sort())o.add&&e.push({text:o.name,iconCls:"fa fa-fw "+(o.iconCls||"fa-address-book-o"),handler:()=>a.openEditWindow(i)});var t=[{text:gettext("Add"),menu:{items:e}},{xtype:"proxmoxButton",text:gettext("Edit"),disabled:!0,enableFn:e=>Proxmox.Schema.authDomains[e.data.type].edit,handler:()=>a.run_editor()},{xtype:"proxmoxStdRemoveButton",getUrl:e=>{let t=a.baseUrl;return Proxmox.Schema.authDomains[e.data.type].useTypeInUrl&&(t+="/"+e.get("type")),t+="/"+e.getId()},enableFn:e=>Proxmox.Schema.authDomains[e.data.type].add,callback:()=>a.reload()},{xtype:"proxmoxButton",text:gettext("Sync"),disabled:!0,enableFn:e=>Proxmox.Schema.authDomains[e.data.type].sync,handler:()=>a.open_sync_window()}];if(a.extraButtons){t.push("-");for(const n of a.extraButtons)t.push(n)}Ext.apply(a,{tbar:t,listeners:{activate:()=>a.reload(),itemdblclick:()=>a.run_editor()}}),a.callParent()}}),Ext.define("pmx-disk-list",{extend:"Ext.data.Model",fields:["devpath","used",{name:"size",type:"number"},{name:"osdid",type:"number",defaultValue:-1},{name:"status",convert:function(e,t){return e||t.data.health||("partition"===t.data.type?"":Proxmox.Utils.unknownText)}},{name:"name",convert:function(e,t){return e||t.data.devpath||void 0}},{name:"disk-type",convert:function(e,t){return e||t.data.type||void 0}},"vendor","model","serial","rpm","type","wearout","health","mounted"],idProperty:"devpath"}),Ext.define("Proxmox.DiskList",{extend:"Ext.tree.Panel",alias:"widget.pmxDiskList",supportsWipeDisk:!1,rootVisible:!1,emptyText:gettext("No Disks found"),stateful:!0,stateId:"tree-node-disks",controller:{xclass:"Ext.app.ViewController",reload:function(){var e=this.getView(),t={},e=(e.includePartitions&&(t["include-partitions"]=1),e.baseurl+"/list");this.store.setProxy({type:"proxmox",extraParams:t,url:e}),this.store.load()},openSmartWindow:function(){var e=this.getView(),t=e.getSelection();!t||t.length<1||(t=t[0],Ext.create("Proxmox.window.DiskSmart",{baseurl:e.baseurl,dev:t.data.name}).show())},initGPT:function(){let a=this;var e=a.getView(),t=e.getSelection();!t||t.length<1||(t=t[0],Proxmox.Utils.API2Request({url:e.exturl+"/initgpt",waitMsgTarget:e,method:"POST",params:{disk:t.data.name},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus),success:function(e,t){Ext.create("Proxmox.window.TaskProgress",{upid:e.result.data,taskDone:function(){a.reload()},autoShow:!0})}}))},wipeDisk:function(){let a=this;var e=a.getView(),t=e.getSelection();!t||t.length<1||(t=t[0],Proxmox.Utils.API2Request({url:e.exturl+"/wipedisk",waitMsgTarget:e,method:"PUT",params:{disk:t.data.name},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus),success:function(e,t){Ext.create("Proxmox.window.TaskProgress",{upid:e.result.data,taskDone:function(){a.reload()},autoShow:!0})}}))},init:function(e){var t=e.nodename||"localhost";e.baseurl=`/api2/json/nodes/${t}/disks`,e.exturl=`/api2/extjs/nodes/${t}/disks`,this.store=Ext.create("Ext.data.Store",{model:"pmx-disk-list"}),this.store.on("load",this.onLoad,this),Proxmox.Utils.monStoreErrors(e,this.store),this.reload()},onLoad:function(e,t,a,i){var o=this.getView();if(a){var n={};for(const x of t){var r,l=x.data;l.expanded=!0,l.children=l.partitions??[];for(r of l.children)r["disk-type"]="partition",r.iconCls="fa fa-fw fa-hdd-o x-fa-tree",r.used="filesystem"===r.used?r.filesystem:r.used,r.parent=l.devpath,r.children=[],r.leaf=!0;l.iconCls="fa fa-fw fa-hdd-o x-fa-tree",l.leaf=0===l.children.length,l.parent||(n[l.devpath]=l)}for(const m of t){var s=m.data;s.parent&&(n[s.parent].leaf=!1,n[s.parent].children.push(s))}var d,u,c=[];for([d,u]of Object.entries(n))c.push(u);o.setRootNode({expanded:!0,children:c}),Proxmox.Utils.setErrorMask(o,!1)}else Proxmox.Utils.setErrorMask(o,Proxmox.Utils.getResponseErrorMessage(i.getError()))}},renderDiskType:function(e){if(void 0===e)return Proxmox.Utils.unknownText;switch(e){case"ssd":return"SSD";case"hdd":return"Hard Disk";case"usb":return"USB";default:return e}},renderDiskUsage:function(e,t,a){let i="";if(a){var o=[];if(a.data["osdid-list"]&&0<a.data["osdid-list"].length)for(const n of a.data["osdid-list"].sort())o.push("OSD."+n.toString());else void 0!==a.data.osdid&&0<=a.data.osdid&&o.push("OSD."+a.data.osdid.toString());0<a.data.journals&&o.push("Journal"),0<a.data.db&&o.push("DB"),0<a.data.wal&&o.push("WAL"),0<o.length&&(i=`, Ceph (${o.join(", ")})`)}return(e={bios:"BIOS boot",zfsreserved:"ZFS reserved",efi:"EFI",lvm:"LVM",zfs:"ZFS"}[e]||e)?""+e+i:Proxmox.Utils.noText},columns:[{xtype:"treecolumn",header:gettext("Device"),width:150,sortable:!0,dataIndex:"devpath"},{header:gettext("Type"),width:80,sortable:!0,dataIndex:"disk-type",renderer:function(e){return this.renderDiskType(e)}},{header:gettext("Usage"),width:150,sortable:!1,renderer:function(e,t,a){return this.renderDiskUsage(e,t,a)},dataIndex:"used"},{header:gettext("Size"),width:100,align:"right",sortable:!0,renderer:Proxmox.Utils.format_size,dataIndex:"size"},{header:"GPT",width:60,align:"right",renderer:Proxmox.Utils.format_boolean,dataIndex:"gpt"},{header:gettext("Vendor"),width:100,sortable:!0,hidden:!0,renderer:Ext.String.htmlEncode,dataIndex:"vendor"},{header:gettext("Model"),width:200,sortable:!0,renderer:Ext.String.htmlEncode,dataIndex:"model"},{header:gettext("Serial"),width:200,sortable:!0,renderer:Ext.String.htmlEncode,dataIndex:"serial"},{header:"S.M.A.R.T.",width:100,sortable:!0,renderer:Ext.String.htmlEncode,dataIndex:"status"},{header:gettext("Mounted"),width:60,align:"right",renderer:Proxmox.Utils.format_boolean,dataIndex:"mounted"},{header:gettext("Wearout"),width:90,sortable:!0,align:"right",dataIndex:"wearout",renderer:function(e){return Ext.isNumeric(e)?(100-e).toString()+"%":gettext("N/A")}}],listeners:{itemdblclick:"openSmartWindow"},initComponent:function(){let r=this;var e=[{text:gettext("Reload"),handler:"reload"},{xtype:"proxmoxButton",text:gettext("Show S.M.A.R.T. values"),parentXType:"treepanel",disabled:!0,enableFn:function(e){return!(!e||e.data.parent)},handler:"openSmartWindow"},{xtype:"proxmoxButton",text:gettext("Initialize Disk with GPT"),parentXType:"treepanel",disabled:!0,enableFn:function(e){return!(!e||e.data.parent||e.data.used&&"unused"!==e.data.used)},handler:"initGPT"}];r.supportsWipeDisk&&(e.push("-"),e.push({xtype:"proxmoxButton",text:gettext("Wipe Disk"),parentXType:"treepanel",dangerous:!0,confirmMsg:function(e){var t=e.data,a=Ext.String.format(gettext("Are you sure you want to wipe {0}?"),t.devpath),i=(a+="<br> "+gettext("All data on the device will be lost!"),r.renderDiskType(t["disk-type"]));let o;o=0<t.children.length?(n=t.children.map(e=>r.renderDiskUsage(e.used)).join(", "),gettext("Partitions")+` (${n})`):r.renderDiskUsage(t.used,void 0,e);var n=Proxmox.Utils.format_size(t.size),e=Ext.String.htmlEncode(t.serial),t=gettext("Type")+`: ${i}<br>`;return a+"<br><br>"+((t+=`${gettext("Usage")}: ${o}<br>`)+(gettext("Size")+`: ${n}<br>`)+(gettext("Serial")+": "+e))},disabled:!0,handler:"wipeDisk"})),r.tbar=e,r.callParent()}}),Ext.define("Proxmox.EOLNotice",{extend:"Ext.Component",alias:"widget.proxmoxEOLNotice",userCls:"eol-notice",padding:"0 5",config:{product:"",version:"",eolDate:"",href:""},autoEl:{tag:"div","data-qtip":gettext("You won't get any security fixes after the End-Of-Life date. Please consider upgrading.")},getIconCls:function(){var e=new Date,t=new Date(this.eolDate);return new Date(t.getTime()-18144e5)<e?"critical fa-exclamation-triangle":"info-blue fa-info-circle"},initComponent:function(){var e=this,t=e.getIconCls(),a=e.href.startsWith("http")?e.href:"https://"+e.href,i=Ext.String.format(gettext("Support for {0} {1} ends on {2}"),e.product,e.version,e.eolDate);e.html=`<i class="fa ${t}"></i>
	    <a href="${a}" target="_blank">${i} <i class="fa fa-external-link"></i></a>
	`,e.callParent()}}),Ext.define("Proxmox.panel.InputPanel",{extend:"Ext.panel.Panel",alias:["widget.inputpanel"],listeners:{activate:function(){this.onlineHelp&&Ext.GlobalEvents.fireEvent("proxmoxShowHelp",this.onlineHelp)},deactivate:function(){this.onlineHelp&&Ext.GlobalEvents.fireEvent("proxmoxHideHelp",this.onlineHelp)}},border:!1,onlineHelp:void 0,hasAdvanced:!1,showAdvanced:!1,onGetValues:function(e){return e},getValues:function(t){Ext.isFunction(this.onGetValues)&&(t=!1);let a={};return Ext.Array.each(this.query("[isFormField]"),function(e){t&&!e.isDirty()||Proxmox.Utils.assemble_field_data(a,e.getSubmitData())}),this.onGetValues(a)},setAdvancedVisible:function(e){var t=this.getComponent("advancedContainer");t&&t.setVisible(e)},onSetValues:function(e){return e},setValues:function(e){let i=this,o=i.up("form");e=i.onSetValues(e),Ext.iterate(e,function(e,t){for(const a of i.query("[isFormField][name="+e+"]"))a&&(a.setValue(t),o.trackResetOnLoad)&&a.resetOriginalValue()})},initComponent:function(){var e=this;let t;if(e.items)t=[{layout:"anchor",items:e.items}],e.items=void 0;else{if(e.column4)t=[],e.columnT&&t.push({padding:"0 0 0 0",layout:"anchor",items:e.columnT}),t.push({layout:"hbox",defaults:{border:!1,layout:"anchor",flex:1},items:[{padding:"0 10 0 0",items:e.column1},{padding:"0 10 0 0",items:e.column2},{padding:"0 10 0 0",items:e.column3},{padding:"0 0 0 10",items:e.column4}]});else{if(!e.column1)throw"unsupported config";t=[],e.columnT&&t.push({padding:"0 0 10 0",layout:"anchor",items:e.columnT}),t.push({layout:"hbox",defaults:{border:!1,layout:"anchor",flex:1},items:[{padding:"0 10 0 0",items:e.column1},{padding:"0 0 0 10",items:e.column2||[]}]})}e.columnB&&t.push({padding:"10 0 0 0",layout:"anchor",items:e.columnB})}let a;e.advancedItems?(a=[{layout:"anchor",items:e.advancedItems}],e.advancedItems=void 0):(e.advancedColumn1||e.advancedColumn2||e.advancedColumnB)&&(a=[{layout:{type:"hbox",align:"begin"},defaults:{border:!1,layout:"anchor",flex:1},items:[{padding:"0 10 0 0",items:e.advancedColumn1||[]},{padding:"0 0 0 10",items:e.advancedColumn2||[]}]}],e.advancedColumn1=void 0,e.advancedColumn2=void 0,e.advancedColumnB)&&(a.push({padding:"10 0 0 0",layout:"anchor",items:e.advancedColumnB}),e.advancedColumnB=void 0),a&&(e.hasAdvanced=!0,a.unshift({xtype:"box",hidden:!1,border:!0,autoEl:{tag:"hr"}}),t.push({xtype:"container",itemId:"advancedContainer",hidden:!e.showAdvanced,defaults:{border:!1},items:a})),Ext.apply(e,{layout:{type:"vbox",align:"stretch"},defaultType:"container",items:t}),e.callParent()}}),Ext.define("Proxmox.widget.Info",{extend:"Ext.container.Container",alias:"widget.pmxInfoWidget",layout:{type:"vbox",align:"stretch"},value:0,maximum:1,printBar:!0,items:[{xtype:"component",itemId:"label",data:{title:"",usage:"",iconCls:void 0},tpl:['<div class="left-aligned">','<tpl if="iconCls">','<i class="{iconCls}"></i> ',"</tpl>",'{title}</div>&nbsp;<div class="right-aligned">{usage}</div>']},{height:2,border:0},{xtype:"progressbar",itemId:"progress",height:5,value:0,animate:!0}],warningThreshold:.75,criticalThreshold:.9,setPrintBar:function(e){this.printBar=e,this.getComponent("progress").setVisible(e)},setIconCls:function(e){this.getComponent("label").data.iconCls=e},setData:function(e){this.updateValue(e.text,e.usage)},updateValue:function(e,t){var a,i=this;i.lastText===e&&i.lastUsage===t||(i.lastText=e,i.lastUsage=t,(a=i.getComponent("label")).update(Ext.apply(a.data,{title:i.title,usage:e})),void 0!==t&&i.printBar&&Ext.isNumeric(t)&&0<=t&&((a=i.getComponent("progress")).updateProgress(t,""),t>i.criticalThreshold?(a.removeCls("warning"),a.addCls("critical")):t>i.warningThreshold?(a.removeCls("critical"),a.addCls("warning")):(a.removeCls("warning"),a.removeCls("critical"))))},initComponent:function(){var e=this;if(!e.title)throw"no title defined";e.callParent(),e.getComponent("progress").setVisible(e.printBar),e.updateValue(e.text,e.value),e.setIconCls(e.iconCls)}}),Ext.define("Proxmox.panel.LogView",{extend:"Ext.panel.Panel",xtype:"proxmoxLogView",pageSize:510,viewBuffer:50,lineHeight:16,scrollToEnd:!0,failCallback:void 0,controller:{xclass:"Ext.app.ViewController",updateParams:function(){var e,t,a,i=this.getViewModel();i.get("hide_timespan")||i.get("livemode")||(e=i.get("since"),(t=i.get("until"))<e?Ext.Msg.alert("Error","Since date must be less equal than Until date."):(a=i.get("submitFormat"),i.set("params.since",Ext.Date.format(e,a)),"Y-m-d"===a?i.set("params.until",Ext.Date.format(t,a)+" 23:59:59"):i.set("params.until",Ext.Date.format(t,a)),this.getView().loadTask.delay(200)))},scrollPosBottom:function(){var e=this.getView(),t=e.getScrollY();return e.getScrollable().getMaxPosition().y-t},updateView:function(e,t,a){var i=this,o=i.getView(),n=i.getViewModel(),r=i.lookup("content"),l=n.get("data");t===l.first&&a===l.total&&e.length===l.lines&&1!==a||(n.set("data",{first:t,total:a,lines:e.length}),l=i.scrollPosBottom(),(n=o.scrollToEnd&&l<=5)||(e.length=a),r.update(e.join("<br>")),n&&((t=o.getScrollable()).suspendEvent("scroll"),o.scrollTo(0,1/0),i.updateStart(!0),t.resumeEvent("scroll")))},doLoad:function(){let n=this;if(n.running)n.requested=!0;else{n.running=!0;let o=n.getView();var e=n.getViewModel();Proxmox.Utils.API2Request({url:n.getView().url,params:e.get("params"),method:"GET",success:function(e){if(!n.isDestroyed){Proxmox.Utils.setErrorMask(n,!1);var i=e.result.total;let t=[],a=1/0;Ext.Array.each(e.result.data,function(e){a>e.n&&(a=e.n),t[e.n-1]=Ext.htmlEncode(e.t)}),n.updateView(t,a-1,i),n.running=!1,n.requested&&(n.requested=!1,o.loadTask.delay(200))}},failure:function(e){o.failCallback?o.failCallback(e):(e=e.htmlStatus,Proxmox.Utils.setErrorMask(n,e)),n.running=!1,n.requested&&(n.requested=!1,o.loadTask.delay(200))}})}},updateStart:function(e,t){var a=this.getView(),i=this.getViewModel(),o=i.get("params.limit"),n=i.get("data.total"),r=a.lastTargetLine&&a.lastTargetLine>t?2/3:1/3,e=(a.lastTargetLine=t,e?Math.trunc(n-o,10):Math.trunc(t-r*o+10));i.set("params.start",Math.max(e,0)),a.loadTask.delay(200)},onScroll:function(e,t){var a=this.getView(),i=this.getViewModel(),o=a.getScrollY()/a.lineHeight,n=a.getHeight()/a.lineHeight,r=Math.max(Math.trunc(o-1-a.viewBuffer),0),n=Math.trunc(o+n+1+a.viewBuffer),{start:a,limit:i}=i.get("params"),l=a<20?0:20;(r<a+l||a+i-l<n)&&this.updateStart(!1,o)},onLiveMode:function(){var e=this.getViewModel(),e=(e.set("livemode",!0),e.set("params",{start:0,limit:510}),this.getView());delete e.content,e.scrollToEnd=!0,this.updateView([],!0,!1)},onTimespan:function(){this.getViewModel().set("livemode",!1),this.updateView([],!1),this.updateParams()},init:function(e){let t=this;if(!e.url)throw"no url specified";var a=this.getViewModel(),i=new Date;i.setDate(i.getDate()-3),a.set("until",new Date),a.set("since",i),a.set("params.limit",e.pageSize),a.set("hide_timespan",!e.log_select_timespan),a.set("submitFormat",e.submitFormat),t.lookup("content").setStyle("line-height",e.lineHeight+"px"),e.loadTask=new Ext.util.DelayedTask(t.doLoad,t),t.updateParams(),e.task=Ext.TaskManager.start({run:()=>{e.isVisible()&&e.scrollToEnd&&t.scrollPosBottom()<=5&&e.loadTask.delay(200)},interval:1e3})}},onDestroy:function(){this.loadTask.cancel(),Ext.TaskManager.stop(this.task)},requestUpdate:function(){this.loadTask.delay(200)},viewModel:{data:{until:null,since:null,submitFormat:"Y-m-d",livemode:!0,hide_timespan:!1,data:{start:0,total:0,textlen:0},params:{start:0,limit:510}}},layout:"auto",bodyPadding:5,scrollable:{x:"auto",y:"auto",listeners:{scroll:{fn:function(e,t,a){var i=this.component.getController();i&&i.onScroll(t,a)},buffer:200}}},tbar:{bind:{hidden:"{hide_timespan}"},items:["->",{xtype:"segmentedbutton",items:[{text:gettext("Live Mode"),bind:{pressed:"{livemode}"},handler:"onLiveMode"},{text:gettext("Select Timespan"),bind:{pressed:"{!livemode}"},handler:"onTimespan"}]},{xtype:"box",autoEl:{cn:gettext("Since")+":"},bind:{disabled:"{livemode}"}},{xtype:"proxmoxDateTimeField",name:"since_date",reference:"since",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{since}",maxValue:"{until}",submitFormat:"{submitFormat}"}},{xtype:"box",autoEl:{cn:gettext("Until")+":"},bind:{disabled:"{livemode}"}},{xtype:"proxmoxDateTimeField",name:"until_date",reference:"until",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{until}",minValue:"{since}",submitFormat:"{submitFormat}"}},{xtype:"button",text:"Update",handler:"updateParams",bind:{disabled:"{livemode}"}}]},items:[{xtype:"box",reference:"content",style:{font:"normal 11px tahoma, arial, verdana, sans-serif","white-space":"pre"}}]}),Ext.define("Proxmox.widget.NodeInfoRepoStatus",{extend:"Proxmox.widget.Info",alias:"widget.pmxNodeInfoRepoStatus",title:gettext("Repository Status"),colspan:2,printBar:!1,product:void 0,repoLink:void 0,viewModel:{data:{subscriptionActive:"",noSubscriptionRepo:"",enterpriseRepo:"",testRepo:""},formulas:{repoStatus:function(e){return""===e("subscriptionActive")||""===e("enterpriseRepo")?"":e("noSubscriptionRepo")||e("testRepo")?"non-production":e("subscriptionActive")&&e("enterpriseRepo")?"ok":!e("subscriptionActive")&&e("enterpriseRepo")?"no-sub":e("enterpriseRepo")&&e("noSubscriptionRepo")&&e("testRepo")?"unknown":"no-repo"},repoStatusMessage:function(e){var t=this.getView(),e=e("repoStatus"),a=` <a data-qtip="${gettext("Open Repositories Panel")}"
		    href="${t.repoLink}">
		    <i class="fa black fa-chevron-right txt-shadow-hover"></i>
		    </a>`;return Proxmox.Utils.formatNodeRepoStatus(e,t.product)+a}}},setValue:function(e){this.updateValue(e)},bind:{value:"{repoStatusMessage}"},setRepositoryInfo:function(e){var t=this.getViewModel();for(const o of e){var a=o.handle,i=o.status||0;"enterprise"===a?t.set("enterpriseRepo",i):"no-subscription"===a?t.set("noSubscriptionRepo",i):"test"===a&&t.set("testRepo",i)}},setSubscriptionStatus:function(e){this.getViewModel().set("subscriptionActive",e)},initComponent:function(){if(void 0===this.product)throw"no product name provided";if(void 0===this.repoLink)throw"no repo link href provided";this.callParent()}}),Ext.define("Proxmox.panel.NotificationConfigViewModel",{extend:"Ext.app.ViewModel",alias:"viewmodel.pmxNotificationConfigPanel",formulas:{builtinSelected:function(e){e=e("selection")?.get("origin");return"modified-builtin"===e||"builtin"===e},removeButtonText:e=>e("builtinSelected")?gettext("Reset"):gettext("Remove"),removeButtonConfirmMessage:function(e){if(e("builtinSelected"))return gettext("Do you want to reset {0} to its default settings?")}}}),Ext.define("Proxmox.panel.NotificationConfigView",{extend:"Ext.panel.Panel",alias:"widget.pmxNotificationConfigView",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"chapter_notifications",layout:{type:"border"},items:[{region:"center",border:!1,xtype:"pmxNotificationEndpointView",cbind:{baseUrl:"{baseUrl}"}},{region:"south",height:"50%",border:!1,collapsible:!0,animCollapse:!1,xtype:"pmxNotificationMatcherView",cbind:{baseUrl:"{baseUrl}"}}]}),Ext.define("Proxmox.panel.NotificationEndpointView",{extend:"Ext.grid.Panel",alias:"widget.pmxNotificationEndpointView",title:gettext("Notification Targets"),viewModel:{type:"pmxNotificationConfigPanel"},bind:{selection:"{selection}"},controller:{xclass:"Ext.app.ViewController",openEditWindow:function(e,t){let a=this;Ext.create("Proxmox.window.EndpointEditBase",{baseUrl:a.getView().baseUrl,type:e,name:t,autoShow:!0,listeners:{destroy:()=>a.reload()}})},openEditForSelectedItem:function(){var e=this.getView().getSelection();e.length<1||this.openEditWindow(e[0].data.type,e[0].data.name)},reload:function(){this.getView().getStore().rstore.load(),this.getView().setSelection(null)},testEndpoint:function(){let t=this.getView();var e=t.getSelection();if(!(e.length<1)){let a=e[0].data.name;Ext.Msg.confirm(gettext("Notification Target Test"),Ext.String.format(gettext("Do you want to send a test notification to '{0}'?"),a),function(e){"yes"===e&&Proxmox.Utils.API2Request({method:"POST",url:`${t.baseUrl}/targets/${a}/test`,success:function(e,t){Ext.Msg.show({title:gettext("Notification Target Test"),message:Ext.String.format(gettext("Sent test notification to '{0}'."),a),buttons:Ext.Msg.OK,icon:Ext.Msg.INFO})},autoErrorAlert:!0})})}}},listeners:{itemdblclick:"openEditForSelectedItem",activate:"reload"},emptyText:gettext("No notification targets configured"),columns:[{dataIndex:"disable",text:gettext("Enable"),renderer:e=>Proxmox.Utils.renderEnabledIcon(!e),align:"center"},{dataIndex:"name",text:gettext("Target Name"),renderer:Ext.String.htmlEncode,flex:2},{dataIndex:"type",text:gettext("Type"),renderer:Ext.String.htmlEncode,flex:1},{dataIndex:"comment",text:gettext("Comment"),renderer:Ext.String.htmlEncode,flex:3},{dataIndex:"origin",text:gettext("Origin"),renderer:e=>{switch(e){case"user-created":return gettext("Custom");case"modified-builtin":return gettext("Built-In (modified)");case"builtin":return gettext("Built-In")}return"unknown"}}],store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-notification-endpoints",model:"proxmox-notification-endpoints",autoStart:!0},sorters:"name"},initComponent:function(){let t=this;if(!t.baseUrl)throw"baseUrl is not set!";var e=[];for(const[a,i]of Object.entries(Proxmox.Schema.notificationEndpointTypes).sort())e.push({text:i.name,iconCls:"fa fa-fw "+(i.iconCls||"fa-bell-o"),handler:()=>t.controller.openEditWindow(a)});Ext.apply(t,{tbar:[{text:gettext("Add"),menu:e},{xtype:"proxmoxButton",text:gettext("Modify"),handler:"openEditForSelectedItem",disabled:!0},{xtype:"proxmoxStdRemoveButton",callback:"reload",bind:{text:"{removeButtonText}",customConfirmationMessage:"{removeButtonConfirmMessage}"},getUrl:function(e){return`${t.baseUrl}/endpoints/${e.data.type}/`+e.getId()},enableFn:e=>{e=e.get("origin");return"user-created"===e||"modified-builtin"===e}},"-",{xtype:"proxmoxButton",text:gettext("Test"),handler:"testEndpoint",disabled:!0}]}),t.callParent(),t.store.rstore.proxy.setUrl(`/api2/json/${t.baseUrl}/targets`)}}),Ext.define("Proxmox.panel.NotificationMatcherView",{extend:"Ext.grid.Panel",alias:"widget.pmxNotificationMatcherView",title:gettext("Notification Matchers"),controller:{xclass:"Ext.app.ViewController",openEditWindow:function(e){let t=this;Ext.create("Proxmox.window.NotificationMatcherEdit",{baseUrl:t.getView().baseUrl,name:e,autoShow:!0,listeners:{destroy:()=>t.reload()}})},openEditForSelectedItem:function(){var e=this.getView().getSelection();e.length<1||this.openEditWindow(e[0].data.name)},reload:function(){this.getView().getStore().rstore.load(),this.getView().setSelection(null)}},viewModel:{type:"pmxNotificationConfigPanel"},bind:{selection:"{selection}"},listeners:{itemdblclick:"openEditForSelectedItem",activate:"reload"},emptyText:gettext("No notification matchers configured"),columns:[{dataIndex:"disable",text:gettext("Enable"),renderer:e=>Proxmox.Utils.renderEnabledIcon(!e),align:"center"},{dataIndex:"name",text:gettext("Matcher Name"),renderer:Ext.String.htmlEncode,flex:1},{dataIndex:"comment",text:gettext("Comment"),renderer:Ext.String.htmlEncode,flex:2},{dataIndex:"origin",text:gettext("Origin"),renderer:e=>{switch(e){case"user-created":return gettext("Custom");case"modified-builtin":return gettext("Built-In (modified)");case"builtin":return gettext("Built-In")}return"unknown"}}],store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-notification-matchers",model:"proxmox-notification-matchers",autoStart:!0},sorters:"name"},initComponent:function(){let e=this;if(!e.baseUrl)throw"baseUrl is not set!";Ext.apply(e,{tbar:[{xtype:"proxmoxButton",text:gettext("Add"),handler:()=>e.getController().openEditWindow(),selModel:!1},{xtype:"proxmoxButton",text:gettext("Modify"),handler:"openEditForSelectedItem",disabled:!0},{xtype:"proxmoxStdRemoveButton",callback:"reload",bind:{text:"{removeButtonText}",customConfirmationMessage:"{removeButtonConfirmMessage}"},baseurl:e.baseUrl+"/matchers",enableFn:e=>{e=e.get("origin");return"user-created"===e||"modified-builtin"===e}}]}),e.callParent(),e.store.rstore.proxy.setUrl(`/api2/json/${e.baseUrl}/matchers`)}}),Ext.define("Proxmox.panel.JournalView",{extend:"Ext.panel.Panel",xtype:"proxmoxJournalView",numEntries:500,lineHeight:16,scrollToEnd:!0,controller:{xclass:"Ext.app.ViewController",updateParams:function(){var e=this.getViewModel(),t=e.get("since"),e=e.get("until");t.setHours(0,0,0,0),e.setHours(0,0,0,0),e.setDate(e.getDate()+1),this.getView().loadTask.delay(200,void 0,void 0,[!1,!1,Ext.Date.format(t,"U"),Ext.Date.format(e,"U")])},scrollPosBottom:function(){var e=this.getView(),t=e.getScrollY();return e.getScrollable().getMaxPosition().y-t},scrollPosTop:function(){return this.getView().getScrollY()},updateScroll:function(e,t,a,i){let o=this.getView();e?o.scrollToEnd&&a<=5?setTimeout(function(){o.scrollTo(0,1/0)},10):!o.scrollToEnd&&i<20*o.lineHeight&&setTimeout(function(){o.scrollTo(0,t*o.lineHeight+i)},10):setTimeout(function(){o.scrollTo(0,0)},10)},updateView:function(t,a,i){var o=this,n=o.getView(),r=o.getViewModel();if(r&&r.get("livemode")===a){var r=o.lookup("content"),l=o.scrollPosBottom(),s=o.scrollPosTop(),d=t.shift(),u=t.pop(),c=t.length,t=t.map(Ext.htmlEncode).join("<br>");let e=!0;a?(i&&c?n.content=n.content?t+"<br>"+n.content:t:!i&&c?n.content=n.content?n.content+"<br>"+t:t:e=!1,i&&n.startcursor||(n.startcursor=u),!i&&n.endcursor||(n.endcursor=d)):n.content=c?t:"nothing logged or no timespan selected",e&&r.update(n.content),o.updateScroll(a,c,l,s)}},doLoad:function(a,i,o,n){let r=this;if(r.running)r.requested=!0;else{r.running=!0;let t=r.getView(),e={lastentries:t.numEntries||500};a?!i&&t.startcursor?e={startcursor:t.startcursor}:t.endcursor&&(e.endcursor=t.endcursor):e={since:o,until:n},Proxmox.Utils.API2Request({url:t.url,params:e,waitMsgTarget:a?void 0:t,method:"GET",success:function(e){r.isDestroyed||(Proxmox.Utils.setErrorMask(r,!1),e=e.result.data,r.updateView(e,a,i),r.running=!1,r.requested&&(r.requested=!1,t.loadTask.delay(200)))},failure:function(e){e=e.htmlStatus;Proxmox.Utils.setErrorMask(r,e),r.running=!1,r.requested&&(r.requested=!1,t.loadTask.delay(200))}})}},onScroll:function(e,t){var a=this.getView();this.getViewModel().get("livemode")&&(this.scrollPosTop()<20*a.lineHeight?(a.scrollToEnd=!1,a.loadTask.delay(200,void 0,void 0,[!0,!0])):this.scrollPosBottom()<=5&&(a.scrollToEnd=!0))},init:function(e){let t=this;if(!e.url)throw"no url specified";let a=t.getViewModel();var i=this.getViewModel(),o=new Date;o.setDate(o.getDate()-3),i.set("until",new Date),i.set("since",o),t.lookup("content").setStyle("line-height",e.lineHeight+"px"),e.loadTask=new Ext.util.DelayedTask(t.doLoad,t,[!0,!1]),e.task=Ext.TaskManager.start({run:function(){e.isVisible()&&e.scrollToEnd&&a.get("livemode")&&t.scrollPosBottom()<=5&&e.loadTask.delay(200,void 0,void 0,[!0,!1])},interval:1e3})},onLiveMode:function(){var e=this.getView();delete e.startcursor,delete e.endcursor,delete e.content,this.getViewModel().set("livemode",!0),e.scrollToEnd=!0,this.updateView([],!0,!1)},onTimespan:function(){this.getViewModel().set("livemode",!1),this.updateView([],!1)}},onDestroy:function(){this.loadTask.cancel(),Ext.TaskManager.stop(this.task),delete this.content},requestUpdate:function(){this.loadTask.delay(200)},viewModel:{data:{livemode:!0,until:null,since:null}},layout:"auto",bodyPadding:5,scrollable:{x:"auto",y:"auto",listeners:{scroll:{fn:function(e,t,a){var i=this.component.getController();i&&i.onScroll(t,a)},buffer:200}}},tbar:{items:["->",{xtype:"segmentedbutton",items:[{text:gettext("Live Mode"),bind:{pressed:"{livemode}"},handler:"onLiveMode"},{text:gettext("Select Timespan"),bind:{pressed:"{!livemode}"},handler:"onTimespan"}]},{xtype:"box",bind:{disabled:"{livemode}"},autoEl:{cn:gettext("Since")+":"}},{xtype:"datefield",name:"since_date",reference:"since",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{since}",maxValue:"{until}"}},{xtype:"box",bind:{disabled:"{livemode}"},autoEl:{cn:gettext("Until")+":"}},{xtype:"datefield",name:"until_date",reference:"until",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{until}",minValue:"{since}"}},{xtype:"button",text:"Update",reference:"updateBtn",handler:"updateParams",bind:{disabled:"{livemode}"}}]},items:[{xtype:"box",reference:"content",style:{font:"normal 11px tahoma, arial, verdana, sans-serif","white-space":"pre"}}]}),Ext.define("pmx-permissions",{extend:"Ext.data.TreeModel",fields:["text","type",{type:"boolean",name:"propagate"}]}),Ext.define("Proxmox.panel.PermissionViewPanel",{extend:"Ext.tree.Panel",xtype:"proxmoxPermissionViewPanel",scrollable:!0,layout:"fit",rootVisible:!1,animate:!1,sortableColumns:!1,auth_id_name:"userid",auth_id:void 0,columns:[{xtype:"treecolumn",header:gettext("Path")+"/"+gettext("Permission"),dataIndex:"text",flex:6},{header:gettext("Propagate"),dataIndex:"propagate",flex:1,renderer:function(e){return Ext.isDefined(e)?Proxmox.Utils.format_boolean(e):""}}],initComponent:function(){let a=this;Proxmox.Utils.API2Request({url:"/access/permissions?"+encodeURIComponent(a.auth_id_name)+"="+encodeURIComponent(a.auth_id),method:"GET",failure:function(e,t){Proxmox.Utils.setErrorMask(a,e.htmlStatus)},success:function(e,t){Proxmox.Utils.setErrorMask(a,!1);e=Ext.decode(e.responseText).data||{};let n={name:"__root",expanded:!0,children:[]},r={"/":{children:[],text:"/",type:"path"}};Ext.Object.each(e,function(e,t){let a={text:e,type:"path",children:[]};Ext.Object.each(t,function(e,t){e={text:e,type:"perm",propagate:1===t||!0===t,iconCls:"fa fa-fw fa-unlock",leaf:!0};a.children.push(e),a.expandable=!0}),r[e]=a}),Ext.Object.each(r,function(e,t){let a=r["/"];if("/"===e)a=n,t.expanded=!0;else for(var i=e.split("/");i.pop();){var o=i.join("/");if(r[o]){a=r[o];break}}a.children.push(t)}),a.setRootNode(n)}}),a.callParent(),a.store.sorters.add(new Ext.util.Sorter({sorterFn:function(e,t){let a=e.data.text,i=t.data.text;return e.data.type!==t.data.type&&(i=e.data.type,a=t.data.type),a>i?1:a<i?-1:0}}))}}),Ext.define("Proxmox.PermissionView",{extend:"Ext.window.Window",alias:"widget.userShowPermissionWindow",mixins:["Proxmox.Mixin.CBind"],scrollable:!0,width:800,height:600,layout:"fit",cbind:{title:e=>Ext.String.htmlEncode(e("auth_id"))+(" - "+gettext("Granted Permissions"))},items:[{xtype:"proxmoxPermissionViewPanel",cbind:{auth_id:"{auth_id}",auth_id_name:"{auth_id_name}"}}]}),Ext.define("Proxmox.panel.PruneInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxPruneInputPanel",mixins:["Proxmox.Mixin.CBind"],keepLastEmptyText:"",cbindData:function(){return this.isCreate=!!this.isCreate,{}},column1:[{xtype:"pmxPruneKeepField",name:"keep-last",fieldLabel:gettext("Keep Last"),cbind:{deleteEmpty:"{!isCreate}",emptyText:"{keepLastEmptyText}"}},{xtype:"pmxPruneKeepField",name:"keep-daily",fieldLabel:gettext("Keep Daily"),cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxPruneKeepField",name:"keep-monthly",fieldLabel:gettext("Keep Monthly"),cbind:{deleteEmpty:"{!isCreate}"}}],column2:[{xtype:"pmxPruneKeepField",fieldLabel:gettext("Keep Hourly"),name:"keep-hourly",cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxPruneKeepField",name:"keep-weekly",fieldLabel:gettext("Keep Weekly"),cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxPruneKeepField",name:"keep-yearly",fieldLabel:gettext("Keep Yearly"),cbind:{deleteEmpty:"{!isCreate}"}}]}),Ext.draw.Container.prototype.defaultDownloadServerUrl="-",Ext.define("Proxmox.chart.axis.segmenter.NumericBase2",{extend:"Ext.chart.axis.segmenter.Numeric",alias:"segmenter.numericBase2",preferredStep:function(e,t){var a=Math.floor(Math.log2(t)),i=Math.pow(2,a);return(t/=i)<=1?t=1:t<2&&(t=2),{unit:{fixes:-a,scale:i},step:t}},exactStep:function(e,t){var a=Math.floor(Math.log2(t));return{unit:{fixes:(t%Math.pow(2,a)==0?0:1)-a,scale:1},step:t}}}),Ext.define("Proxmox.widget.RRDChart",{extend:"Ext.chart.CartesianChart",alias:"widget.proxmoxRRDChart",unit:void 0,powerOfTwo:!1,downloadServerUrl:"-",controller:{xclass:"Ext.app.ViewController",init:function(e){this.powerOfTwo=e.powerOfTwo},convertToUnits:function(e){var t=["","k","M","G","T","P"];let a=0,i="0.##";e<.1&&(i+="#");for(var o=this.powerOfTwo?1024:1e3;o<=e&&a<t.length-1;)e/=o,a++;e=Ext.Number.correctFloat(e),e=Ext.util.Format.number(e,i);let n=t[a];return n&&this.powerOfTwo&&(n+="i"),e.toString()+" "+n},leftAxisRenderer:function(e,t,a){return this.convertToUnits(t)},onSeriesTooltipRender:function(e,t,a){var i=this.getView();let o="",n=("percent"===i.unit?o="%":"bytes"===i.unit?o="B":"bytespersecond"===i.unit&&(o="B/s"),a.field);i.fieldTitles&&i.fieldTitles[i.fields.indexOf(a.field)]&&(n=i.fieldTitles[i.fields.indexOf(a.field)]);i=this.convertToUnits(t.get(a.field)),a=new Date(t.get("time"));e.setHtml(`${n}: ${i}${o}<br>`+a)},onAfterAnimation:function(e,t){var a;e.header&&e.header.tools&&(a=e.header.tools[0],e=e.interactions[0].getUndoButton(),a.setDisabled(e.isDisabled()))}},width:770,height:300,animation:!1,interactions:[{type:"crosszoom"}],legend:{type:"dom",padding:0},listeners:{redraw:{fn:"onAfterAnimation",options:{buffer:500}}},touchAction:{panX:!0,panY:!0},constructor:function(e){var t=e.powerOfTwo?"numericBase2":"numeric";e.axes=[{type:"numeric",position:"left",grid:!0,renderer:"leftAxisRenderer",minimum:0,segmenter:t},{type:"time",position:"bottom",grid:!0,fields:["time"]}],this.callParent([e])},checkThemeColors:function(){var e=getComputedStyle(document.documentElement),t=e.getPropertyValue("--pwt-panel-background").trim()||"#ffffff";let a=e.getPropertyValue("--pwt-text-color").trim()||"#000000",i=e.getPropertyValue("--pwt-chart-primary").trim()||"#000000",o=e.getPropertyValue("--pwt-chart-grid-stroke").trim()||"#dddddd";this.setBackground(t),this.axes.forEach(e=>{e.setLabel({color:a}),e.setTitle({color:a}),e.setStyle({strokeStyle:i}),e.setGrid({stroke:o})}),this.redraw()},initComponent:function(){let i=this;if(!i.store)throw"cannot work without store";if(!i.fields)throw"cannot work without fields";i.callParent();let e="";"percent"===i.unit?e="%":"bytes"===i.unit?e="Bytes":"bytespersecond"===i.unit?e="Bytes/s":i.fieldTitles&&1===i.fieldTitles.length?e=i.fieldTitles[0]:1===i.fields.length&&(e=i.fields[0]),i.axes[0].setTitle(e),i.updateHeader(),i.header&&i.legend&&(i.header.padding="4 9 4",i.header.add(i.legend),i.legend=void 0),i.noTool||i.addTool({type:"minus",disabled:!0,tooltip:gettext("Undo Zoom"),handler:function(){var e=i.interactions[0].getUndoButton();e.handler&&e.handler()}}),i.fields.forEach(function(e,t){let a=e;i.fieldTitles&&i.fieldTitles[t]&&(a=i.fieldTitles[t]),i.addSeries(Ext.apply({type:"line",xField:"time",yField:e,title:a,fill:!0,style:{lineWidth:1.5,opacity:.6},marker:{opacity:0,scaling:.01},highlightCfg:{opacity:1,scaling:1.5},tooltip:{trackMouse:!0,renderer:"onSeriesTooltipRender"}},i.seriesConfig))}),i.store.onAfter("load",function(){i.setAnimation({duration:200,easing:"easeIn"})},this,{single:!0}),i.checkThemeColors(),i.mediaQueryList=window.matchMedia("(prefers-color-scheme: dark)"),i.themeListener=e=>{i.checkThemeColors()},i.mediaQueryList.addEventListener("change",i.themeListener)},doDestroy:function(){this.mediaQueryList.removeEventListener("change",this.themeListener),this.callParent()}}),Ext.define("Proxmox.panel.GaugeWidget",{extend:"Ext.panel.Panel",alias:"widget.proxmoxGauge",defaults:{style:{"text-align":"center"}},items:[{xtype:"box",itemId:"title",data:{title:""},tpl:"<h3>{title}</h3>"},{xtype:"polar",height:120,border:!1,downloadServerUrl:"-",itemId:"chart",series:[{type:"gauge",value:0,colors:["#f5f5f5"],sectors:[0],donut:90,needleLength:100,totalAngle:Math.PI}],sprites:[{id:"valueSprite",type:"text",text:"",textAlign:"center",textBaseline:"bottom",x:125,y:110,fontSize:30}]},{xtype:"box",itemId:"text"}],header:!1,border:!1,warningThreshold:.6,criticalThreshold:.9,warningColor:"#fc0",criticalColor:"#FF6C59",defaultColor:"#c2ddf2",backgroundColor:"#f5f5f5",initialValue:0,checkThemeColors:function(){var e=this,t=getComputedStyle(document.documentElement),a=t.getPropertyValue("--pwt-panel-background").trim()||"#ffffff",i=t.getPropertyValue("--pwt-text-color").trim()||"#000000",t=(e.defaultColor=t.getPropertyValue("--pwt-gauge-default").trim()||"#c2ddf2",e.criticalColor=t.getPropertyValue("--pwt-gauge-crit").trim()||"#ff6c59",e.warningColor=t.getPropertyValue("--pwt-gauge-warn").trim()||"#fc0",e.backgroundColor=t.getPropertyValue("--pwt-gauge-back").trim()||"#f5f5f5",e.chart.series[0].getValue()/100);let o=e.defaultColor;t>=e.criticalThreshold?o=e.criticalColor:t>=e.warningThreshold&&(o=e.warningColor),e.chart.series[0].setColors([o,e.backgroundColor]),e.chart.setBackground(a),e.valueSprite.setAttributes({fillStyle:i},!0),e.chart.redraw()},updateValue:function(e,t){var a=this;let i=a.defaultColor;var o={};e>=a.criticalThreshold?i=a.criticalColor:e>=a.warningThreshold&&(i=a.warningColor),a.chart.series[0].setColors([i,a.backgroundColor]),a.chart.series[0].setValue(100*e),a.valueSprite.setText(" "+(100*e).toFixed(0)+"%"),o.x=a.chart.getWidth()/2,o.y=a.chart.getHeight()-20,a.spriteFontSize&&(o.fontSize=a.spriteFontSize),a.valueSprite.setAttributes(o,!0),void 0!==t&&a.text.setHtml(t)},initComponent:function(){let t=this;t.callParent(),t.title&&t.getComponent("title").update({title:t.title}),t.text=t.getComponent("text"),t.chart=t.getComponent("chart"),t.valueSprite=t.chart.getSurface("chart").get("valueSprite"),t.checkThemeColors(),t.mediaQueryList=window.matchMedia("(prefers-color-scheme: dark)"),t.themeListener=e=>{t.checkThemeColors()},t.mediaQueryList.addEventListener("change",t.themeListener)},doDestroy:function(){this.mediaQueryList.removeEventListener("change",this.themeListener),this.callParent()}}),Ext.define("Proxmox.panel.GotifyEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxGotifyEditPanel",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"notification_targets_gotify",type:"gotify",items:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0},{xtype:"proxmoxtextfield",fieldLabel:gettext("Server URL"),name:"server",allowBlank:!1},{xtype:"proxmoxtextfield",inputType:"password",fieldLabel:gettext("API Token"),name:"token",cbind:{emptyText:e=>e("isCreate")?"":gettext("Unchanged"),allowBlank:"{!isCreate}"}},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:e=>(e.enable=!e.disable,delete e.disable,e),onGetValues:function(e){return e.enable?this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,e}}),Ext.define("Proxmox.panel.Certificates",{extend:"Ext.grid.Panel",xtype:"pmxCertificates",uploadButtons:void 0,infoUrl:void 0,columns:[{header:gettext("File"),width:150,dataIndex:"filename"},{header:gettext("Issuer"),flex:1,dataIndex:"issuer"},{header:gettext("Subject"),flex:1,dataIndex:"subject"},{header:gettext("Public Key Alogrithm"),flex:1,dataIndex:"public-key-type",hidden:!0},{header:gettext("Public Key Size"),flex:1,dataIndex:"public-key-bits",hidden:!0},{header:gettext("Valid Since"),width:150,dataIndex:"notbefore",renderer:Proxmox.Utils.render_timestamp},{header:gettext("Expires"),width:150,dataIndex:"notafter",renderer:Proxmox.Utils.render_timestamp},{header:gettext("Subject Alternative Names"),flex:1,dataIndex:"san",renderer:Proxmox.Utils.render_san},{header:gettext("Fingerprint"),dataIndex:"fingerprint",hidden:!0},{header:gettext("PEM"),dataIndex:"pem",hidden:!0}],reload:function(){this.rstore.load()},delete_certificate:function(){var e=this.selModel.getSelection()[0];if(e){let a=this.certById[e.id];e=a.url;Proxmox.Utils.API2Request({url:`/api2/extjs/${e}?restart=1`,method:"DELETE",success:function(e,t){a.reloadUi&&(Ext.getBody().mask(gettext("API server will be restarted to use new certificates, please reload web-interface!"),["pve-static-mask"]),Ext.defer(()=>window.location.reload(!0),1e4))},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})}},controller:{xclass:"Ext.app.ViewController",view_certificate:function(){var e=this.getView(),t=e.getSelection();!t||t.length<1||Ext.create("Proxmox.window.CertificateViewer",{cert:t[0].data.filename,url:"/api2/extjs/"+e.infoUrl}).show()}},listeners:{itemdblclick:"view_certificate"},initComponent:function(){let t=this;if(t.nodename||(t.nodename="_all"),!t.uploadButtons)throw"no upload buttons defined";if(!t.infoUrl)throw"no certificate store url given";t.rstore=Ext.create("Proxmox.data.UpdateStore",{storeid:"certs-"+t.nodename,model:"proxmox-certificate",proxy:{type:"proxmox",url:"/api2/extjs/"+t.infoUrl}}),t.store={type:"diff",rstore:t.rstore};var e=[];if(t.deletableCertIds={},t.certById={},1===t.uploadButtons.length){let a=t.uploadButtons[0];if(!a.url)throw"missing certificate url";(t.certById[a.id]=a).deletable&&(t.deletableCertIds[a.id]=!0),e.push({xtype:"button",text:gettext("Upload Custom Certificate"),handler:function(){var e=this.up("grid"),t=Ext.create("Proxmox.window.CertificateUpload",{url:"/api2/extjs/"+a.url,reloadUi:a.reloadUi});t.show(),t.on("destroy",e.reload,e)}})}else{var a=[];t.selModel=Ext.create("Ext.selection.RowModel",{});for(const i of t.uploadButtons){if(!i.id)throw"missing id in certificate entry";if(!i.url)throw"missing url in certificate entry";if(!i.name)throw"missing name in certificate entry";(t.certById[i.id]=i).deletable&&(t.deletableCertIds[i.id]=!0),a.push({text:Ext.String.format("Upload {0} Certificate",i.name),handler:function(){var e=this.up("grid"),t=Ext.create("Proxmox.window.CertificateUpload",{url:"/api2/extjs/"+i.url,reloadUi:i.reloadUi});t.show(),t.on("destroy",e.reload,e)}})}e.push({text:gettext("Upload Custom Certificate"),menu:{xtype:"menu",items:a}})}e.push({xtype:"proxmoxButton",text:gettext("Delete Custom Certificate"),confirmMsg:e=>{e=t.certById[e.id];return e.name?Ext.String.format(gettext("Are you sure you want to remove the certificate used for {0}"),e.name):gettext("Are you sure you want to remove the certificate")},callback:()=>t.reload(),selModel:t.selModel,disabled:!0,enableFn:e=>!!t.deletableCertIds[e.id],handler:function(){t.delete_certificate()}},"-",{xtype:"proxmoxButton",itemId:"viewbtn",disabled:!0,text:gettext("View Certificate"),handler:"view_certificate"}),Ext.apply(t,{tbar:e}),t.callParent(),t.rstore.startUpdate(),t.on("destroy",t.rstore.stopUpdate,t.rstore)}}),Ext.define("Proxmox.panel.ACMEAccounts",{extend:"Ext.grid.Panel",xtype:"pmxACMEAccounts",title:gettext("Accounts"),acmeUrl:void 0,controller:{xclass:"Ext.app.ViewController",addAccount:function(){let e=this;var t=e.getView(),a=-1!==t.getStore().findExact("name","default");Ext.create("Proxmox.window.ACMEAccountCreate",{defaultExists:a,acmeUrl:t.acmeUrl,taskDone:function(){e.reload()}}).show()},viewAccount:function(){var e=this.getView(),t=e.getSelection();t.length<1||Ext.create("Proxmox.window.ACMEAccountView",{url:e.acmeUrl+"/account/"+t[0].data.name}).show()},reload:function(){this.getView().getStore().rstore.load()},showTaskAndReload:function(e,t,a){let i=this;t&&(t=a.result.data,Ext.create("Proxmox.window.TaskProgress",{upid:t,taskDone:function(){i.reload()}}).show())}},minHeight:150,emptyText:gettext("No Accounts configured"),columns:[{dataIndex:"name",text:gettext("Name"),renderer:Ext.String.htmlEncode,flex:1}],listeners:{itemdblclick:"viewAccount"},store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-acme-accounts",model:"proxmox-acme-accounts",autoStart:!0},sorters:"name"},initComponent:function(){var e=this;if(!e.acmeUrl)throw"no acmeUrl given";Ext.apply(e,{tbar:[{xtype:"proxmoxButton",text:gettext("Add"),selModel:!1,handler:"addAccount"},{xtype:"proxmoxButton",text:gettext("View"),handler:"viewAccount",disabled:!0},{xtype:"proxmoxStdRemoveButton",baseurl:e.acmeUrl+"/account",callback:"showTaskAndReload"}]}),e.callParent(),e.store.rstore.proxy.setUrl(`/api2/json/${e.acmeUrl}/account`)}}),Ext.define("Proxmox.panel.ACMEPluginView",{extend:"Ext.grid.Panel",alias:"widget.pmxACMEPluginView",title:gettext("Challenge Plugins"),acmeUrl:void 0,controller:{xclass:"Ext.app.ViewController",addPlugin:function(){let e=this;var t=e.getView();Ext.create("Proxmox.window.ACMEPluginEdit",{acmeUrl:t.acmeUrl,url:t.acmeUrl+"/plugins",isCreate:!0,apiCallDone:function(){e.reload()}}).show()},editPlugin:function(){let e=this;var t=e.getView(),a=t.getSelection();a.length<1||(a=a[0].data.plugin,Ext.create("Proxmox.window.ACMEPluginEdit",{acmeUrl:t.acmeUrl,url:t.acmeUrl+"/plugins/"+a,apiCallDone:function(){e.reload()}}).show())},reload:function(){this.getView().getStore().rstore.load()}},minHeight:150,emptyText:gettext("No Plugins configured"),columns:[{dataIndex:"plugin",text:gettext("Plugin"),renderer:Ext.String.htmlEncode,flex:1},{dataIndex:"api",text:"API",renderer:Ext.String.htmlEncode,flex:1}],listeners:{itemdblclick:"editPlugin"},store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-acme-plugins",model:"proxmox-acme-plugins",autoStart:!0,filters:e=>!!e.data.api},sorters:"plugin"},initComponent:function(){var e=this;if(!e.acmeUrl)throw"no acmeUrl given";e.url=e.acmeUrl+"/plugins",Ext.apply(e,{tbar:[{xtype:"proxmoxButton",text:gettext("Add"),handler:"addPlugin",selModel:!1},{xtype:"proxmoxButton",text:gettext("Edit"),handler:"editPlugin",disabled:!0},{xtype:"proxmoxStdRemoveButton",callback:"reload",baseurl:e.acmeUrl+"/plugins"}]}),e.callParent(),e.store.rstore.proxy.setUrl(`/api2/json/${e.acmeUrl}/plugins`)}}),Ext.define("proxmox-acme-domains",{extend:"Ext.data.Model",fields:["domain","type","alias","plugin","configkey"],idProperty:"domain"}),Ext.define("Proxmox.panel.ACMEDomains",{extend:"Ext.grid.Panel",xtype:"pmxACMEDomains",mixins:["Proxmox.Mixin.CBind"],margin:"10 0 0 0",title:"ACME",emptyText:gettext("No Domains configured"),url:void 0,domainUsages:void 0,orderUrl:void 0,separateDomainEntries:void 0,acmeUrl:void 0,cbindData:function(e){return{acmeUrl:this.acmeUrl,accountUrl:`/api2/json/${this.acmeUrl}/account`}},viewModel:{data:{domaincount:0,account:void 0,configaccount:void 0,accountEditable:!1,accountsAvailable:!1,hasUsage:!1},formulas:{canOrder:e=>!!e("account")&&0<e("domaincount"),editBtnIcon:e=>"fa black fa-"+(e("accountEditable")?"check":"pencil"),accountTextHidden:e=>e("accountEditable")||!e("accountsAvailable"),accountValueHidden:e=>!e("accountEditable")||!e("accountsAvailable")}},controller:{xclass:"Ext.app.ViewController",init:function(e){this.lookup("accountselector").store.on("load",this.onAccountsLoad,this)},onAccountsLoad:function(e,t,a){let i=this,o=i.getViewModel();var n=o.get("configaccount");o.set("accountsAvailable",0<t.length),i.autoChangeAccount&&0<t.length?(i.changeAccount(t[0].data.name,()=>{o.set("accountEditable",!1),i.reload()}),i.autoChangeAccount=!1):n&&(-1!==e.findExact("name",n)?o.set("account",n):o.set("account",null))},addDomain:function(){let e=this;var t=e.getView();Ext.create("Proxmox.window.ACMEDomainEdit",{url:t.url,acmeUrl:t.acmeUrl,nodeconfig:t.nodeconfig,domainUsages:t.domainUsages,separateDomainEntries:t.separateDomainEntries,apiCallDone:function(){e.reload()}}).show()},editDomain:function(){let e=this;var t=e.getView(),a=t.getSelection();a.length<1||Ext.create("Proxmox.window.ACMEDomainEdit",{url:t.url,acmeUrl:t.acmeUrl,nodeconfig:t.nodeconfig,domainUsages:t.domainUsages,separateDomainEntries:t.separateDomainEntries,domain:a[0].data,apiCallDone:function(){e.reload()}}).show()},removeDomain:function(){let a=this;var e,t,i=a.getView(),o=i.getSelection();o.length<1||(e={},"acme"!==(o=o[0].data).configkey?e.delete=o.configkey:(t=Proxmox.Utils.parseACME(i.nodeconfig.acme),Proxmox.Utils.remove_domain_from_acme(t,o.domain),e.acme=Proxmox.Utils.printACME(t)),Proxmox.Utils.API2Request({method:"PUT",url:i.url,params:e,success:function(e,t){a.reload()},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}}))},toggleEditAccount:function(){let e=this,t=e.getViewModel();t.get("accountEditable")?e.changeAccount(t.get("account"),function(){t.set("accountEditable",!1),e.reload()}):t.set("accountEditable",!0)},changeAccount:function(e,a){var t=this.getView(),i={},o=Proxmox.Utils.parseACME(t.nodeconfig.acme);o.account=e,i.acme=Proxmox.Utils.printACME(o),Proxmox.Utils.API2Request({method:"PUT",waitMsgTarget:t,url:t.url,params:i,success:function(e,t){Ext.isFunction(a)&&a()},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})},order:function(a){let i=this;var e=i.getView();Proxmox.Utils.API2Request({method:"POST",params:{force:1},url:a?a.url:e.orderUrl,success:function(e,t){Ext.create("Proxmox.window.TaskViewer",{upid:e.result.data,taskDone:function(e){i.orderFinished(e,a)}}).show()},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})},orderFinished:function(e,t){e&&t.reloadUi&&(Ext.getBody().mask(gettext("API server will be restarted to use new certificates, please reload web-interface!"),["pve-static-mask"]),Ext.defer(()=>window.location.reload(!0),1e4))},reload:function(){this.getView().rstore.load()},addAccount:function(){let t=this;var e=t.getView();Ext.create("Proxmox.window.ACMEAccountCreate",{autoShow:!0,acmeUrl:e.acmeUrl,taskDone:function(){t.reload();var e=t.lookup("accountselector");t.autoChangeAccount=!0,e.store.load()}})}},tbar:[{xtype:"proxmoxButton",text:gettext("Add"),handler:"addDomain",selModel:!1},{xtype:"proxmoxButton",text:gettext("Edit"),disabled:!0,handler:"editDomain"},{xtype:"proxmoxStdRemoveButton",handler:"removeDomain"},"-","order-menu","-",{xtype:"displayfield",value:gettext("Using Account")+":",bind:{hidden:"{!accountsAvailable}"}},{xtype:"displayfield",reference:"accounttext",renderer:e=>e||Proxmox.Utils.NoneText,bind:{value:"{account}",hidden:"{accountTextHidden}"}},{xtype:"pmxACMEAccountSelector",hidden:!0,reference:"accountselector",cbind:{url:"{accountUrl}"},bind:{value:"{account}",hidden:"{accountValueHidden}"}},{xtype:"button",iconCls:"fa black fa-pencil",baseCls:"x-plain",userCls:"pointer",bind:{iconCls:"{editBtnIcon}",hidden:"{!accountsAvailable}"},handler:"toggleEditAccount"},{xtype:"displayfield",value:gettext("No Account available."),bind:{hidden:"{accountsAvailable}"}},{xtype:"button",hidden:!0,reference:"accountlink",text:gettext("Add ACME Account"),bind:{hidden:"{accountsAvailable}"},handler:"addAccount"}],updateStore:function(e,t,a){let i=[],o,n=(o=a&&0<t.length?t[0]:{data:{}},this.nodeconfig=o.data,"default");o.data.acme&&(((a=Proxmox.Utils.parseACME(o.data.acme)).domains||[]).forEach(e=>{""!==e&&(e={domain:e,type:"standalone",configkey:"acme"},i.push(e))}),a.account)&&(n=a.account);t=this.getViewModel();t.get("account")===n||t.get("accountEditable")||(t.set("configaccount",n),this.lookup("accountselector").store.load());for(let e=0;e<Proxmox.Utils.acmedomain_count;e++){var r=o.data["acmedomain"+e];r&&((r=Proxmox.Utils.parsePropertyString(r,"domain")).type=r.plugin?"dns":"standalone",r.configkey="acmedomain"+e,i.push(r))}t.set("domaincount",i.length),this.store.loadData(i,!1)},listeners:{itemdblclick:"editDomain"},columns:[{dataIndex:"domain",flex:5,text:gettext("Domain")},{dataIndex:"usage",flex:1,text:gettext("Usage"),bind:{hidden:"{!hasUsage}"}},{dataIndex:"type",flex:1,text:gettext("Type")},{dataIndex:"plugin",flex:1,text:gettext("Plugin")}],initComponent:function(){let e=this;if(!e.acmeUrl)throw"no acmeUrl given";if(!e.url)throw"no url given";if(!e.nodename)throw"no nodename given";if(!e.domainUsages&&!e.orderUrl)throw"neither domainUsages nor orderUrl given";if(e.rstore=Ext.create("Proxmox.data.UpdateStore",{interval:1e4,autoStart:!0,storeid:"proxmox-node-domains-"+e.nodename,proxy:{type:"proxmox",url:"/api2/json/"+e.url}}),e.store=Ext.create("Ext.data.Store",{model:"proxmox-acme-domains",sorters:"domain"}),e.domainUsages){var t=[];for(const a of e.domainUsages){if(!a.name)throw"missing certificate url";if(!a.url)throw"missing certificate url";t.push({text:Ext.String.format("Order {0} Certificate Now",a.name),handler:function(){return e.getController().order(a)}})}e.tbar.splice(e.tbar.indexOf("order-menu"),1,{text:gettext("Order Certificates Now"),menu:{xtype:"menu",items:t}})}else e.tbar.splice(e.tbar.indexOf("order-menu"),1,{xtype:"button",reference:"order",text:gettext("Order Certificates Now"),bind:{disabled:"{!canOrder}"},handler:function(){return e.getController().order()}});e.callParent(),e.getViewModel().set("hasUsage",!!e.domainUsages),e.mon(e.rstore,"load","updateStore",e),Proxmox.Utils.monStoreErrors(e,e.rstore),e.on("destroy",e.rstore.stopUpdate,e.rstore)}}),Ext.define("Proxmox.panel.EmailRecipientPanel",{extend:"Ext.panel.Panel",xtype:"pmxEmailRecipientPanel",mixins:["Proxmox.Mixin.CBind"],border:!1,mailValidator:function(){var e=this.down("[name=mailto-user]"),t=this.down("[name=mailto]");return!(!e.getValue()?.length&&!t.getValue())||gettext("Either mailto or mailto-user must be set")},items:[{layout:"anchor",border:!1,cbind:{isCreate:"{isCreate}"},items:[{xtype:"pmxUserSelector",name:"mailto-user",multiSelect:!0,allowBlank:!0,editable:!1,skipEmptyText:!0,fieldLabel:gettext("Recipient(s)"),cbind:{deleteEmpty:"{!isCreate}"},validator:function(){return this.up("pmxEmailRecipientPanel").mailValidator()},autoEl:{tag:"div","data-qtip":gettext("The notification will be sent to the user's configured mail address")},listConfig:{width:600,columns:[{header:gettext("User"),sortable:!0,dataIndex:"userid",renderer:Ext.String.htmlEncode,flex:1},{header:gettext("E-Mail"),sortable:!0,dataIndex:"email",renderer:Ext.String.htmlEncode,flex:1},{header:gettext("Comment"),sortable:!1,dataIndex:"comment",renderer:Ext.String.htmlEncode,flex:1}]}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Additional Recipient(s)"),name:"mailto",allowBlank:!0,emptyText:"<EMAIL>, ...",cbind:{deleteEmpty:"{!isCreate}"},autoEl:{tag:"div","data-qtip":gettext("Multiple recipients must be separated by spaces, commas or semicolons")},validator:function(){return this.up("pmxEmailRecipientPanel").mailValidator()}}]}]}),Ext.define("Proxmox.panel.SendmailEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxSendmailEditPanel",mixins:["Proxmox.Mixin.CBind"],type:"sendmail",onlineHelp:"notification_targets_sendmail",mailValidator:function(){var e=this.down("[name=mailto-user]"),t=this.down("[name=mailto]");return!(!e.getValue()?.length&&!t.getValue())||gettext("Either mailto or mailto-user must be set")},items:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0},{xtype:"pmxEmailRecipientPanel",cbind:{isCreate:"{isCreate}"}},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],advancedItems:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Author"),name:"author",allowBlank:!0,cbind:{emptyText:"{defaultMailAuthor}",deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("From Address"),name:"from-address",allowBlank:!0,emptyText:gettext("Defaults to datacenter configuration, or root@$hostname"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:e=>(e.enable=!e.disable,delete e.disable,e),onGetValues:function(e){return e.enable?this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,e.mailto&&(e.mailto=e.mailto.split(/[\s,;]+/)),e}}),Ext.define("Proxmox.panel.SmtpEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxSmtpEditPanel",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"notification_targets_smtp",type:"smtp",viewModel:{xtype:"viewmodel",cbind:{isCreate:"{isCreate}"},data:{mode:"tls",authentication:!0},formulas:{portEmptyText:function(e){let t;switch(e("mode")){case"insecure":t=25;break;case"starttls":t=587;break;case"tls":t=465}return`${Proxmox.Utils.defaultText} (${t})`},passwordEmptyText:function(e){var t=this.isCreate;return e("authentication")&&!t?gettext("Unchanged"):""}}},columnT:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0}],column1:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Server"),name:"server",allowBlank:!1,emptyText:gettext("mail.example.com")},{xtype:"proxmoxKVComboBox",name:"mode",fieldLabel:gettext("Encryption"),editable:!1,comboItems:[["insecure",Proxmox.Utils.noneText+" ("+gettext("insecure")+")"],["starttls","STARTTLS"],["tls","TLS"]],bind:"{mode}",cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxintegerfield",name:"port",fieldLabel:gettext("Port"),minValue:1,maxValue:65535,bind:{emptyText:"{portEmptyText}"},submitEmptyText:!1,cbind:{deleteEmpty:"{!isCreate}"}}],column2:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("Authenticate"),name:"authentication",bind:{value:"{authentication}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Username"),name:"username",allowBlank:!1,cbind:{deleteEmpty:"{!isCreate}"},bind:{disabled:"{!authentication}"}},{xtype:"proxmoxtextfield",inputType:"password",fieldLabel:gettext("Password"),name:"password",allowBlank:!0,bind:{disabled:"{!authentication}",emptyText:"{passwordEmptyText}"}}],columnB:[{xtype:"proxmoxtextfield",fieldLabel:gettext("From Address"),name:"from-address",allowBlank:!1,emptyText:gettext("<EMAIL>")},{xtype:"pmxEmailRecipientPanel",cbind:{isCreate:"{isCreate}"}},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],advancedColumnB:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Author"),name:"author",allowBlank:!0,cbind:{emptyText:"{defaultMailAuthor}",deleteEmpty:"{!isCreate}"}}],onGetValues:function(e){return e.mailto&&(e.mailto=e.mailto.split(/[\s,;]+/)),e.authentication||this.isCreate||(Proxmox.Utils.assemble_field_data(e,{delete:"username"}),Proxmox.Utils.assemble_field_data(e,{delete:"password"})),e.enable?this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,delete e.authentication,e},onSetValues:function(e){return e.authentication=!!e.username,e.enable=!e.disable,delete e.disable,e}}),Ext.define("Proxmox.panel.StatusView",{extend:"Ext.panel.Panel",alias:"widget.pmxStatusView",layout:{type:"column"},title:gettext("Status"),getRecordValue:function(e,t){if(e)return(t=(t=void 0===t?this.getStore():t).getById(e))?t.data.value:"";throw"no key given"},fieldRenderer:function(e,t){return void 0===t?e:Ext.isNumeric(t)&&1!==t?Proxmox.Utils.render_size_usage(e,t):Proxmox.Utils.render_usage(e)},fieldCalculator:function(e,t){return!Ext.isNumeric(t)&&Ext.isNumeric(e)?e:Ext.isNumeric(e)||void 0===e.used||void 0===e.total?e/t:0<e.total?e.used/e.total:0},updateField:function(t){var a=this;let i=a.fieldRenderer;if(Ext.isFunction(t.renderer)&&(i=t.renderer),!0===t.multiField)t.updateValue(i.call(t,a.getStore().getRecord()));else if(void 0!==t.textField)t.updateValue(i.call(t,a.getRecordValue(t.textField)));else if(void 0!==t.valueField){var o=a.getRecordValue(t.valueField),n=void 0!==t.maxField?a.getRecordValue(t.maxField):1;let e=a.fieldCalculator;Ext.isFunction(t.calculate)&&(e=t.calculate),t.updateValue(i.call(t,o,n),e(o,n))}},getStore:function(){if(this.rstore)return this.rstore;throw"there is no rstore"},updateTitle:function(){this.setTitle(this.getRecordValue("name"))},updateValues:function(e,t,a){var i=this;a&&(i.query("pmxInfoWidget").forEach(i.updateField,i),i.query("pveInfoWidget").forEach(i.updateField,i),i.updateTitle(e))},initComponent:function(){var e=this;if(!e.rstore)throw"no rstore given";if(!e.title)throw"no title given";Proxmox.Utils.monStoreErrors(e,e.rstore),e.callParent(),e.mon(e.rstore,"load",e.updateValues,e)}}),Ext.define("pmx-tfa-users",{extend:"Ext.data.Model",fields:["userid"],idProperty:"userid",proxy:{type:"proxmox",url:"/api2/json/access/tfa"}}),Ext.define("pmx-tfa-entry",{extend:"Ext.data.Model",fields:["fullid","userid","type","description","created","enable"],idProperty:"fullid"}),Ext.define("Proxmox.panel.TfaView",{extend:"Ext.grid.GridPanel",alias:"widget.pmxTfaView",mixins:["Proxmox.Mixin.CBind"],title:gettext("Second Factors"),reference:"tfaview",issuerName:"Proxmox",yubicoEnabled:!1,cbindData:function(e){return{yubicoEnabled:this.yubicoEnabled}},store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,model:"pmx-tfa-entry",rstore:{type:"store",proxy:"memory",storeid:"pmx-tfa-entry",model:"pmx-tfa-entry"}},controller:{xclass:"Ext.app.ViewController",init:function(e){e.tfaStore=Ext.create("Proxmox.data.UpdateStore",{autoStart:!0,interval:5e3,storeid:"pmx-tfa-users",model:"pmx-tfa-users"}),e.tfaStore.on("load",this.onLoad,this),e.on("destroy",e.tfaStore.stopUpdate),Proxmox.Utils.monStoreErrors(e,e.tfaStore)},reload:function(){this.getView().tfaStore.load()},onLoad:function(e,t,a){if(a){let e=(new Date).getTime()/1e3,o=[];Ext.Array.each(t,t=>{let a=(t.data["tfa-locked-until"]||0)>e,i=t.data["totp-locked"];Ext.Array.each(t.data.entries,e=>{o.push({fullid:t.id+"/"+e.id,userid:t.id,type:e.type,description:e.description,created:e.created,enable:e.enable,locked:a||"totp"===e.type&&i})})});a=this.getView().store.rstore;a.loadData(o),a.fireEvent("load",a,o,!0)}},addTotp:function(){let e=this;Ext.create("Proxmox.window.AddTotp",{isCreate:!0,issuerName:e.getView().issuerName,listeners:{destroy:function(){e.reload()}}}).show()},addWebauthn:function(){let e=this;Ext.create("Proxmox.window.AddWebauthn",{isCreate:!0,autoShow:!0,listeners:{destroy:()=>e.reload()}})},addRecovery:async function(){let e=this;Ext.create("Proxmox.window.AddTfaRecovery",{autoShow:!0,listeners:{destroy:()=>e.reload()}})},addYubico:function(){let e=this;Ext.create("Proxmox.window.AddYubico",{isCreate:!0,autoShow:!0,listeners:{destroy:()=>e.reload()}})},editItem:function(){let e=this;var t=e.getView().getSelection();1!==t.length||t[0].id.endsWith("/recovery")||Ext.create("Proxmox.window.TfaEdit",{"tfa-id":t[0].data.fullid,autoShow:!0,listeners:{destroy:()=>e.reload()}})},renderUser:e=>e.split("/")[0],renderEnabled:function(e,t,a){return a.data.locked?gettext("Locked"):void 0===e?Proxmox.Utils.yesText:Proxmox.Utils.format_boolean(e)},onRemoveButton:function(e,t,a){let i=this;Ext.create("Proxmox.tfa.confirmRemove",{...a.data,callback:e=>i.removeItem(e,a),autoShow:!0})},removeItem:async function(e,t){e=null!==e?"?password="+encodeURIComponent(e):"";try{this.getView().mask(gettext("Please wait..."),"x-mask-loading"),await Proxmox.Async.api2({url:"/api2/extjs/access/tfa/"+t.id+e,method:"DELETE"}),this.reload()}catch(e){Ext.Msg.alert(gettext("Error"),e.result.message)}finally{this.getView().unmask()}}},viewConfig:{trackOver:!1},listeners:{itemdblclick:"editItem"},columns:[{header:gettext("User"),width:200,sortable:!0,dataIndex:"fullid",renderer:"renderUser"},{header:gettext("Enabled"),width:80,sortable:!0,dataIndex:"enable",renderer:"renderEnabled"},{header:gettext("TFA Type"),width:80,sortable:!0,dataIndex:"type"},{header:gettext("Created"),width:150,sortable:!0,dataIndex:"created",renderer:e=>e?Proxmox.Utils.render_timestamp(e):"N/A"},{header:gettext("Description"),width:300,sortable:!0,dataIndex:"description",renderer:Ext.String.htmlEncode,flex:1}],tbar:[{text:gettext("Add"),cbind:{},menu:{xtype:"menu",items:[{text:gettext("TOTP"),itemId:"totp",iconCls:"fa fa-fw fa-clock-o",handler:"addTotp"},{text:gettext("WebAuthn"),itemId:"webauthn",iconCls:"fa fa-fw fa-shield",handler:"addWebauthn"},{text:gettext("Recovery Keys"),itemId:"recovery",iconCls:"fa fa-fw fa-file-text-o",handler:"addRecovery"},{text:gettext("Yubico OTP"),itemId:"yubico",iconCls:"fa fa-fw fa-yahoo",handler:"addYubico",cbind:{hidden:"{!yubicoEnabled}"}}]}},"-",{xtype:"proxmoxButton",text:gettext("Edit"),handler:"editItem",enableFn:e=>!e.id.endsWith("/recovery"),disabled:!0},{xtype:"proxmoxButton",disabled:!0,text:gettext("Remove"),getRecordName:e=>e.data.description,handler:"onRemoveButton"}]}),Ext.define("Proxmox.panel.NotesView",{extend:"Ext.panel.Panel",xtype:"pmxNotesView",mixins:["Proxmox.Mixin.CBind"],title:gettext("Notes"),bodyPadding:10,scrollable:!0,animCollapse:!1,collapseFirst:!1,maxLength:65536,enableTBar:!1,onlineHelp:"markdown_basics",tbar:{itemId:"tbar",hidden:!0,items:[{text:gettext("Edit"),iconCls:"fa fa-pencil-square-o",handler:function(){this.up("panel").run_editor()}}]},cbindData:function(e){var t=this;let a="";if(t.node)t.url=`/api2/extjs/nodes/${t.node}/config`;else if("root"===t.pveSelNode?.data?.id)t.url="/api2/extjs/cluster/options",a=t.pveSelNode?.data?.type;else{var i=t.pveSelNode?.data?.node;if(a=t.pveSelNode?.data?.type,!i)throw"no node name specified";if(!Ext.Array.contains(["node","qemu","lxc"],a))throw"invalid type specified";var o=t.pveSelNode?.data?.vmid;if(!o&&"node"!==a)throw"no VM ID specified";t.url=`/api2/extjs/nodes/${i}/`,"qemu"!==a&&"lxc"!==a||(t.url+=a+`/${o}/`,t.maxLength=8192),t.url+="config"}return t.pveType=a,t.load(),{}},run_editor:function(){let e=this;Ext.create("Proxmox.window.NotesEdit",{url:e.url,onlineHelp:e.onlineHelp,listeners:{destroy:()=>e.load()},autoShow:!0}).setMaxLength(e.maxLength)},setNotes:function(e=""){var t=Proxmox.Markdown.parse(e);this.update(t),this.collapsible&&"auto"===this.collapseMode&&this.setCollapsed(!e)},load:function(){let a=this;Proxmox.Utils.API2Request({url:a.url,waitMsgTarget:a,failure:(e,t)=>{a.update(gettext("Error")+" "+e.htmlStatus),a.setCollapsed(!1)},success:({result:e})=>a.setNotes(e.data.description)})},listeners:{render:function(e){let i=this;var t=Ext.state.Manager.getProvider();i.mon(t,"statechange",function(e,t,a){null!==a&&"edit-notes-on-double-click"===t&&(a?i.getEl().on("dblclick",i.run_editor,i):i.getEl().clearListeners())}),t.get("edit-notes-on-double-click",!1)&&i.getEl().on("dblclick",i.run_editor,i)},afterlayout:function(){var e=this;e.collapsible&&!e.getCollapsed()&&"always"===e.collapseMode&&(e.setCollapsed(!0),e.collapseMode="")}},tools:[{glyph:"xf044@FontAwesome",tooltip:gettext("Edit notes"),callback:e=>e.run_editor(),style:{paddingRight:"5px"}}],initComponent:function(){var e,t=this;t.callParent(),!0===t.enableTBar||"node"===t.pveType||""===t.pveType?t.down("#tbar").setVisible(!0):1!==t.pveSelNode?.data?.template&&(t.setCollapsible(!0),t.collapseDirection="right",e=Ext.state.Manager.getProvider(),t.collapseMode=e.get("guest-notes-collapse","never"),"auto"===t.collapseMode)&&t.setCollapsed(!0)}}),Ext.define("Proxmox.panel.WebhookEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxWebhookEditPanel",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"notification_targets_webhook",type:"webhook",columnT:[],column1:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),regex:Proxmox.Utils.safeIdRegex,allowBlank:!1}],column2:[{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0}],columnB:[{xtype:"fieldcontainer",fieldLabel:gettext("Method/URL"),layout:"hbox",border:!1,margin:"0 0 5 0",items:[{xtype:"proxmoxKVComboBox",name:"method",editable:!1,value:"post",comboItems:[["post","POST"],["put","PUT"],["get","GET"]],width:80,margin:"0 5 0 0"},{xtype:"proxmoxtextfield",name:"url",allowBlank:!1,emptyText:"https://example.com/hook",regex:Proxmox.Utils.httpUrlRegex,regexText:gettext("Must be a valid URL"),flex:4}]},{xtype:"pmxWebhookKeyValueList",name:"header",fieldLabel:gettext("Headers"),addLabel:gettext("Add Header"),maskValues:!1,cbind:{isCreate:"{isCreate}"},margin:"0 0 10 0"},{xtype:"textarea",fieldLabel:gettext("Body"),name:"body",allowBlank:!0,minHeight:"150",fieldStyle:{"font-family":"monospace"},margin:"0 0 5 0"},{xtype:"pmxWebhookKeyValueList",name:"secret",fieldLabel:gettext("Secrets"),addLabel:gettext("Add Secret"),maskValues:!0,cbind:{isCreate:"{isCreate}"},margin:"0 0 10 0"},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:e=>(e.enable=!e.disable,e.body&&(e.body=Proxmox.Utils.base64ToUtf8(e.body)),delete e.disable,e),onGetValues:function(e){return e.enable?this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,e.body?e.body=Proxmox.Utils.utf8ToBase64(e.body):(delete e.body,this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"body"})),Ext.isArray(e.header)&&!e.header.length&&(delete e.header,this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"header"})),Ext.isArray(e.secret)&&!e.secret.length&&(delete e.secret,this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"secret"})),delete e.enable,e}}),Ext.define("Proxmox.form.WebhookKeyValueList",{extend:"Ext.form.FieldContainer",alias:"widget.pmxWebhookKeyValueList",mixins:["Ext.form.field.Field"],fieldTitle:gettext("Item"),addLabel:void 0,maskRe:void 0,allowBlank:!0,selectAll:!1,isFormField:!0,deleteEmpty:!1,config:{deleteEmpty:!1,maskValues:!1},setValue:function(e){let a=this;e=Ext.isArray(e)?e:(e??"").split(";").filter(e=>""!==e);var t=a.lookup("grid").getStore();return 0<e.length?t.setData(e.map(e=>{var e=Proxmox.Utils.parsePropertyString(e),t=a.maskValues?"":Proxmox.Utils.base64ToUtf8(e.value),e={headerName:e.name,headerValue:t};return!a.isCreate&&a.maskValues&&(e.emptyText=gettext("Unchanged")),e})):t.removeAll(),a.checkChange(),a},getValue:function(){let t=[];return this.lookup("grid").getStore().each(e=>{e.data.headerName&&(e={name:e.data.headerName,value:Proxmox.Utils.utf8ToBase64(e.data.headerValue)},t.push(Proxmox.Utils.printPropertyString(e)))}),t},getErrors:function(e){let t=this,a=!1;return t.lookup("grid").getStore().each(e=>{e.data.headerName||(a=!0),!e.data.headerValue&&e.data.newValue&&(a=!0),e.data.headerValue||t.maskValues||(a=!0)}),a?[gettext("Name/value must not be empty.")]:[]},getSubmitData:function(){let e=this,t=null,a;return!e.disabled&&e.submitValue&&(null!==(a=e.getValue())&&""!==a?(t={})[e.getName()]=a:e.getDeleteEmpty()&&((t={}).delete=e.getName())),t},controller:{xclass:"Ext.app.ViewController",addLine:function(){this.lookup("grid").getStore().add({headerName:"",headerValue:"",emptyText:gettext("Value"),newValue:!0})},removeSelection:function(e){var t=this.getView(),a=this.lookup("grid"),e=e.getWidgetRecord();void 0!==e&&(a.getStore().remove(e),t.checkChange(),t.validate())},itemChange:function(e,t){var a,i=e.getWidgetRecord();i&&(a=e.getWidgetColumn(),i.set(a.dataIndex,t),(i=e.up("pmxWebhookKeyValueList")).checkChange(),i.validate())},control:{"grid button":{click:"removeSelection"}}},initComponent:function(){var e,t,a=this,i=[{xtype:"grid",reference:"grid",minHeight:100,maxHeight:100,scrollable:"vertical",viewConfig:{deferEmptyText:!1},store:{listeners:{update:function(){this.commitChanges()}}},margin:"5 0 5 0",columns:[{header:a.fieldTtitle,dataIndex:"headerName",xtype:"widgetcolumn",widget:{xtype:"textfield",isFormField:!1,maskRe:a.maskRe,allowBlank:!1,queryMode:"local",emptyText:gettext("Key"),listeners:{change:"itemChange"}},onWidgetAttach:function(e,t){t.isValid()},flex:1},{header:a.fieldTtitle,dataIndex:"headerValue",xtype:"widgetcolumn",widget:{xtype:"proxmoxtextfield",inputType:a.maskValues?"password":"text",isFormField:!1,maskRe:a.maskRe,queryMode:"local",listeners:{change:"itemChange"},allowBlank:!a.isCreate&&a.maskValues,bind:{emptyText:"{record.emptyText}"}},onWidgetAttach:function(e,t){t.isValid()},flex:1},{xtype:"widgetcolumn",width:40,widget:{xtype:"button",iconCls:"fa fa-trash-o"}}]},{xtype:"button",text:a.addLabel||gettext("Add"),iconCls:"fa fa-plus-circle",handler:"addLine"}];for([e,t]of Object.entries(a.gridConfig??{}))i[0][e]=t;Ext.apply(a,{items:i}),a.callParent(),a.initField()}}),Ext.define("Proxmox.window.Edit",{extend:"Ext.window.Window",alias:"widget.proxmoxWindowEdit",autoLoad:!1,autoLoadOptions:void 0,extraRequestParams:{},resizable:!1,subject:void 0,isCreate:!1,isAdd:!1,isRemove:!1,showReset:!0,submitText:void 0,submitOptions:{},backgroundDelay:0,submitUrl:Ext.identityFn,loadUrl:Ext.identityFn,referenceHolder:!0,defaultButton:"submitbutton",defaultFocus:"field:focusable[disabled=false][hidden=false]",showProgress:!1,showTaskViewer:!1,taskDone:Ext.emptyFn,apiCallDone:Ext.emptyFn,onlineHelp:void 0,constructor:function(e){var t=this;t.extraRequestParams=Object.assign({},t.extraRequestParams),t.submitOptions=Object.assign({},t.submitOptions),t.callParent(arguments)},isValid:function(){return this.formPanel.getForm().isValid()},getValues:function(t){let a={};return Ext.apply(a,this.extraRequestParams),this.formPanel.getForm().getFields().each(function(e){e.up("inputpanel")||t&&!e.isDirty()||Proxmox.Utils.assemble_field_data(a,e.getSubmitData())}),Ext.Array.each(this.query("inputpanel"),function(e){Proxmox.Utils.assemble_field_data(a,e.getValues(t))}),a},setValues:function(t){let i=this.formPanel.getForm(),e=i.getFields();Ext.iterate(t,function(t,a){e.filterBy(e=>(e.id===t||e.name===t||e.dataIndex===t)&&!e.up("inputpanel")).each(e=>{e.setValue(a),i.trackResetOnLoad&&e.resetOriginalValue()})}),Ext.Array.each(this.query("inputpanel"),function(e){e.setValues(t)})},setSubmitText:function(e){this.lookup("submitbutton").setText(e)},submit:function(){let i=this,a=i.formPanel.getForm(),o=i.getValues(),e=(Ext.Object.each(o,function(e,t){Object.prototype.hasOwnProperty.call(o,e)&&Ext.isArray(t)&&!t.length&&(o[e]="")}),i.digest&&(o.digest=i.digest),i.backgroundDelay&&(o.background_delay=i.backgroundDelay),Ext.isFunction(i.submitUrl)?i.submitUrl(i.url,o):i.submitUrl||i.url);"DELETE"===i.method&&(e=e+"?"+Ext.Object.toQueryString(o),o=void 0);var t=Ext.apply({url:e,waitMsgTarget:i,method:i.method||(i.backgroundDelay?"POST":"PUT"),params:o,failure:function(e,t){i.apiCallDone(!1,e,t),e.result&&e.result.errors&&a.markInvalid(e.result.errors),Ext.Msg.alert(gettext("Error"),e.htmlStatus)},success:function(e,t){var a=(i.backgroundDelay||i.showProgress||i.showTaskViewer)&&e.result.data;i.apiCallDone(!0,e,t),a?(i.hide(),t=e.result.data,a=i.showTaskViewer?"Viewer":"Progress",Ext.create("Proxmox.window.Task"+a,{autoShow:!0,upid:t,taskDone:i.taskDone,listeners:{destroy:function(){i.close()}}})):i.close()}},i.submitOptions??{});Proxmox.Utils.API2Request(t)},load:function(e){let a=this,i=a.formPanel.getForm(),t=(e=e||{},Ext.apply({waitMsgTarget:a},e));var o;0<Object.keys(a.extraRequestParams).length&&(o=t.params||{},Ext.applyIf(o,a.extraRequestParams),t.params=o);let n=Ext.isFunction(a.loadUrl)?a.loadUrl(a.url,a.initialConfig):a.loadUrl||a.url;var r;r=e.success,Ext.apply(t,{url:n,method:"GET",success:function(e,t){i.clearInvalid(),a.digest=e.result?.digest||e.result?.data?.digest,r?r(e,t):a.setValues(e.result.data),Ext.Array.each(a.query("radiofield"),e=>e.resetOriginalValue())},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus,function(){a.close()})}}),Proxmox.Utils.API2Request(t)},initComponent:function(){let i=this;if(!(i.url||i.submitUrl&&i.loadUrl&&i.submitUrl!==Ext.identityFn&&i.loadUrl!==Ext.identityFn))throw"neither 'url' nor both, submitUrl and loadUrl specified";if(i.create)throw"deprecated parameter, use isCreate";var e=Ext.isArray(i.items)?i.items:[i.items];i.items=void 0,i.formPanel=Ext.create("Ext.form.Panel",{url:i.url,method:i.method||"PUT",trackResetOnLoad:!0,bodyPadding:void 0!==i.bodyPadding?i.bodyPadding:10,border:!1,defaults:Ext.apply({},i.defaults,{border:!1}),fieldDefaults:Ext.apply({},i.fieldDefaults,{labelWidth:100,anchor:"100%"}),items:e});let o=i.formPanel.down("inputpanel"),a=i.formPanel.getForm(),t,n=(t=i.isCreate?i.submitText||(i.isAdd?gettext("Add"):i.isRemove?gettext("Remove"):gettext("Create")):i.submitText||gettext("OK"),Ext.create("Ext.Button",{reference:"submitbutton",text:t,disabled:!i.isCreate,handler:function(){i.submit()}})),r=Ext.create("Ext.panel.Tool",{glyph:"xf0e2@FontAwesome",tooltip:gettext("Reset form data"),callback:()=>a.reset(),style:{paddingRight:"2px"},disabled:!0});function l(){var e=a.isValid(),t=a.isDirty();n.setDisabled(!e||!(t||i.isCreate)),r.setDisabled(!t)}a.on("dirtychange",l),a.on("validitychange",l);let s=300;i.fieldDefaults&&i.fieldDefaults.labelWidth&&(s+=i.fieldDefaults.labelWidth-100);e=o&&(o.column1||o.column2);if(i.subject&&!i.title&&(i.title=Proxmox.Utils.dialog_title(i.subject,i.isCreate,i.isAdd)),i.buttons=[n],!i.isCreate&&i.showReset&&(i.tools=[r]),o&&o.hasAdvanced){let a=Ext.state.Manager.getProvider();var d=a.get("proxmox-advanced-cb");o.setAdvancedVisible(d),i.buttons.unshift({xtype:"proxmoxcheckbox",itemId:"advancedcb",boxLabelAlign:"before",boxLabel:gettext("Advanced"),stateId:"proxmox-advanced-cb",value:d,listeners:{change:function(e,t){o.setAdvancedVisible(t),a.set("proxmox-advanced-cb",t)}}})}let u=i.onlineHelp;(u=!u&&o&&o.onlineHelp?o.onlineHelp:u)&&(d=Ext.create("Proxmox.button.Help"),i.buttons.unshift(d,"->"),Ext.GlobalEvents.fireEvent("proxmoxShowHelp",u)),Ext.applyIf(i,{modal:!0,width:e?2*s:s,border:!1,items:[i.formPanel]}),i.callParent(),o?.hasAdvanced&&o.down("#advancedContainer").query("field").forEach(function(e){i.mon(e,"validitychange",(e,t)=>{t||e.up("inputpanel").setAdvancedVisible(!0)})}),i.on("afterlayout",function(){i.suspendLayout=!0,i.isValid(),i.suspendLayout=!1}),i.autoLoad&&i.load(i.autoLoadOptions)}}),Ext.define("Proxmox.window.PasswordEdit",{extend:"Proxmox.window.Edit",alias:"proxmoxWindowPasswordEdit",mixins:["Proxmox.Mixin.CBind"],subject:gettext("Password"),url:"/api2/extjs/access/password",width:380,fieldDefaults:{labelWidth:150},minLength:5,confirmCurrentPassword:!1,hintHtml:void 0,items:[{xtype:"textfield",inputType:"password",fieldLabel:gettext("Your Current Password"),reference:"confirmation-password",name:"confirmation-password",allowBlank:!1,vtype:"password",cbind:{hidden:"{!confirmCurrentPassword}",disabled:"{!confirmCurrentPassword}"}},{xtype:"textfield",inputType:"password",fieldLabel:gettext("New Password"),allowBlank:!1,name:"password",listeners:{change:e=>e.next().validate(),blur:e=>e.next().validate()},cbind:{minLength:"{minLength}"}},{xtype:"textfield",inputType:"password",fieldLabel:gettext("Confirm New Password"),name:"verifypassword",allowBlank:!1,vtype:"password",initialPassField:"password",submitValue:!1},{xtype:"component",userCls:"pmx-hint",name:"password-hint",hidden:!0,cbind:{html:"{hintHtml}",hidden:"{!hintHtml}"}},{xtype:"hiddenfield",name:"userid",cbind:{value:"{userid}"}}]}),Ext.define("Proxmox.window.SafeDestroy",{extend:"Ext.window.Window",alias:"widget.proxmoxSafeDestroy",title:gettext("Confirm"),modal:!0,buttonAlign:"center",bodyPadding:10,width:450,layout:{type:"hbox"},defaultFocus:"confirmField",showProgress:!1,additionalItems:[],taskDone:Ext.emptyFn,apiCallDone:Ext.emptyFn,config:{item:{id:void 0,formattedIdentifier:void 0},url:void 0,note:void 0,taskName:void 0,params:{}},getParams:function(){return Ext.Object.isEmpty(this.params)?"":"?"+Ext.Object.toQueryString(this.params)},controller:{xclass:"Ext.app.ViewController",control:{"field[name=confirm]":{change:function(e,t){var a=this.getView(),i=this.lookupReference("removeButton");t===a.getItem().id.toString()?i.enable():i.disable()},specialkey:function(e,t){var a=this.lookupReference("removeButton");a.isDisabled()||t.getKey()!==t.ENTER||a.fireEvent("click",a,t)}},"button[reference=removeButton]":{click:function(){const i=this.getView();Proxmox.Utils.API2Request({url:i.getUrl()+i.getParams(),method:"DELETE",waitMsgTarget:i,failure:function(e,t){i.apiCallDone(!1,e,t),i.close(),Ext.Msg.alert("Error",e.htmlStatus)},success:function(e,t){var a=!(!i.showProgress||!e.result.data);i.apiCallDone(!0,e,t),a?(i.hide(),t=e.result.data,Ext.create("Proxmox.window.TaskProgress",{upid:t,taskDone:i.taskDone,listeners:{destroy:function(){i.close()}}}).show()):i.close()}})}}}},buttons:[{reference:"removeButton",text:gettext("Remove"),disabled:!0}],initComponent:function(){var e=this,t=(e.items=[{xtype:"component",cls:[Ext.baseCSSPrefix+"message-box-icon",Ext.baseCSSPrefix+"message-box-warning",Ext.baseCSSPrefix+"dlg-icon"]},{xtype:"container",flex:1,layout:{type:"vbox",align:"stretch"},items:[{xtype:"component",reference:"messageCmp"},{itemId:"confirmField",reference:"confirmField",xtype:"textfield",name:"confirm",labelWidth:300,hideTrigger:!0,allowBlank:!1}].concat(e.additionalItems).concat([{xtype:"container",reference:"noteContainer",flex:1,hidden:!0,layout:{type:"vbox"},items:[{xtype:"component",reference:"noteCmp",userCls:"pmx-hint"}]}])}],e.callParent(),e.getItem().id);if(!Ext.isDefined(t))throw"no ID specified";Ext.isDefined(e.getNote())&&(e.lookupReference("noteCmp").setHtml(`<span title="${e.getNote()}">${e.getNote()}</span>`),(a=e.lookupReference("noteContainer")).setHidden(!1),a.setDisabled(!1));var a=e.getTaskName();if(!Ext.isDefined(a))throw"no task name specified";e.lookupReference("messageCmp").setHtml(Ext.htmlEncode(Proxmox.Utils.format_task_description(a,e.getItem().formattedIdentifier??t)));a=gettext("Please enter the ID to confirm")+` (${t})`;e.lookupReference("confirmField").setFieldLabel(Ext.htmlEncode(a))}}),Ext.define("Proxmox.window.PackageVersions",{extend:"Ext.window.Window",alias:"widget.proxmoxPackageVersions",title:gettext("Package versions"),width:600,height:650,layout:"fit",modal:!0,url:"/nodes/localhost/apt/versions",viewModel:{parent:null,data:{packageList:""}},buttons:[{xtype:"button",text:gettext("Copy"),iconCls:"fa fa-clipboard",handler:function(e){window.getSelection().selectAllChildren(document.getElementById("pkgversions")),document.execCommand("copy")}},{text:gettext("Ok"),handler:function(){this.up("window").close()}}],items:[{xtype:"component",autoScroll:!0,id:"pkgversions",padding:5,bind:{html:"{packageList}"},style:{"white-space":"pre","font-family":"monospace"}}],listeners:{afterrender:function(){this.loadPackageVersions()}},loadPackageVersions:async function(){var e=(await Proxmox.Async.api2({waitMsgTarget:this.down('component[id="pkgversions"]'),method:"GET",url:this.url}).catch(Proxmox.Utils.alertResponseFailure))["result"];let t="";for(const i of e.data){let e="not correctly installed";i.OldVersion&&"unknown"!==i.OldVersion?e=i.OldVersion:"ConfigFiles"===i.CurrentState&&(e="residual config");var a=i.Package;i.ExtraInfo?t+=`${a}: ${e} (${i.ExtraInfo})\n`:t+=`${a}: ${e}\n`}this.getViewModel().set("packageList",Ext.htmlEncode(t))}}),Ext.define("Proxmox.window.TaskProgress",{extend:"Ext.window.Window",alias:"widget.proxmoxTaskProgress",taskDone:Ext.emptyFn,width:300,layout:"auto",modal:!0,bodyPadding:5,initComponent:function(){let t=this;if(!t.upid)throw"no task specified";var e=Proxmox.Utils.parse_task_upid(t.upid);function a(e,t){return(e=i.getById(e))?e.data.value:t}let i=Ext.create("Proxmox.data.ObjectStore",{url:`/api2/json/nodes/${e.node}/tasks/${encodeURIComponent(t.upid)}/status`,interval:1e3,rows:{status:{defaultValue:"unknown"},exitstatus:{defaultValue:"unknown"}}}),o=(t.on("destroy",i.stopUpdate),Ext.create("Ext.ProgressBar"));t.mon(i,"load",function(){var e;"stopped"===a("status")&&("OK"===(e=a("exitstatus"))?(o.reset(),o.updateText("Done!"),Ext.Function.defer(t.close,1e3,t)):(t.close(),Ext.Msg.alert("Task failed",Ext.htmlEncode(e))),t.taskDone("OK"===e))});e=Ext.htmlEncode(Proxmox.Utils.format_task_description(e.type,e.id));Ext.apply(t,{title:gettext("Task")+": "+e,items:o,buttons:[{text:gettext("Details"),handler:function(){Ext.create("Proxmox.window.TaskViewer",{autoShow:!0,taskDone:t.taskDone,upid:t.upid}),t.close()}}]}),t.callParent(),i.startUpdate(),o.wait({text:gettext("running...")})}}),Ext.define("Proxmox.window.TaskViewer",{extend:"Ext.window.Window",alias:"widget.proxmoxTaskViewer",extraTitle:"",taskDone:Ext.emptyFn,initComponent:function(){let a=this;if(!a.upid)throw"no task specified";let e=Proxmox.Utils.parse_task_upid(a.upid),i;var t={status:{header:gettext("Status"),defaultValue:"unknown",renderer:function(e){var t;return"stopped"!==e?Ext.htmlEncode(e):(t=i.getObjectValue("exitstatus"))?Ext.htmlEncode(e+": "+t):"unknown"}},exitstatus:{visible:!1,renderer:Ext.String.htmlEncode},type:{header:gettext("Task type"),required:!0,renderer:Ext.String.htmlEncode},user:{header:gettext("User name"),renderer:function(e){let t=e;e=i.getObjectValue("tokenid");return e&&(t+=`!${e} (API Token)`),Ext.String.htmlEncode(t)},required:!0},tokenid:{header:gettext("API Token"),renderer:Ext.String.htmlEncode,visible:!1},node:{header:gettext("Node"),required:!0,renderer:Ext.String.htmlEncode},pid:{header:gettext("Process ID"),required:!0,renderer:Ext.String.htmlEncode},task_id:{header:gettext("Task ID"),renderer:Ext.String.htmlEncode},starttime:{header:gettext("Start Time"),required:!0,renderer:Proxmox.Utils.render_timestamp},upid:{header:gettext("Unique task ID"),renderer:Ext.String.htmlEncode}};a.endtime&&("object"==typeof a.endtime&&(a.endtime=parseInt(a.endtime.getTime()/1e3,10)),t.endtime={header:gettext("End Time"),required:!0,renderer:function(){return Proxmox.Utils.render_timestamp(a.endtime)}}),t.duration={header:gettext("Duration"),required:!0,renderer:function(){var e=i.getObjectValue("starttime"),t=a.endtime||Date.now()/1e3;return Proxmox.Utils.format_duration_human(t-e)}};let o=Ext.create("Proxmox.data.ObjectStore",{url:`/api2/json/nodes/${e.node}/tasks/${encodeURIComponent(a.upid)}/status`,interval:1e3,rows:t});a.on("destroy",o.stopUpdate);function n(){Proxmox.Utils.API2Request({url:`/nodes/${e.node}/tasks/`+encodeURIComponent(a.upid),waitMsgTarget:a,method:"DELETE",failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})}let r=new Ext.Button({text:gettext("Stop"),disabled:!0,handler:n}),l=new Ext.Button({text:gettext("Stop"),disabled:!0,handler:n}),s=(i=Ext.create("Proxmox.grid.ObjectGrid",{title:gettext("Status"),layout:"fit",tbar:[r],rstore:o,rows:t,border:!1}),new Ext.Button({text:gettext("Download"),iconCls:"fa fa-download",handler:()=>Proxmox.Utils.downloadAsFile(`/api2/json/nodes/${e.node}/tasks/${encodeURIComponent(a.upid)}/log?download=1`)})),d=Ext.create("Proxmox.panel.LogView",{title:gettext("Output"),tbar:[l,"->",s],border:!1,url:`/api2/extjs/nodes/${e.node}/tasks/${encodeURIComponent(a.upid)}/log`});a.mon(o,"load",function(){var e=i.getObjectValue("status");"stopped"===e&&(d.scrollToEnd=!1,d.requestUpdate(),o.stopUpdate(),a.taskDone("OK"===i.getObjectValue("exitstatus"))),r.setDisabled("running"!==e),l.setDisabled("running"!==e),s.setDisabled("running"===e)}),o.startUpdate(),Ext.apply(a,{title:Ext.htmlEncode("Task viewer: "+e.desc+a.extraTitle),width:800,height:500,layout:"fit",modal:!0,items:[{xtype:"tabpanel",region:"center",items:[d,i]}]}),a.callParent(),d.fireEvent("show",d)}}),Ext.define("Proxmox.window.LanguageEditWindow",{extend:"Ext.window.Window",alias:"widget.pmxLanguageEditWindow",viewModel:{parent:null,data:{language:"__default__"}},controller:{xclass:"Ext.app.ViewController",init:function(e){let t=Ext.util.Cookies.get(e.cookieName)||"__default__";var a;"kr"===t&&(t="ko",a=Ext.Date.add(new Date,Ext.Date.YEAR,10),Ext.util.Cookies.set(e.cookieName,t,a)),this.getViewModel().set("language",t)},applyLanguage:function(e){var t=this.getView(),a=this.getViewModel(),i=Ext.Date.add(new Date,Ext.Date.YEAR,10);Ext.util.Cookies.set(t.cookieName,a.get("language"),i),t.mask(gettext("Please wait..."),"x-mask-loading"),window.location.reload()}},cookieName:"PVELangCookie",title:gettext("Language"),modal:!0,bodyPadding:10,resizable:!1,items:[{xtype:"proxmoxLanguageSelector",fieldLabel:gettext("Language"),labelWidth:75,bind:{value:"{language}"}}],buttons:[{text:gettext("Apply"),handler:"applyLanguage"}]}),Ext.define("Proxmox.window.DiskSmart",{extend:"Ext.window.Window",alias:"widget.pmxSmartWindow",modal:!0,layout:{type:"fit"},width:800,height:500,minWidth:400,minHeight:300,bodyPadding:5,items:[{xtype:"gridpanel",layout:{type:"fit"},emptyText:gettext("No S.M.A.R.T. Values"),scrollable:!0,flex:1,itemId:"smartGrid",reserveScrollbar:!0,columns:[{text:"ID",dataIndex:"id",width:50,align:"right"},{text:gettext("Attribute"),dataIndex:"name",flex:1,renderer:Ext.String.htmlEncode},{text:gettext("Value"),dataIndex:"real-value",renderer:Ext.String.htmlEncode},{text:gettext("Normalized"),dataIndex:"real-normalized",width:60,align:"right"},{text:gettext("Threshold"),dataIndex:"threshold",width:60,align:"right"},{text:gettext("Worst"),dataIndex:"worst",width:60,align:"right"},{text:gettext("Flags"),dataIndex:"flags"},{text:gettext("Failing"),dataIndex:"fail",renderer:Ext.String.htmlEncode}]},{xtype:"component",itemId:"smartPlainText",hidden:!0,autoScroll:!0,padding:5,style:{"white-space":"pre","font-family":"monospace"}}],buttons:[{text:gettext("Reload"),name:"reload",handler:function(){this.up("window").store.reload()}},{text:gettext("Close"),name:"close",handler:function(){this.up("window").close()}}],initComponent:function(){var e=this;if(!e.baseurl)throw"no baseurl specified";if(!e.dev)throw"no device specified";e.title=`${gettext("S.M.A.R.T. Values")} (${e.dev})`,e.store=Ext.create("Ext.data.Store",{model:"pmx-disk-smart",proxy:{type:"proxmox",url:e.baseurl+"/smart?disk="+e.dev}}),e.callParent();let i=e.down("#smartGrid"),o=e.down("#smartPlainText");Proxmox.Utils.monStoreErrors(i,e.store),e.mon(e.store,"load",function(e,t,a){!a||t.length<=0||((a="text"===t[0].data.type)?o.setHtml(Ext.String.htmlEncode(t[0].data.text)):i.setStore(t[0].attributes()),i.setVisible(!a),o.setVisible(a))}),e.store.load()}},function(){Ext.define("pmx-disk-smart",{extend:"Ext.data.Model",fields:[{name:"health"},{name:"type"},{name:"text"}],hasMany:{model:"pmx-smart-attribute",name:"attributes"}}),Ext.define("pmx-smart-attribute",{extend:"Ext.data.Model",fields:[{name:"id",type:"number"},"name","value","worst","threshold","flags","fail","raw","normalized",{name:"real-value",calculate:e=>e.raw??e.value},{name:"real-normalized",calculate:e=>e.normalized??e.value}],idProperty:"name"})}),Ext.define("Proxmox.window.ZFSDetail",{extend:"Ext.window.Window",alias:"widget.pmxZFSDetail",mixins:["Proxmox.Mixin.CBind"],cbindData:function(e){var t=this;return t.url=`/nodes/${t.nodename}/disks/zfs/`+encodeURIComponent(t.zpool),{zpoolUri:"/api2/json/"+t.url,title:gettext("Status")+": "+t.zpool}},controller:{xclass:"Ext.app.ViewController",reload:function(){let i=this,a=i.getView();i.lookup("status").reload(),Proxmox.Utils.API2Request({url:"/api2/extjs/"+a.url,waitMsgTarget:a,method:"GET",failure:function(e,t){Proxmox.Utils.setErrorMask(a,e.htmlStatus)},success:function(e,t){var a=i.lookup("devices");a.getSelectionModel().deselectAll(),a.setRootNode(e.result.data),a.expandAll()}})},init:function(e){Proxmox.Utils.monStoreErrors(this,this.lookup("status").getStore().rstore),this.reload()}},modal:!0,width:800,height:600,resizable:!0,cbind:{title:"{title}"},layout:{type:"vbox",align:"stretch"},defaults:{layout:"fit",border:!1},tbar:[{text:gettext("Reload"),iconCls:"fa fa-refresh",handler:"reload"}],items:[{xtype:"proxmoxObjectGrid",reference:"status",flex:0,cbind:{url:"{zpoolUri}",nodename:"{nodename}"},rows:{state:{header:gettext("Health"),renderer:Proxmox.Utils.render_zfs_health},scan:{header:gettext("Scan")},status:{header:gettext("Status")},action:{header:gettext("Action")},errors:{header:gettext("Errors")}}},{xtype:"treepanel",reference:"devices",title:gettext("Devices"),stateful:!0,stateId:"grid-node-zfsstatus",rootVisible:!1,fields:["name","status",{type:"string",name:"iconCls",calculate:function(e){if(e.leaf)return"fa x-fa-tree fa-hdd-o"}}],sorters:"name",flex:1,cbind:{zpool:"{zpoolUri}",nodename:"{nodename}"},columns:[{xtype:"treecolumn",text:gettext("Name"),dataIndex:"name",flex:1},{text:gettext("Health"),renderer:Proxmox.Utils.render_zfs_health,dataIndex:"state"},{text:"READ",dataIndex:"read"},{text:"WRITE",dataIndex:"write"},{text:"CKSUM",dataIndex:"cksum"},{text:gettext("Message"),dataIndex:"msg"}]}]}),Ext.define("Proxmox.window.CertificateViewer",{extend:"Proxmox.window.Edit",xtype:"pmxCertViewer",title:gettext("Certificate"),fieldDefaults:{labelWidth:120},width:800,resizable:!0,items:[{xtype:"displayfield",fieldLabel:gettext("Name"),name:"filename"},{xtype:"displayfield",fieldLabel:gettext("Fingerprint"),name:"fingerprint"},{xtype:"displayfield",fieldLabel:gettext("Issuer"),name:"issuer"},{xtype:"displayfield",fieldLabel:gettext("Subject"),name:"subject"},{xtype:"displayfield",fieldLabel:gettext("Public Key Type"),name:"public-key-type"},{xtype:"displayfield",fieldLabel:gettext("Public Key Size"),name:"public-key-bits"},{xtype:"displayfield",fieldLabel:gettext("Valid Since"),renderer:Proxmox.Utils.render_timestamp,name:"notbefore"},{xtype:"displayfield",fieldLabel:gettext("Expires"),renderer:Proxmox.Utils.render_timestamp,name:"notafter"},{xtype:"displayfield",fieldLabel:gettext("Subject Alternative Names"),name:"san",renderer:Proxmox.Utils.render_san},{xtype:"textarea",editable:!1,grow:!0,growMax:200,fieldLabel:gettext("Certificate"),name:"pem"}],initComponent:function(){var t=this;if(!t.cert)throw"no cert given";if(!t.url)throw"no url given";t.callParent(),t.down("toolbar[dock=bottom]").setVisible(!1),t.load({success:function(e){Ext.isArray(e.result.data)&&Ext.Array.each(e.result.data,function(e){return e.filename!==t.cert||(t.setValues(e),!1)})}})}}),Ext.define("Proxmox.window.CertificateUpload",{extend:"Proxmox.window.Edit",xtype:"pmxCertUpload",title:gettext("Upload Custom Certificate"),resizable:!1,isCreate:!0,submitText:gettext("Upload"),method:"POST",width:600,reloadUi:void 0,apiCallDone:function(e,t,a){e&&this.reloadUi&&(Ext.getBody().mask(gettext("API server will be restarted to use new certificates, please reload web-interface!"),["pve-static-mask"]),Ext.defer(()=>window.location.reload(!0),1e4))},items:[{fieldLabel:gettext("Private Key (Optional)"),labelAlign:"top",emptyText:gettext("No change"),name:"key",xtype:"textarea"},{xtype:"filebutton",text:gettext("From File"),listeners:{change:function(e,t,a){let i=this.up("form");t=t.event,Ext.Array.each(t.target.files,function(e){Proxmox.Utils.loadTextFromFile(e,function(e){i.down("field[name=key]").setValue(e)},16384)}),e.reset()}}},{xtype:"box",autoEl:"hr"},{fieldLabel:gettext("Certificate Chain"),labelAlign:"top",allowBlank:!1,name:"certificates",xtype:"textarea"},{xtype:"filebutton",text:gettext("From File"),listeners:{change:function(e,t,a){let i=this.up("form");t=t.event,Ext.Array.each(t.target.files,function(e){Proxmox.Utils.loadTextFromFile(e,function(e){i.down("field[name=certificates]").setValue(e)},16384)}),e.reset()}}},{xtype:"hidden",name:"restart",value:"1"},{xtype:"hidden",name:"force",value:"1"}],initComponent:function(){if(!this.url)throw"neither url given";this.callParent()}}),Ext.define("Proxmox.window.ConsentModal",{extend:"Ext.window.Window",alias:["widget.pmxConsentModal"],mixins:["Proxmox.Mixin.CBind"],maxWidth:1e3,maxHeight:1e3,minWidth:600,minHeight:400,scrollable:!0,modal:!0,closable:!1,resizable:!1,alwaysOnTop:!0,title:gettext("Consent"),items:[{xtype:"displayfield",padding:10,scrollable:!0,cbind:{value:"{consent}"}}],buttons:[{handler:function(){this.up("window").close()},text:gettext("OK")}]}),Ext.define("Proxmox.window.ACMEAccountCreate",{extend:"Proxmox.window.Edit",mixins:["Proxmox.Mixin.CBind"],xtype:"pmxACMEAccountCreate",acmeUrl:void 0,width:450,title:gettext("Register Account"),isCreate:!0,method:"POST",submitText:gettext("Register"),showTaskViewer:!0,defaultExists:!1,items:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Account Name"),name:"name",cbind:{emptyText:e=>e("defaultExists")?"":"default",allowBlank:e=>!e("defaultExists")}},{xtype:"textfield",name:"contact",vtype:"email",allowBlank:!1,fieldLabel:gettext("E-Mail")},{xtype:"proxmoxComboGrid",name:"directory",reference:"directory",allowBlank:!1,valueField:"url",displayField:"name",fieldLabel:gettext("ACME Directory"),store:{autoLoad:!0,fields:["name","url"],idProperty:["name"],proxy:{type:"proxmox"},sorters:{property:"name",direction:"ASC"}},listConfig:{columns:[{header:gettext("Name"),dataIndex:"name",flex:1},{header:gettext("URL"),dataIndex:"url",flex:1}]},listeners:{change:function(e,t){if(t){var n=this.up("window").acmeUrl;let a=this.up("window").down("#tos_url_display"),i=this.up("window").down("#tos_url"),o=this.up("window").down("#tos_checkbox");a.setValue(gettext("Loading")),i.setValue(void 0),o.setValue(void 0),o.setHidden(!0),Proxmox.Utils.API2Request({url:n+"/tos",method:"GET",params:{directory:t},success:function(e,t){i.setValue(e.result.data),a.setValue(e.result.data),o.setHidden(!1)},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})}}}},{xtype:"displayfield",itemId:"tos_url_display",renderer:Proxmox.Utils.render_optional_url,name:"tos_url_display"},{xtype:"hidden",itemId:"tos_url",name:"tos_url"},{xtype:"proxmoxcheckbox",itemId:"tos_checkbox",boxLabel:gettext("Accept TOS"),submitValue:!1,validateValue:function(e){return!(!e||!this.checked)}}],initComponent:function(){var e=this;if(!e.acmeUrl)throw"no acmeUrl given";e.url=e.acmeUrl+"/account",e.callParent(),e.lookup("directory").store.proxy.setUrl(`/api2/json/${e.acmeUrl}/directories`)}}),Ext.define("Proxmox.window.ACMEAccountView",{extend:"Proxmox.window.Edit",xtype:"pmxACMEAccountView",width:600,fieldDefaults:{labelWidth:140},title:gettext("Account"),items:[{xtype:"displayfield",fieldLabel:gettext("E-Mail"),name:"email"},{xtype:"displayfield",fieldLabel:gettext("Created"),name:"createdAt"},{xtype:"displayfield",fieldLabel:gettext("Status"),name:"status"},{xtype:"displayfield",fieldLabel:gettext("Directory"),renderer:Proxmox.Utils.render_optional_url,name:"directory"},{xtype:"displayfield",fieldLabel:gettext("Terms of Services"),renderer:Proxmox.Utils.render_optional_url,name:"tos"}],initComponent:function(){var t=this;t.callParent(),t.down("toolbar[dock=bottom]").setVisible(!1),t.load({success:function(e){e=e.result.data;e.email=e.account.contact[0],e.createdAt=e.account.createdAt,e.status=e.account.status,t.setValues(e)}})}}),Ext.define("Proxmox.window.ACMEPluginEdit",{extend:"Proxmox.window.Edit",xtype:"pmxACMEPluginEdit",mixins:["Proxmox.Mixin.CBind"],isAdd:!0,isCreate:!1,width:550,acmeUrl:void 0,subject:"ACME DNS Plugin",cbindData:function(e){return{challengeSchemaUrl:`/api2/json/${this.acmeUrl}/challenge-schema`}},items:[{xtype:"inputpanel",createdFields:{},createdInitially:!1,originalValues:{},createSchemaFields:function(e){var t,a,i=this,o=i.down("container"),n=i.down("field[name=data]"),r=i.down("field[name=hint]");i.createdInitially||([i.originalValues]=Proxmox.Utils.parseACMEPluginData(n.getValue()));let l=[];for([t,a]of Object.entries(i.createdFields)){var s=a.getValue();null!=s&&""!==s&&l.push(t+"="+s),o.remove(a)}var d,u,c=n.getValue();null!=c&&""!==c&&l.push(c),n.setValue(l.join("\n")),i.createdFields={},"object"!=typeof e.fields&&(e.fields={});let x=!1;for([d,u]of Object.entries(e.fields).sort((e,t)=>e[0].localeCompare(t[0]))){let e;switch(u.type){case"string":e="proxmoxtextfield";break;case"integer":e="proxmoxintegerfield";break;case"number":e="numberfield";break;default:console.warn(`unknown type '${u.type}'`),e="proxmoxtextfield"}let t=d;"string"==typeof u.name&&(t=u.name);var m=Ext.create({xtype:e,name:"custom_"+d,fieldLabel:Ext.htmlEncode(t),width:"100%",labelWidth:150,labelSeparator:"=",emptyText:u.default||"",autoEl:u.description?{tag:"div","data-qtip":Ext.htmlEncode(Ext.htmlEncode(u.description))}:void 0});i.createdFields[d]=m,o.add(m),x=!0}n.setHidden(x),e.description?(r.setValue(e.description),r.setHidden(!1)):(r.setValue(""),r.setHidden(!0));var p,f,g=[];[l,g]=Proxmox.Utils.parseACMEPluginData(n.getValue());for([p,f]of Object.entries(l))i.createdFields[p]?(i.createdFields[p].setValue(f),i.createdFields[p].originalValue=i.originalValues[p],i.createdFields[p].checkDirty()):g.push(p+"="+f);n.setValue(g.join("\n")),i.createdInitially||(n.resetOriginalValue(),i.createdInitially=!0)},onGetValues:function(e){var t,a,i=this.up("pmxACMEPluginEdit");i.isCreate&&(e.id=e.plugin,e.type="dns"),delete e.plugin,Proxmox.Utils.delete_if_default(e,"validation-delay","30",i.isCreate);let o="";for([t,a]of Object.entries(this.createdFields)){var n=a.getValue();null!=n&&""!==n&&(o+=t+`=${n}
`),delete e["custom_"+t]}return e.data=Ext.util.Base64.encode(o+e.data),e},items:[{xtype:"pmxDisplayEditField",cbind:{editable:e=>e("isCreate"),submitValue:e=>e("isCreate")},editConfig:{flex:1,xtype:"proxmoxtextfield",allowBlank:!1},name:"plugin",labelWidth:150,fieldLabel:gettext("Plugin ID")},{xtype:"proxmoxintegerfield",name:"validation-delay",labelWidth:150,fieldLabel:gettext("Validation Delay"),emptyText:30,cbind:{deleteEmpty:"{!isCreate}"},minValue:0,maxValue:172800},{xtype:"pmxACMEApiSelector",name:"api",labelWidth:150,cbind:{url:"{challengeSchemaUrl}"},listeners:{change:function(e){var t=e.getSchema();e.up("inputpanel").createSchemaFields(t)}}},{xtype:"textarea",fieldLabel:gettext("API Data"),labelWidth:150,name:"data"},{xtype:"displayfield",fieldLabel:gettext("Hint"),labelWidth:150,name:"hint",hidden:!0}]}],initComponent:function(){var a=this;if(!a.acmeUrl)throw"no acmeUrl given";a.callParent(),a.isCreate?a.method="POST":a.load({success:function(e,t){a.setValues(e.result.data)}})}}),Ext.define("Proxmox.window.ACMEDomainEdit",{extend:"Proxmox.window.Edit",xtype:"pmxACMEDomainEdit",mixins:["Proxmox.Mixin.CBind"],subject:gettext("Domain"),isCreate:!1,width:450,acmeUrl:void 0,url:void 0,domainUsages:void 0,separateDomainEntries:void 0,cbindData:function(e){return{pluginsUrl:`/api2/json/${this.acmeUrl}/plugins`,hasUsage:!!this.domainUsages}},items:[{xtype:"inputpanel",onGetValues:function(e){var t=this.up("pmxACMEDomainEdit");let a=t.nodeconfig;var i=t.domain||{},o={digest:a.digest};let n=i.configkey;var r=Proxmox.Utils.parseACME(a.acme),l=()=>{for(let e=0;e<Proxmox.Utils.acmedomain_count;e++)if(void 0===a["acmedomain"+e])return"acmedomain"+e;throw"too many domains configured"};return t.separateDomainEntries||t.domainUsages?(n&&"acme"!==n||(n=l()),delete e.type,o[n]=Proxmox.Utils.printPropertyString(e,"domain")):"dns"===e.type?(i.configkey&&"acme"!==i.configkey||(n=l(),i.domain&&(Proxmox.Utils.remove_domain_from_acme(r,i.domain),o.acme=Proxmox.Utils.printACME(r))),delete e.type,o[n]=Proxmox.Utils.printPropertyString(e,"domain")):(i.configkey&&"acme"!==i.configkey&&(o.delete=[i.configkey]),Proxmox.Utils.add_domain_to_acme(r,e.domain),i.domain!==e.domain&&Proxmox.Utils.remove_domain_from_acme(r,i.domain),o.acme=Proxmox.Utils.printACME(r)),o},items:[{xtype:"proxmoxKVComboBox",name:"type",fieldLabel:gettext("Challenge Type"),allowBlank:!1,value:"standalone",comboItems:[["standalone","HTTP"],["dns","DNS"]],validator:function(e){var a=this.up("pmxACMEDomainEdit"),t=a.domain?a.domain.configkey:void 0;if("dns"===this.getValue()&&(!t||"acme"===t)){let t=!1;for(let e=0;e<Proxmox.Utils.acmedomain_count;e++)a.nodeconfig["acmedomain"+e]||(t=!0);if(!t)return gettext("Only 5 Domains with type DNS can be configured")}return!0},listeners:{change:function(e,t){var a=this.up("pmxACMEDomainEdit").down("field[name=plugin]");a.setDisabled("dns"!==t),a.setHidden("dns"!==t)}}},{xtype:"hidden",name:"alias"},{xtype:"pmxACMEPluginSelector",name:"plugin",disabled:!0,hidden:!0,allowBlank:!1,cbind:{url:"{pluginsUrl}"}},{xtype:"proxmoxtextfield",name:"domain",allowBlank:!1,vtype:"DnsNameOrWildcard",value:"",fieldLabel:gettext("Domain")},{xtype:"combobox",name:"usage",multiSelect:!0,editable:!1,fieldLabel:gettext("Usage"),cbind:{hidden:"{!hasUsage}",allowBlank:"{!hasUsage}"},fields:["usage","name"],displayField:"name",valueField:"usage",store:{data:[{usage:"api",name:"API"},{usage:"smtp",name:"SMTP"}]}}]}],initComponent:function(){var e,t=this;if(!t.url)throw"no url given";if(!t.acmeUrl)throw"no acmeUrl given";if(!t.nodeconfig)throw"no nodeconfig given";t.isCreate=!t.domain,t.isCreate&&(t.domain=Proxmox.NodeName+"."),t.callParent(),t.isCreate?t.setValues({domain:t.domain}):(e={...t.domain},Ext.isDefined(e.usage)&&(e.usage=e.usage.split(";")),t.setValues(e))}}),Ext.define("Proxmox.window.EndpointEditBase",{extend:"Proxmox.window.Edit",isAdd:!0,fieldDefaults:{labelWidth:120},width:700,initComponent:function(){var e=this;if(e.isCreate=!e.name,!e.baseUrl)throw"baseUrl not set";"group"===e.type?e.url=`/api2/extjs${e.baseUrl}/groups`:e.url=`/api2/extjs${e.baseUrl}/endpoints/`+e.type,e.isCreate?e.method="POST":(e.url+="/"+e.name,e.method="PUT");var t=Proxmox.Schema.notificationEndpointTypes[e.type];if(!t)throw"unknown endpoint type";e.subject=t.name,Ext.apply(e,{items:[{name:e.name,xtype:t.ipanel,isCreate:e.isCreate,baseUrl:e.baseUrl,type:e.type,defaultMailAuthor:t.defaultMailAuthor}]}),e.callParent(),e.isCreate||e.load()}}),Ext.define("Proxmox.panel.NotificationMatcherGeneralPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxNotificationMatcherGeneralPanel",mixins:["Proxmox.Mixin.CBind"],items:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Matcher Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:function(e){return e.enable=!e.disable,delete e.disable,e},onGetValues:function(e){return e.enable?this.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,e}}),Ext.define("Proxmox.panel.NotificationMatcherTargetPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxNotificationMatcherTargetPanel",mixins:["Proxmox.Mixin.CBind"],items:[{xtype:"pmxNotificationTargetSelector",name:"target",allowBlank:!1}]}),Ext.define("Proxmox.window.NotificationMatcherEdit",{extend:"Proxmox.window.Edit",isAdd:!0,onlineHelp:"notification_matchers",fieldDefaults:{labelWidth:120},width:800,initComponent:function(){var e=this;if(e.isCreate=!e.name,!e.baseUrl)throw"baseUrl not set";e.url=`/api2/extjs${e.baseUrl}/matchers`,e.isCreate?e.method="POST":(e.url+="/"+e.name,e.method="PUT"),e.subject=gettext("Notification Matcher"),Ext.apply(e,{bodyPadding:0,items:[{xtype:"tabpanel",region:"center",layout:"fit",bodyPadding:10,items:[{name:e.name,title:gettext("General"),xtype:"pmxNotificationMatcherGeneralPanel",isCreate:e.isCreate,baseUrl:e.baseUrl},{name:e.name,title:gettext("Match Rules"),xtype:"pmxNotificationMatchRulesEditPanel",isCreate:e.isCreate,baseUrl:e.baseUrl},{name:e.name,title:gettext("Targets to notify"),xtype:"pmxNotificationMatcherTargetPanel",isCreate:e.isCreate,baseUrl:e.baseUrl}]}]}),e.callParent(),e.isCreate||e.load()}}),Ext.define("Proxmox.form.NotificationTargetSelector",{extend:"Ext.grid.Panel",alias:"widget.pmxNotificationTargetSelector",mixins:{field:"Ext.form.field.Field"},padding:"0 0 10 0",allowBlank:!0,selectAll:!1,isFormField:!0,store:{autoLoad:!0,model:"proxmox-notification-endpoints",sorters:"name"},columns:[{header:gettext("Target Name"),dataIndex:"name",flex:1},{header:gettext("Type"),dataIndex:"type",flex:1},{header:gettext("Comment"),dataIndex:"comment",flex:3}],selModel:{selType:"checkboxmodel",mode:"SIMPLE"},checkChangeEvents:["selectionchange","change"],listeners:{selectionchange:function(){this.checkChange()}},getSubmitData:function(){var e={};return e[this.name]=this.getValue(),e},getValue:function(){return void 0!==this.savedValue?this.savedValue:(this.getSelectionModel().getSelection()??[]).map(e=>e.data.name)},setValueSelection:function(e){let a=this.getStore(),i=[];var t=e.map(e=>{var t=a.findRecord("name",e,0,!1,!0,!0);return t||i.push(e),t}).filter(e=>e);for(const n of i){var o=a.add({name:n,type:"-",comment:gettext("Included target does not exist!")});t.push(o[0])}e=this.getSelectionModel();t.length?e.select(t):e.deselectAll(),this.getErrors()},setValue:function(e){let t=this;var a=t.getStore();return a.isLoaded()?t.setValueSelection(e):(t.savedValue=e,a.on("load",function(){t.setValueSelection(e),delete t.savedValue},{single:!0})),t.mixins.field.setValue.call(t,e)},getErrors:function(e){var t=this;return t.isDisabled()||!1!==t.allowBlank||0!==t.getSelectionModel().getCount()?(t.removeBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[]):(t.addBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[gettext("No target selected")])},initComponent:function(){this.callParent(),this.initField()}}),Ext.define("Proxmox.panel.NotificationRulesEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxNotificationMatchRulesEditPanel",mixins:["Proxmox.Mixin.CBind"],controller:{xclass:"Ext.app.ViewController",control:{field:{change:function(e){var t,a,i=this.getViewModel();e.field&&(i=i.get("selectedRecord"))&&(t=Ext.apply({},i.get("data")),(a=e.getValue())&&a.length||(t[e.field]=a,i.set({data:t})))}}}},viewModel:{data:{selectedRecord:null,matchFieldType:"exact",matchFieldField:"",matchFieldValue:"",rootMode:"all"},formulas:{nodeType:{get:function(e){return e("selectedRecord")?.get("type")},set:function(e){var t=this.get("selectedRecord");let a;switch(e){case"match-severity":a={value:["info","notice","warning","error","unknown"]};break;case"match-field":a={type:"exact",field:"",value:""};break;case"match-calendar":a={value:""}}e={type:e,data:a};t.set(e)}},showMatchingMode:function(e){e=e("selectedRecord");return!!e&&e.isRoot()},showMatcherType:function(e){e=e("selectedRecord");return!!e&&!e.isRoot()},rootMode:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){var t=this.get("selectedRecord"),a=t.get("data");let i=!1;e.startsWith("not")&&(e=e.substring(3),i=!0),t.set({data:{...a,value:e,invert:i}})},get:function(e){return(e?.get("data").invert?"not":"")+e?.get("data")?.value}}}},column1:[{xtype:"pmxNotificationMatchRuleTree",cbind:{isCreate:"{isCreate}"}}],column2:[{xtype:"pmxNotificationMatchRuleSettings",cbind:{baseUrl:"{baseUrl}"}}],onGetValues:function(t){let a=this;var e=e=>{Ext.isArray(t[e])&&0===t[e].length&&(delete t[e],a.isCreate||Proxmox.Utils.assemble_field_data(t,{delete:e}))};return e("match-field"),e("match-severity"),e("match-calendar"),t}}),Ext.define("Proxmox.panel.NotificationMatchRuleTree",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchRuleTree",mixins:["Proxmox.Mixin.CBind"],border:!1,getNodeTextAndIcon:function(e,t){let a,i;switch(e){case"match-severity":{let e=t.value;Ext.isArray(t.value)&&(e=t.value.join(", ")),a=Ext.String.format(gettext("Match severity: {0}"),e),i="fa fa-exclamation",e||(i+=" internal-error")}break;case"match-field":var o=t.field,n=t.value;a=Ext.String.format(gettext("Match field: {0}={1}"),o,n),i="fa fa-square-o",o&&n&&(!Ext.isArray(n)||n.length)||(i+=" internal-error");break;case"match-calendar":o=t.value;a=Ext.String.format(gettext("Match calendar: {0}"),o),i="fa fa-calendar-o",o&&o.length||(i+=" internal-error");break;case"mode":"all"===t.value?a=t.invert?gettext("At least one rule does not match"):gettext("All rules match"):"any"===t.value&&(a=t.invert?gettext("No rule matches"):gettext("Any rule matches")),i="fa fa-filter"}return[a,i]},initComponent:function(){let d=this,n=Ext.create("Ext.data.TreeStore",{root:{expanded:!0,expandable:!1,text:"",type:"mode",data:{value:"all",invert:!1},children:[],iconCls:"fa fa-filter"}}),t=Ext.create({xtype:"hiddenfield",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getErrors:function(){for(const e of this.value??[])if(!e.match(/^([^:]+):([^=]+)=(.+)$/))return[""];return[]},getSubmitValue:function(){let e=this.value;return e=e||[]},name:"match-field"}),a=Ext.create({xtype:"hiddenfield",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getErrors:function(){for(const e of this.value??[])if(!e)return[""];return[]},getSubmitValue:function(){let e=this.value;return e=e||[]},name:"match-severity"}),i=Ext.create({xtype:"hiddenfield",name:"mode",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getSubmitValue:function(){return this.value}}),u=Ext.create({xtype:"hiddenfield",name:"match-calendar",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getErrors:function(){for(const e of this.value??[])if(!e)return[""];return[]},getSubmitValue:function(){return this.value}}),c=Ext.create({xtype:"proxmoxcheckbox",name:"invert-match",hidden:!0,deleteEmpty:!d.isCreate});t.addListener("change",function(e,t){var a;for(a of n.queryBy(e=>"match-field"===e.get("type")).getRange())a.remove(!0);if(t){var i,t=t.map(function(e){let[,t,a,i]=e.match(/^(?:(regex|exact):)?([A-Za-z0-9_][A-Za-z0-9._-]*)=(.+)$/);return"exact"===(t=void 0===t?"exact":t)&&(i=i.split(",")),{type:"match-field",data:{type:t,field:a,value:i},leaf:!0}}),o=n.getRootNode();for(i of t)o.appendChild(i)}}),a.addListener("change",function(e,t){var a;for(a of n.queryBy(e=>"match-severity"===e.get("type")).getRange())a.remove(!0);var i,t=t.map(function(e){return{type:"match-severity",data:{value:e.split(",")},leaf:!0}}),o=n.getRootNode();for(i of t)o.appendChild(i)}),u.addListener("change",function(e,t){var a;for(a of n.queryBy(e=>"match-calendar"===e.get("type")).getRange())a.remove(!0);var i,t=t.map(function(e){return{type:"match-calendar",data:{value:e},leaf:!0}}),o=n.getRootNode();for(i of t)o.appendChild(i)}),i.addListener("change",function(e,t){var a=n.getRootNode().get("data");n.getRootNode().set("data",{...a,value:t})}),c.addListener("change",function(e,t){var a=n.getRootNode().get("data");n.getRootNode().set("data",{...a,invert:t})}),n.addListener("datachanged",function(e){e.suspendEvent("datachanged");let o=[],n=[],r=[],l="all",s=!1;e.each(function(e){var t=e.get("type"),a=e.get("data");switch(t){case"match-field":o.push(`${a.type}:${a.field??""}=`+(a.value??""));break;case"match-severity":Ext.isArray(a.value)?n.push(a.value.join(",")):n.push(a.value);break;case"match-calendar":r.push(a.value);break;case"mode":l=a.value,s=a.invert}var[t,i]=d.getNodeTextAndIcon(t,a);e.set({text:t,iconCls:i})}),t.suspendEvent("change"),t.setValue(o),t.resumeEvent("change"),u.suspendEvent("change"),u.setValue(r),u.resumeEvent("change"),i.suspendEvent("change"),i.setValue(l),i.resumeEvent("change"),c.suspendEvent("change"),c.setValue(s),c.resumeEvent("change"),a.suspendEvent("change"),a.setValue(n),a.resumeEvent("change"),e.resumeEvent("datachanged")});let o=Ext.create({xtype:"treepanel",store:n,minHeight:300,maxHeight:300,scrollable:!0,bind:{selection:"{selectedRecord}"}});Ext.apply(d,{items:[t,i,a,c,u,o,{xtype:"button",margin:"5 5 5 0",text:gettext("Add"),iconCls:"fa fa-plus-circle",handler:function(){n.getRootNode().appendChild({type:"match-field",data:{type:"exact",field:"",value:""},leaf:!0}),o.setSelection(n.getRootNode().lastChild)}},{xtype:"button",margin:"5 5 5 0",text:gettext("Remove"),iconCls:"fa fa-minus-circle",handler:function(){var e;for(e of o.getSelection())e.isRoot()||e.remove(!0)}}]}),d.callParent()}}),Ext.define("Proxmox.panel.NotificationMatchRuleSettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchRuleSettings",mixins:["Proxmox.Mixin.CBind"],border:!1,layout:"anchor",items:[{xtype:"proxmoxKVComboBox",name:"mode",fieldLabel:gettext("Match if"),allowBlank:!1,isFormField:!1,matchFieldWidth:!1,comboItems:[["all",gettext("All rules match")],["any",gettext("Any rule matches")],["notall",gettext("At least one rule does not match")],["notany",gettext("No rule matches")]],hidden:!0,bind:{hidden:"{!showMatchingMode}",disabled:"{!showMatchingMode}",value:"{rootMode}"}},{xtype:"proxmoxKVComboBox",fieldLabel:gettext("Node type"),isFormField:!1,allowBlank:!1,hidden:!0,bind:{value:"{nodeType}",hidden:"{!showMatcherType}",disabled:"{!showMatcherType}"},comboItems:[["match-field",gettext("Match Field")],["match-severity",gettext("Match Severity")],["match-calendar",gettext("Match Calendar")]]},{xtype:"pmxNotificationMatchFieldSettings",cbind:{baseUrl:"{baseUrl}"}},{xtype:"pmxNotificationMatchSeveritySettings"},{xtype:"pmxNotificationMatchCalendarSettings"}]}),Ext.define("Proxmox.panel.MatchCalendarSettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchCalendarSettings",border:!1,layout:"anchor",hidden:!0,bind:{hidden:"{!typeIsMatchCalendar}"},viewModel:{formulas:{typeIsMatchCalendar:{bind:{bindTo:"{selectedRecord}",deep:!0},get:function(e){return"match-calendar"===e?.get("type")}},matchCalendarValue:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){var t=this.get("selectedRecord"),a=t.get("data");t.set({data:{...a,value:e}})},get:function(e){return e?.get("data")?.value}}}},items:[{xtype:"proxmoxKVComboBox",fieldLabel:gettext("Timespan to match"),isFormField:!1,allowBlank:!1,editable:!0,displayField:"key",field:"value",bind:{value:"{matchCalendarValue}",disabled:"{!typeIsMatchCalender}"},comboItems:[["mon 8-12",""],["tue..fri,sun 0:00-23:59",""]]}],initComponent:function(){Ext.apply(this.viewModel,{parent:this.up("pmxNotificationMatchRulesEditPanel").getViewModel()}),this.callParent()}}),Ext.define("Proxmox.panel.MatchSeveritySettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchSeveritySettings",border:!1,layout:"anchor",hidden:!0,bind:{hidden:"{!typeIsMatchSeverity}"},viewModel:{formulas:{typeIsMatchSeverity:{bind:{bindTo:"{selectedRecord}",deep:!0},get:function(e){return"match-severity"===e?.get("type")}},matchSeverityValue:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){var t=this.get("selectedRecord"),a=t.get("data");t.set({data:{...a,value:e}})},get:function(e){return e?.get("data")?.value}}}},items:[{xtype:"proxmoxKVComboBox",fieldLabel:gettext("Severities to match"),isFormField:!1,allowBlank:!0,multiSelect:!0,field:"value",hidden:!0,bind:{value:"{matchSeverityValue}",hidden:"{!typeIsMatchSeverity}",disabled:"{!typeIsMatchSeverity}"},comboItems:[["info",gettext("Info")],["notice",gettext("Notice")],["warning",gettext("Warning")],["error",gettext("Error")],["unknown",gettext("Unknown")]]}],initComponent:function(){Ext.apply(this.viewModel,{parent:this.up("pmxNotificationMatchRulesEditPanel").getViewModel()}),this.callParent()}}),Ext.define("Proxmox.panel.MatchFieldSettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchFieldSettings",border:!1,layout:"anchor",hidden:!0,bind:{hidden:"{!typeIsMatchField}"},controller:{xclass:"Ext.app.ViewController",control:{"field[reference=fieldSelector]":{change:function(e){var t=this.getView().down("field[reference=valueSelector]").getStore(),e=e.getValue();e&&t.setFilters([{property:"field",value:e}])}}}},viewModel:{formulas:{typeIsMatchField:{bind:{bindTo:"{selectedRecord}",deep:!0},get:function(e){return"match-field"===e?.get("type")}},isRegex:function(e){return"regex"===e("matchFieldType")},matchFieldType:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){var t=this.get("selectedRecord"),a=t.get("data"),i=[];if("regex"===e){let e="^";a.value&&a.value.length&&(e+=`(${a.value.join("|")})`),e+="$",i.push(e)}t.set({data:{...a,type:e,value:i}})},get:function(e){return e?.get("data")?.type}},matchFieldField:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){var t=this.get("selectedRecord"),a=t.get("data");t.set({data:{...a,field:e,value:[]}})},get:function(e){return e?.get("data")?.field}},matchFieldValue:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){var t=this.get("selectedRecord"),a=t.get("data");t.set({data:{...a,value:e}})},get:function(e){return e?.get("data")?.value}}}},initComponent:function(){var e=this,t=Ext.create("Ext.data.Store",{model:"proxmox-notification-fields",autoLoad:!0,proxy:{type:"proxmox",url:`/api2/json/${e.baseUrl}/matcher-fields`},listeners:{load:function(){this.each(function(e){e.set({description:Proxmox.Utils.formatNotificationFieldName(e.get("name"))})}),this.commitChanges()}}}),a=Ext.create("Ext.data.Store",{model:"proxmox-notification-field-values",autoLoad:!0,proxy:{type:"proxmox",url:`/api2/json/${e.baseUrl}/matcher-field-values`},listeners:{load:function(){this.each(function(e){"type"===e.get("field")&&e.set({comment:Proxmox.Utils.formatNotificationFieldValue(e.get("value"))})},this,!0),this.commitChanges()}}});Ext.apply(e.viewModel,{parent:e.up("pmxNotificationMatchRulesEditPanel").getViewModel()}),Ext.apply(e,{items:[{fieldLabel:gettext("Match Type"),xtype:"proxmoxKVComboBox",reference:"type",isFormField:!1,allowBlank:!1,submitValue:!1,field:"type",bind:{value:"{matchFieldType}"},comboItems:[["exact",gettext("Exact")],["regex",gettext("Regex")]]},{fieldLabel:gettext("Field"),reference:"fieldSelector",xtype:"proxmoxComboGrid",isFormField:!1,submitValue:!1,allowBlank:!1,editable:!1,store:t,queryMode:"local",valueField:"name",displayField:"description",field:"field",bind:{value:"{matchFieldField}"},listConfig:{columns:[{header:gettext("Description"),dataIndex:"description",flex:2},{header:gettext("Field Name"),dataIndex:"name",flex:1}]}},{fieldLabel:gettext("Value"),reference:"valueSelector",xtype:"proxmoxComboGrid",autoSelect:!1,editable:!1,isFormField:!1,submitValue:!1,allowBlank:!1,showClearTrigger:!0,field:"value",store:a,valueField:"value",displayField:"value",notFoundIsValid:!1,multiSelect:!0,bind:{value:"{matchFieldValue}",hidden:"{isRegex}"},listConfig:{columns:[{header:gettext("Value"),dataIndex:"value",flex:1},{header:gettext("Comment"),dataIndex:"comment",flex:2}]}},{fieldLabel:gettext("Regex"),xtype:"proxmoxtextfield",editable:!0,isFormField:!1,submitValue:!1,allowBlank:!1,field:"value",bind:{value:"{matchFieldValue}",hidden:"{!isRegex}"}}]}),e.callParent()}}),Ext.define("proxmox-file-tree",{extend:"Ext.data.Model",fields:["filepath","text","type","size",{name:"sizedisplay",calculate:e=>{return void 0===e.size?"":Proxmox.Utils.format_size(e.size)}},{name:"mtime",type:"date",dateFormat:"timestamp"},{name:"iconCls",calculate:function(e){let t=Proxmox.Schema.pxarFileTypes[e.type]?.icon??"file-o";return"fa fa-"+(t=e.expanded&&"d"===e.type?"folder-open-o":t)}}],idProperty:"filepath"}),Ext.define("Proxmox.window.FileBrowser",{extend:"Ext.window.Window",width:800,height:600,modal:!0,config:{listURL:"",downloadURL:"",extraParams:{},downloadableFileTypes:{h:!0,f:!0,d:!0},downloadPrefix:""},controller:{xclass:"Ext.app.ViewController",buildUrl:function(e,t){var a,i,o=new URL(e,window.location.origin);for([a,i]of Object.entries(t))o.searchParams.append(a,i);return o.href},downloadTar:function(){this.downloadFile(!0)},downloadZip:function(){this.downloadFile(!1)},downloadFile:function(t){var a=this.getView(),i=this.lookup("tree").getSelection();if(i&&!(i.length<1)){var i=i[0].data,o={...a.extraParams};o.filepath=i.filepath;let e=a.downloadPrefix+i.text;"d"===i.type&&(t?(o.tar=1,e+=".tar.zst"):e+=".zip"),Proxmox.Utils.downloadAsFile(this.buildUrl(a.downloadURL,o),e)}},fileChanged:function(){var e,t,a,i=this.getView(),o=this.lookup("tree").getSelection();!o||o.length<1||(o=o[0].data,e=Ext.String.format(gettext('Selected "{0}"'),atob(o.filepath)),i.lookup("selectText").setText(e),e=i.downloadURL&&i.downloadableFileTypes[o.type],t="d"===o.type,(a=i.lookup("downloadBtn")).setDisabled(!e||t),a.setHidden(e&&t),o=Proxmox.Schema.pxarFileTypes[o.type]?.label??Proxmox.Utils.unknownText,o=Ext.String.format(gettext("File of type {0} cannot be downloaded directly, download a parent directory instead."),o),e||a.setStyle({pointerEvents:"all"}),a.setTooltip(e?null:o),(a=i.lookup("menuBtn")).setDisabled(!e||!t),a.setHidden(!e||!t))},errorHandler:function(e,t){return 503!==e?.status&&(this.lookup("downloadBtn").setDisabled(!0),this.lookup("menuBtn").setDisabled(!0),!!this.initialLoadDone)&&(Ext.Msg.alert(gettext("Error"),t),!0)},init:function(o){let n=this,r=n.lookup("tree");if(!o.listURL)throw"no list URL given";let l=r.getStore();var e=l.getProxy();e.setUrl(o.listURL),e.setTimeout(6e4),e.setExtraParams(o.extraParams),r.mon(l,"beforeload",()=>{Proxmox.Utils.setErrorMask(r,!0)}),r.mon(l,"load",(e,t,a,i,o)=>{a?Proxmox.Utils.setErrorMask(r,!1):(o.loadCount||(o.loadCount=0),503===i?.error?.status&&o.loadCount<10?(o.collapse(),o.expand(),o.loadCount++):(a=i.getError(),o=Proxmox.Utils.getResponseErrorMessage(a),i=a,a=o,n.errorHandler(i,a)?Proxmox.Utils.setErrorMask(r,!1):Proxmox.Utils.setErrorMask(r,o)))}),l.load((e,t,a)=>{var i=l.getRoot();if(i.expand(),"all"===o.archive)i.expandChildren(!1);else if(o.archive){let e=i.findChild("text",o.archive);e&&(e.expand(),setTimeout(function(){r.setSelection(e),r.getView().focusRow(e)},10))}else 1===i.childNodes.length&&i.firstChild.expand();n.initialLoadDone=a})},control:{treepanel:{selectionchange:"fileChanged"}}},layout:"fit",items:[{xtype:"treepanel",scrollable:!0,rootVisible:!1,reference:"tree",store:{autoLoad:!1,model:"proxmox-file-tree",defaultRootId:"/",nodeParam:"filepath",sorters:"text",proxy:{appendId:!1,type:"proxmox"}},viewConfig:{loadMask:!1},columns:[{text:gettext("Name"),xtype:"treecolumn",flex:1,dataIndex:"text",renderer:Ext.String.htmlEncode},{text:gettext("Size"),dataIndex:"sizedisplay",align:"end",sorter:{sorterFn:function(e,t){return"d"===e.data.type&&"d"!==t.data.type?-1:"d"!==e.data.type&&"d"===t.data.type?1:(e.data.size||0)-(t.data.size||0)}}},{text:gettext("Modified"),dataIndex:"mtime",minWidth:200},{text:gettext("Type"),dataIndex:"type",renderer:e=>Proxmox.Schema.pxarFileTypes[e]?.label??Proxmox.Utils.unknownText}]}],fbar:[{text:"",xtype:"label",reference:"selectText"},{text:gettext("Download"),xtype:"button",handler:"downloadZip",reference:"downloadBtn",disabled:!0,hidden:!0},{text:gettext("Download as"),xtype:"button",reference:"menuBtn",menu:{items:[{iconCls:"fa fa-fw fa-file-zip-o",text:gettext(".zip"),handler:"downloadZip",reference:"downloadZip"},{iconCls:"fa fa-fw fa-archive",text:gettext(".tar.zst"),handler:"downloadTar",reference:"downloadTar"}]}}]}),Ext.define("Proxmox.window.AuthEditBase",{extend:"Proxmox.window.Edit",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,isAdd:!0,fieldDefaults:{labelWidth:120},baseurl:"/access/domains",useTypeInUrl:!1,initComponent:function(){var a=this,e=(a.isCreate=!a.realm,a.url="/api2/extjs"+a.baseUrl,a.useTypeInUrl&&(a.url+="/"+a.authType),a.isCreate?a.method="POST":(a.url+="/"+a.realm,a.method="PUT"),Proxmox.Schema.authDomains[a.authType]);if(!e)throw"unknown auth type "+a.authType;if(!e.add&&a.isCreate)throw"trying to add non addable realm of type "+a.authType;a.subject=e.name;let t,i;t=e.syncipanel?(i=0,{xtype:"tabpanel",region:"center",layout:"fit",bodyPadding:10,items:[{title:gettext("General"),realm:a.realm,xtype:e.ipanel,isCreate:a.isCreate,useTypeInUrl:a.useTypeInUrl,type:a.authType,showDefaultRealm:a.showDefaultRealm},{title:gettext("Sync Options"),realm:a.realm,xtype:e.syncipanel,isCreate:a.isCreate,type:a.authType}]}):[{realm:a.realm,xtype:e.ipanel,isCreate:a.isCreate,useTypeInUrl:a.useTypeInUrl,type:a.authType,showDefaultRealm:a.showDefaultRealm}],Ext.apply(a,{items:t,bodyPadding:i}),a.callParent(),a.isCreate||a.load({success:function(e,t){e=e.result.data||{};if(!a.useTypeInUrl&&e.type!==a.authType)throw a.close(),`got wrong auth type '${a.authType}' for realm '${e.type}'`;a.setValues(e)}})}}),Ext.define("Proxmox.panel.OpenIDInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthOpenIDPanel",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,type:"openid",onGetValues:function(e){return this.isCreate&&!this.useTypeInUrl&&(e.type=this.type),e},columnT:[{xtype:"textfield",name:"issuer-url",fieldLabel:gettext("Issuer URL"),allowBlank:!1}],column1:[{xtype:"pmxDisplayEditField",name:"realm",cbind:{value:"{realm}",editable:"{isCreate}"},fieldLabel:gettext("Realm"),allowBlank:!1},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Default Realm"),name:"default",value:0,cbind:{deleteEmpty:"{!isCreate}",hidden:"{!showDefaultRealm}",disabled:"{!showDefaultRealm}"},autoEl:{tag:"div","data-qtip":gettext("Set realm as default for login")}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Client ID"),name:"client-id",allowBlank:!1},{xtype:"proxmoxtextfield",fieldLabel:gettext("Client Key"),cbind:{deleteEmpty:"{!isCreate}"},name:"client-key"}],column2:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("Autocreate Users"),name:"autocreate",value:0,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxDisplayEditField",name:"username-claim",fieldLabel:gettext("Username Claim"),editConfig:{xtype:"proxmoxKVComboBox",editable:!0,comboItems:[["__default__",Proxmox.Utils.defaultText],["subject","subject"],["username","username"],["email","email"]]},cbind:{value:e=>e("isCreate")?"__default__":Proxmox.Utils.defaultText,deleteEmpty:"{!isCreate}",editable:"{isCreate}"}},{xtype:"proxmoxtextfield",name:"scopes",fieldLabel:gettext("Scopes"),emptyText:Proxmox.Utils.defaultText+" (email profile)",submitEmpty:!1,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxKVComboBox",name:"prompt",fieldLabel:gettext("Prompt"),editable:!0,emptyText:gettext("Auth-Provider Default"),comboItems:[["__default__",gettext("Auth-Provider Default")],["none","none"],["login","login"],["consent","consent"],["select_account","select_account"]],cbind:{deleteEmpty:"{!isCreate}"}}],columnB:[{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],advancedColumnB:[{xtype:"proxmoxtextfield",name:"acr-values",fieldLabel:gettext("ACR Values"),submitEmpty:!1,cbind:{deleteEmpty:"{!isCreate}"}}]}),Ext.define("Proxmox.panel.LDAPInputPanelViewModel",{extend:"Ext.app.ViewModel",alias:"viewmodel.pmxAuthLDAPPanel",data:{mode:"ldap",anonymous_search:1},formulas:{tls_enabled:function(e){return"ldap"!==e("mode")}}}),Ext.define("Proxmox.panel.LDAPInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthLDAPPanel",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,viewModel:{type:"pmxAuthLDAPPanel"},type:"ldap",onlineHelp:"user-realms-ldap",onGetValues:function(e){var t;return this.isCreate&&!this.useTypeInUrl&&(e.type=this.type),e.anonymous_search&&!this.isCreate&&(e.delete||(e.delete=[]),Array.isArray(e.delete)||(t=e.delete,e.delete=[],e.delete.push(t)),e.delete.push("bind-dn"),e.delete.push("password")),delete e.anonymous_search,e},onSetValues:function(e){return e.anonymous_search=e["bind-dn"]?0:1,this.getViewModel().set("anonymous_search",e.anonymous_search),e},cbindData:function(e){return{isLdap:"ldap"===this.type,isAd:"ad"===this.type}},column1:[{xtype:"pmxDisplayEditField",name:"realm",cbind:{value:"{realm}",editable:"{isCreate}"},fieldLabel:gettext("Realm"),allowBlank:!1},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Default Realm"),name:"default",value:0,cbind:{deleteEmpty:"{!isCreate}",hidden:"{!showDefaultRealm}",disabled:"{!showDefaultRealm}"},autoEl:{tag:"div","data-qtip":gettext("Set realm as default for login")}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Base Domain Name"),name:"base-dn",emptyText:"cn=Users,dc=company,dc=net",cbind:{hidden:"{!isLdap}",allowBlank:"{!isLdap}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("User Attribute Name"),name:"user-attr",emptyText:"uid / sAMAccountName",cbind:{hidden:"{!isLdap}",allowBlank:"{!isLdap}"}},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Anonymous Search"),name:"anonymous_search",bind:{value:"{anonymous_search}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Bind Domain Name"),name:"bind-dn",allowBlank:!1,cbind:{emptyText:e=>e("isAd")?"<EMAIL>":"cn=user,dc=company,dc=net",autoEl:e=>e("isAd")?{tag:"div","data-qtip":gettext("LDAP DN syntax can be used as well, e.g. cn=user,dc=company,dc=net")}:{}},bind:{disabled:"{anonymous_search}"}},{xtype:"proxmoxtextfield",inputType:"password",fieldLabel:gettext("Bind Password"),name:"password",cbind:{emptyText:e=>e("isCreate")?"":gettext("Unchanged"),allowBlank:"{!isCreate}"},bind:{disabled:"{anonymous_search}"}}],column2:[{xtype:"proxmoxtextfield",name:"server1",fieldLabel:gettext("Server"),allowBlank:!1},{xtype:"proxmoxtextfield",name:"server2",fieldLabel:gettext("Fallback Server"),submitEmpty:!1,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxintegerfield",name:"port",fieldLabel:gettext("Port"),minValue:1,maxValue:65535,emptyText:gettext("Default"),submitEmptyText:!1,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxKVComboBox",name:"mode",fieldLabel:gettext("Mode"),editable:!1,comboItems:[["ldap","LDAP"],["ldap+starttls","STARTTLS"],["ldaps","LDAPS"]],bind:"{mode}",cbind:{deleteEmpty:"{!isCreate}",value:e=>e("isCreate")?"ldap":"LDAP"}},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Verify Certificate"),name:"verify",value:0,cbind:{deleteEmpty:"{!isCreate}"},bind:{disabled:"{!tls_enabled}"},autoEl:{tag:"div","data-qtip":gettext("Verify TLS certificate of the server")}}],columnB:[{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}]}),Ext.define("Proxmox.panel.LDAPSyncInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthLDAPSyncPanel",mixins:["Proxmox.Mixin.CBind"],editableAttributes:["firstname","lastname","email"],editableDefaults:["scope","enable-new"],default_opts:{},sync_attributes:{},type:"ldap",onGetValues:function(t){let a=this,i=(a.editableDefaults.forEach(e=>{t[e]?(a.default_opts[e]=t[e],delete t[e]):delete a.default_opts[e]}),[]);return["acl","entry","properties"].forEach(e=>{t["remove-vanished-"+e]&&i.push(e),delete t["remove-vanished-"+e]}),a.default_opts["remove-vanished"]=i.join(";"),t["sync-defaults-options"]=Proxmox.Utils.printPropertyString(a.default_opts),a.editableAttributes.forEach(e=>{t[e]?(a.sync_attributes[e]=t[e],delete t[e]):delete a.sync_attributes[e]}),t["sync-attributes"]=Proxmox.Utils.printPropertyString(a.sync_attributes),Proxmox.Utils.delete_if_default(t,"sync-defaults-options"),Proxmox.Utils.delete_if_default(t,"sync-attributes"),"string"==typeof t.delete&&(t.delete=t.delete.split(",")),a.isCreate&&delete t.delete,t},setValues:function(t){let a=this;if((t["sync-attributes"]&&(a.sync_attributes=Proxmox.Utils.parsePropertyString(t["sync-attributes"]),delete t["sync-attributes"],a.editableAttributes.forEach(e=>{a.sync_attributes[e]&&(t[e]=a.sync_attributes[e])})),t["sync-defaults-options"])&&(a.default_opts=Proxmox.Utils.parsePropertyString(t["sync-defaults-options"]),delete t.default_opts,a.editableDefaults.forEach(e=>{a.default_opts[e]&&(t[e]=a.default_opts[e])}),a.default_opts["remove-vanished"]))for(const e of a.default_opts["remove-vanished"].split(";"))t["remove-vanished-"+e]=1;return a.callParent([t])},column1:[{xtype:"proxmoxtextfield",name:"firstname",fieldLabel:gettext("First Name attribute"),autoEl:{tag:"div","data-qtip":Ext.String.format(gettext("Often called {0}"),"`givenName`")}},{xtype:"proxmoxtextfield",name:"lastname",fieldLabel:gettext("Last Name attribute"),autoEl:{tag:"div","data-qtip":Ext.String.format(gettext("Often called {0}"),"`sn`")}},{xtype:"proxmoxtextfield",name:"email",fieldLabel:gettext("E-Mail attribute"),autoEl:{tag:"div","data-qtip":e=>e("isAd")?Ext.String.format(gettext("Often called {0} or {1}"),"`userPrincipalName`","`mail`"):Ext.String.format(gettext("Often called {0}"),"`mail`")}},{xtype:"displayfield",value:gettext("Default Sync Options")},{xtype:"proxmoxKVComboBox",value:"__default__",deleteEmpty:!1,comboItems:[["__default__",Ext.String.format(gettext("{0} ({1})"),Proxmox.Utils.yesText,Proxmox.Utils.defaultText)],["true",Proxmox.Utils.yesText],["false",Proxmox.Utils.noText]],name:"enable-new",fieldLabel:gettext("Enable new users")}],column2:[{xtype:"proxmoxtextfield",name:"user-classes",fieldLabel:gettext("User classes"),cbind:{deleteEmpty:"{!isCreate}"},emptyText:"inetorgperson, posixaccount, person, user",autoEl:{tag:"div","data-qtip":gettext("Default user classes: inetorgperson, posixaccount, person, user")}},{xtype:"proxmoxtextfield",name:"filter",fieldLabel:gettext("User Filter"),cbind:{deleteEmpty:"{!isCreate}"}}],columnB:[{xtype:"fieldset",title:gettext("Remove Vanished Options"),items:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("ACL"),name:"remove-vanished-acl",boxLabel:gettext("Remove ACLs of vanished users")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Entry"),name:"remove-vanished-entry",boxLabel:gettext("Remove vanished user")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Properties"),name:"remove-vanished-properties",boxLabel:gettext("Remove vanished properties from synced users.")}]}]}),Ext.define("Proxmox.panel.ADInputPanel",{extend:"Proxmox.panel.LDAPInputPanel",xtype:"pmxAuthADPanel",type:"ad",onlineHelp:"user-realms-ad"}),Ext.define("Proxmox.panel.ADSyncInputPanel",{extend:"Proxmox.panel.LDAPSyncInputPanel",xtype:"pmxAuthADSyncPanel",type:"ad"}),Ext.define("Proxmox.panel.SimpleRealmInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthSimplePanel",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,column1:[{xtype:"pmxDisplayEditField",name:"realm",cbind:{value:"{realm}"},fieldLabel:gettext("Realm")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Default Realm"),name:"default",value:0,deleteEmpty:!0,autoEl:{tag:"div","data-qtip":gettext("Set realm as default for login")},cbind:{hidden:"{!showDefaultRealm}",disabled:"{!showDefaultRealm}"}}],column2:[],columnB:[{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),allowBlank:!0,deleteEmpty:!0}]}),Ext.define("Proxmox.window.TfaLoginWindow",{extend:"Ext.window.Window",mixins:["Proxmox.Mixin.CBind"],title:gettext("Second login factor required"),modal:!0,resizable:!1,width:512,layout:{type:"vbox",align:"stretch"},defaultButton:"tfaButton",viewModel:{data:{confirmText:gettext("Confirm Second Factor"),canConfirm:!1,availableChallenge:{}}},cancelled:!0,controller:{xclass:"Ext.app.ViewController",init:function(e){var t=this,a=t.getViewModel();if(!e.userid)throw"no userid given";if(!e.ticket)throw"no ticket given";var i=e.challenge;if(!i)throw"no challenge given";var o,n=t.getLastTabUsed();let r=-1,l=0,s=0,d=!1;for(const c of["webauthn","totp","recovery","u2f","yubico"]){var u=!!i[c];a.set("availableChallenge."+c,u),u&&(s++,"recovery"===c&&(d=!0),l===n||r<0)&&(r=l),l++}!s||1===s&&d&&!i.recovery.length?(t.lookup("cannotLogin").setVisible(!0),t.lookup("recoveryKey").setVisible(!1),e.down("tabpanel").setActiveTab(2)):(e.down("tabpanel").setActiveTab(r),i.recovery&&(e.challenge.recovery.length?(o=e.challenge.recovery.map(e=>Ext.String.format(gettext("ID {0}"),e)).join(", "),t.lookup("availableRecovery").update(Ext.String.htmlEncode(Ext.String.format(gettext("Available recovery keys: {0}"),o))),t.lookup("availableRecovery").setVisible(!0),e.challenge.recovery.length<=3&&t.lookup("recoveryLow").setVisible(!0)):(t.lookup("recoveryEmpty").setVisible(!0),t.lookup("recoveryKey").setVisible(!1))),i.webauthn&&0===r?t.loginWebauthn():i.u2f&&3===r&&t.loginU2F())},control:{tabpanel:{tabchange:function(e,t,a){a=a.down("field"),a&&a.setDisabled(!0),a=t.down("field"),a&&(a.setDisabled(!1),a.focus(),a.validate()),a=t.confirmText||gettext("Confirm Second Factor");this.getViewModel().set("confirmText",a),this.saveLastTabUsed(e,t)}},field:{validitychange:function(e,t){this.getViewModel().set("canConfirm",t)},afterrender:e=>e.focus()}},saveLastTabUsed:function(e,t){e=e.items.indexOf(t);window.localStorage.setItem("Proxmox.TFALogin.lastTab",JSON.stringify({id:e}))},getLastTabUsed:function(){var e=window.localStorage.getItem("Proxmox.TFALogin.lastTab");return"string"==typeof e?JSON.parse(e).id:null},onClose:function(){var e=this.getView();e.cancelled&&e.onReject()},cancel:function(){this.getView().close()},loginTotp:function(){var e=this.lookup("totp").getValue();this.finishChallenge("totp:"+e)},loginYubico:function(){var e=this.lookup("yubico").getValue();this.finishChallenge("yubico:"+e)},loginWebauthn:async function(){var t=this,e=t.getView(),e=(t.lookup("webAuthnWaiting").setVisible(!0),t.lookup("webAuthnError").setVisible(!1),e.challenge.webauthn);if("string"!=typeof e.string){e.string=e.publicKey.challenge,e.publicKey.challenge=Proxmox.Utils.base64url_to_bytes(e.string);for(const o of e.publicKey.allowCredentials)o.id=Proxmox.Utils.base64url_to_bytes(o.id)}var a=new AbortController;e.signal=a.signal;let i;try{i=await navigator.credentials.get(e)}catch(e){return this.getViewModel().set("canConfirm",!0),t.lookup("webAuthnError").setData({error:Ext.htmlEncode(e.toString())}),void t.lookup("webAuthnError").setVisible(!0)}finally{a=t.lookup("webAuthnWaiting");a&&a.setVisible(!1)}a={id:i.id,type:i.type,challenge:e.string,rawId:Proxmox.Utils.bytes_to_base64url(i.rawId),response:{authenticatorData:Proxmox.Utils.bytes_to_base64url(i.response.authenticatorData),clientDataJSON:Proxmox.Utils.bytes_to_base64url(i.response.clientDataJSON),signature:Proxmox.Utils.bytes_to_base64url(i.response.signature)}};await t.finishChallenge("webauthn:"+JSON.stringify(a))},loginU2F:async function(){var t=this;let o=t.getView();t.lookup("u2fWaiting").setVisible(!0),t.lookup("u2fError").setVisible(!1);let e;try{if((e=await new Promise((e,t)=>{try{var a=o.challenge.u2f,i=a.challenge;u2f.sign(i.appId,i.challenge,a.keys,e)}catch(e){t(e)}})).errorCode)throw Proxmox.Utils.render_u2f_error(e.errorCode);delete e.errorCode}catch(e){return this.getViewModel().set("canConfirm",!0),t.lookup("u2fError").setData({error:Ext.htmlEncode(e.toString())}),void t.lookup("u2fError").setVisible(!0)}finally{var a=t.lookup("u2fWaiting");a&&a.setVisible(!1)}await t.finishChallenge("u2f:"+JSON.stringify(e))},loginRecovery:function(){var e=this.lookup("recoveryKey").getValue();this.finishChallenge("recovery:"+e)},loginTFA:function(){this.getViewModel().set("canConfirm",!1);var e=this.getView();this[e.down("tabpanel").getActiveTab().handler]()},finishChallenge:function(e){var t=this.getView(),e=(t.cancelled=!1,{username:t.userid,"tfa-challenge":t.ticket,password:e}),a=t.onResolve,i=t.onReject;return t.close(),Proxmox.Async.api2({url:"/api2/extjs/access/ticket",method:"POST",params:e}).then(a).catch(i)}},listeners:{close:"onClose"},items:[{xtype:"tabpanel",region:"center",layout:"fit",bodyPadding:10,items:[{xtype:"panel",title:"WebAuthn",iconCls:"fa fa-fw fa-shield",confirmText:gettext("Start WebAuthn challenge"),handler:"loginWebauthn",bind:{disabled:"{!availableChallenge.webauthn}"},items:[{xtype:"box",html:gettext("Please insert your authentication device and press its button")},{xtype:"box",html:gettext("Waiting for second factor.")+'<i class="fa fa-refresh fa-spin fa-fw"></i>',reference:"webAuthnWaiting",hidden:!0},{xtype:"box",data:{error:""},tpl:'<i class="fa fa-warning warning"></i> {error}',reference:"webAuthnError",hidden:!0}]},{xtype:"panel",title:gettext("TOTP App"),iconCls:"fa fa-fw fa-clock-o",handler:"loginTotp",bind:{disabled:"{!availableChallenge.totp}"},items:[{xtype:"textfield",fieldLabel:gettext("Please enter your TOTP verification code"),labelWidth:300,name:"totp",disabled:!0,reference:"totp",allowBlank:!1,regex:/^[0-9]{2,16}$/,regexText:gettext("TOTP codes usually consist of six decimal digits"),inputAttrTpl:"autocomplete=one-time-code"}]},{xtype:"panel",title:gettext("Recovery Key"),iconCls:"fa fa-fw fa-file-text-o",handler:"loginRecovery",bind:{disabled:"{!availableChallenge.recovery}"},items:[{xtype:"box",reference:"cannotLogin",hidden:!0,html:'<i class="fa fa-exclamation-triangle warning"></i>'+Ext.String.format(gettext("No second factor left! Please contact an administrator!"),4)},{xtype:"box",reference:"recoveryEmpty",hidden:!0,html:'<i class="fa fa-exclamation-triangle warning"></i>'+Ext.String.format(gettext("No more recovery keys left! Please generate a new set!"),4)},{xtype:"box",reference:"recoveryLow",hidden:!0,html:'<i class="fa fa-exclamation-triangle warning"></i>'+Ext.String.format(gettext("Less than {0} recovery keys available. Please generate a new set after login!"),4)},{xtype:"box",reference:"availableRecovery",hidden:!0},{xtype:"textfield",fieldLabel:gettext("Please enter one of your single-use recovery keys"),labelWidth:300,name:"recoveryKey",disabled:!0,reference:"recoveryKey",allowBlank:!1,regex:/^[0-9a-f]{4}(-[0-9a-f]{4}){3}$/,regexText:gettext("Does not look like a valid recovery key")}]},{xtype:"panel",title:"U2F",iconCls:"fa fa-fw fa-shield",confirmText:gettext("Start U2F challenge"),handler:"loginU2F",bind:{disabled:"{!availableChallenge.u2f}"},tabConfig:{bind:{hidden:"{!availableChallenge.u2f}"}},items:[{xtype:"box",html:gettext("Please insert your authentication device and press its button")},{xtype:"box",html:gettext("Waiting for second factor.")+'<i class="fa fa-refresh fa-spin fa-fw"></i>',reference:"u2fWaiting",hidden:!0},{xtype:"box",data:{error:""},tpl:'<i class="fa fa-warning warning"></i> {error}',reference:"u2fError",hidden:!0}]},{xtype:"panel",title:gettext("Yubico OTP"),iconCls:"fa fa-fw fa-yahoo",handler:"loginYubico",bind:{disabled:"{!availableChallenge.yubico}"},tabConfig:{bind:{hidden:"{!availableChallenge.yubico}"}},items:[{xtype:"textfield",fieldLabel:gettext("Please enter your Yubico OTP code"),labelWidth:300,name:"yubico",disabled:!0,reference:"yubico",allowBlank:!1,regex:/^[a-z0-9]{30,60}$/,regexText:gettext("TOTP codes consist of six decimal digits")}]}]}],buttons:[{handler:"loginTFA",reference:"tfaButton",disabled:!0,bind:{text:"{confirmText}",disabled:"{!canConfirm}"}}]}),Ext.define("Proxmox.window.AddTfaRecovery",{extend:"Proxmox.window.Edit",alias:"widget.pmxAddTfaRecovery",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",isCreate:!0,isAdd:!0,subject:gettext("TFA recovery keys"),width:512,method:"POST",fixedUser:!1,url:"/api2/extjs/access/tfa",submitUrl:function(e,t){var a=t.userid;return delete t.userid,e+"/"+a},apiCallDone:function(e,t){e&&(e=t.result.data.recovery.map((e,t)=>t+": "+e).join("\n"),Ext.create("Proxmox.window.TfaRecoveryShow",{autoShow:!0,userid:this.getViewModel().get("userid"),values:e}))},viewModel:{data:{has_entry:!1,userid:null}},controller:{xclass:"Ext.app.ViewController",hasEntry:async function(e){var t=this.getView();try{return await Proxmox.Async.api2({url:t.url+`/${e}/recovery`,method:"GET"}),!0}catch(e){return!1}},init:function(e){this.onUseridChange(null,Proxmox.UserName)},onUseridChange:async function(e,t){var a=this.getViewModel(),t=(this.userid=t,a.set("userid",t),await this.hasEntry(t));a.set("has_entry",t)}},items:[{xtype:"pmxDisplayEditField",name:"userid",cbind:{editable:e=>!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1,validator:function(e){return!this.up("window").getViewModel().get("has_entry")}},renderer:Ext.String.htmlEncode,listeners:{change:"onUseridChange"}},{xtype:"hiddenfield",name:"type",value:"recovery"},{xtype:"displayfield",bind:{hidden:"{!has_entry}"},hidden:!0,userCls:"pmx-hint",value:gettext("User already has recovery keys.")},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}),Ext.define("Proxmox.window.TfaRecoveryShow",{extend:"Ext.window.Window",alias:["widget.pmxTfaRecoveryShow"],mixins:["Proxmox.Mixin.CBind"],width:600,modal:!0,resizable:!1,title:gettext("Recovery Keys"),onEsc:Ext.emptyFn,items:[{xtype:"form",layout:"anchor",bodyPadding:10,border:!1,fieldDefaults:{anchor:"100%"},items:[{xtype:"textarea",editable:!1,inputId:"token-secret-value",cbind:{value:"{values}"},fieldStyle:{fontFamily:"monospace"},height:"160px"},{xtype:"displayfield",border:!1,padding:"5 0 0 0",userCls:"pmx-hint",value:gettext("Please record recovery keys - they will only be displayed now")}]}],buttons:[{handler:function(e){document.getElementById("token-secret-value").select(),document.execCommand("copy")},iconCls:"fa fa-clipboard",text:gettext("Copy Recovery Keys")},{handler:function(e){var t=this.up("window");t.paperkeys(t.values,t.userid)},iconCls:"fa fa-print",text:gettext("Print Recovery Keys")}],paperkeys:function(e,t){let a=document.createElement("iframe");Object.assign(a.style,{position:"fixed",right:"0",bottom:"0",width:"0",height:"0",border:"0"});var i=document.location.host,o=document.title;a.src="data:text/html;base64,"+btoa(`<html><head><script>
	    window.addEventListener('DOMContentLoaded', (ev) => window.print());
	</script><style>@media print and (max-height: 150mm) {
	  h4, p { margin: 0; font-size: 1em; }
	}</style></head><body style="padding: 5px;">
	<h4>Recovery Keys for '${t}' - ${o} (${i})</h4>
<p style="font-size:1.5em;line-height:1.5em;font-family:monospace;
   white-space:pre-wrap;overflow-wrap:break-word;">
${e}
</p>
	</body></html>`),document.body.appendChild(a),this.on("destroy",()=>document.body.removeChild(a))}}),Ext.define("Proxmox.window.AddTotp",{extend:"Proxmox.window.Edit",alias:"widget.pmxAddTotp",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Add a TOTP login factor"),width:512,layout:{type:"vbox",align:"stretch"},isAdd:!0,userid:void 0,tfa_id:void 0,issuerName:"Proxmox - "+(document?.location?.hostname||"unknown"),fixedUser:!1,updateQrCode:function(){var e=this,t=e.lookup("totp_form").getValues();let a=t.algorithm;a=a||"SHA1";t="otpauth://totp/"+encodeURIComponent(t.issuer)+":"+encodeURIComponent(t.userid)+"?secret="+t.secret+"&period="+t.step+"&digits="+t.digits+"&algorithm="+a+"&issuer="+encodeURIComponent(t.issuer);e.getController().getViewModel().set("otpuri",t),e.qrcode.makeCode(t),e.lookup("challenge").setVisible(!0),e.down("#qrbox").setVisible(!0)},viewModel:{data:{valid:!1,secret:"",otpuri:"",userid:null},formulas:{secretEmpty:function(e){return 0===e("secret").length}}},controller:{xclass:"Ext.app.ViewController",control:{"field[qrupdate=true]":{change:function(){this.getView().updateQrCode()}},field:{validitychange:function(e,t){var a=this.getViewModel(),i=this.lookup("totp_form"),o=this.lookup("challenge"),n=this.lookup("password");a.set("valid",i.isValid()&&o.isValid()&&n.isValid())}},"#":{show:function(){var e=this.getView();e.qrdiv=document.createElement("div"),e.qrcode=new QRCode(e.qrdiv,{width:256,height:256,correctLevel:QRCode.CorrectLevel.M}),e.down("#qrbox").getEl().appendChild(e.qrdiv),e.getController().randomizeSecret()}}},randomizeSecret:function(){var e=new Uint8Array(32);window.crypto.getRandomValues(e);let t="";e.forEach(function(e){e&=31,t+=e<26?String.fromCharCode(e+65):String.fromCharCode(e-26+50)}),this.getViewModel().set("secret",t)}},items:[{xtype:"form",layout:"anchor",border:!1,reference:"totp_form",fieldDefaults:{anchor:"100%"},items:[{xtype:"pmxDisplayEditField",name:"userid",cbind:{editable:e=>e("isAdd")&&!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},renderer:Ext.String.htmlEncode,listeners:{change:function(e,t,a){this.up("window").getViewModel().set("userid",t)}},qrupdate:!0},{xtype:"textfield",fieldLabel:gettext("Description"),emptyText:gettext("For example: TFA device ID, required to identify multiple factors."),allowBlank:!1,name:"description",maxLength:256},{layout:"hbox",border:!1,padding:"0 0 5 0",items:[{xtype:"textfield",fieldLabel:gettext("Secret"),emptyText:gettext("Unchanged"),name:"secret",reference:"tfa_secret",regex:/^[A-Z2-7=]+$/,regexText:"Must be base32 [A-Z2-7=]",maskRe:/[A-Z2-7=]/,qrupdate:!0,bind:{value:"{secret}"},flex:4,padding:"0 5 0 0"},{xtype:"button",text:gettext("Randomize"),reference:"randomize_button",handler:"randomizeSecret",flex:1}]},{xtype:"numberfield",fieldLabel:gettext("Time period"),name:"step",hidden:!0,value:30,minValue:10,qrupdate:!0},{xtype:"numberfield",fieldLabel:gettext("Digits"),name:"digits",value:6,hidden:!0,minValue:6,maxValue:8,qrupdate:!0},{xtype:"textfield",fieldLabel:gettext("Issuer Name"),name:"issuer",cbind:{value:"{issuerName}"},qrupdate:!0},{xtype:"box",itemId:"qrbox",visible:!1,bind:{visible:"{!secretEmpty}"},style:{margin:"16px auto",padding:"16px",width:"288px",height:"288px","background-color":"white"}},{xtype:"textfield",fieldLabel:gettext("Verify Code"),allowBlank:!1,reference:"challenge",name:"challenge",bind:{disabled:"{!showTOTPVerifiction}",visible:"{showTOTPVerifiction}"},emptyText:gettext("Scan QR code in a TOTP app and enter an auth. code here")},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}],initComponent:function(){this.url="/api2/extjs/access/tfa/",this.method="POST",this.callParent()},getValues:function(e){var t=this.getController().getViewModel(),a=this.callParent(arguments),i=encodeURIComponent(a.userid),i=(this.url="/api2/extjs/access/tfa/"+i,delete a.userid,{description:a.description,type:"totp",totp:t.get("otpuri"),value:a.challenge});return a.password&&(i.password=a.password),i}}),Ext.define("Proxmox.window.AddWebauthn",{extend:"Ext.window.Window",alias:"widget.pmxAddWebauthn",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Add a Webauthn login token"),width:512,user:void 0,fixedUser:!1,initComponent:function(){this.callParent(),Ext.GlobalEvents.fireEvent("proxmoxShowHelp",this.onlineHelp)},viewModel:{data:{valid:!1,userid:null}},controller:{xclass:"Ext.app.ViewController",control:{field:{validitychange:function(e,t){var a=this.getViewModel(),i=this.lookup("webauthn_form");a.set("valid",i.isValid())}},"#":{show:function(){var e=this.getView();"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0))}}},registerWebauthn:async function(){var t=this.lookup("webauthn_form").getValues(),a=(t.type="webauthn",t.user);delete t.user,this.getView().mask(gettext("Please wait..."),"x-mask-loading");try{var i=(await Proxmox.Async.api2({url:"/api2/extjs/access/tfa/"+a,method:"POST",params:t})).result.data;if(!i.challenge)throw"server did not respond with a challenge";var o=JSON.parse(i.challenge),n=o.publicKey.challenge,r=(o.publicKey.challenge=Proxmox.Utils.base64url_to_bytes(n),o.publicKey.user.id=Proxmox.Utils.base64url_to_bytes(o.publicKey.user.id),o.publicKey.excludeCredentials=(o.publicKey.excludeCredentials||[]).map(e=>({id:Proxmox.Utils.base64url_to_bytes(e.id),type:e.type})),Ext.Msg.show({title:"Webauthn: "+gettext("Setup"),message:gettext("Please press the button on your Webauthn Device"),buttons:[]}));let e;try{e=await navigator.credentials.create(o)}catch(e){let t=e.message;throw"InvalidStateError"===e.name&&(t=gettext("Is this token already registered?")),gettext("An error occurred during token registration.")+(`<br>${e.name}: `+t)}var l={id:e.id,type:e.type,rawId:Proxmox.Utils.bytes_to_base64url(e.rawId),response:{attestationObject:Proxmox.Utils.bytes_to_base64url(e.response.attestationObject),clientDataJSON:Proxmox.Utils.bytes_to_base64url(e.response.clientDataJSON)}},s=(r.close(),{type:"webauthn",challenge:n,value:JSON.stringify(l)});t.password&&(s.password=t.password),await Proxmox.Async.api2({url:"/api2/extjs/access/tfa/"+a,method:"POST",params:s})}catch(e){let t=e;console.error(t),"object"==typeof t&&(t=t.result?.message),Ext.Msg.alert(gettext("Error"),t)}this.getView().close()}},items:[{xtype:"form",reference:"webauthn_form",layout:"anchor",border:!1,bodyPadding:10,fieldDefaults:{anchor:"100%"},items:[{xtype:"pmxDisplayEditField",name:"user",cbind:{editable:e=>!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},renderer:Ext.String.htmlEncode,listeners:{change:function(e,t,a){this.up("window").getViewModel().set("userid",t)}}},{xtype:"textfield",fieldLabel:gettext("Description"),allowBlank:!1,name:"description",maxLength:256,emptyText:gettext("For example: TFA device ID, required to identify multiple factors.")},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}],buttons:[{xtype:"proxmoxHelpButton"},"->",{xtype:"button",text:gettext("Register Webauthn Device"),handler:"registerWebauthn",bind:{disabled:"{!valid}"}}]}),Ext.define("Proxmox.window.AddYubico",{extend:"Proxmox.window.Edit",alias:"widget.pmxAddYubico",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Add a Yubico OTP key"),width:512,isAdd:!0,userid:void 0,fixedUser:!1,initComponent:function(){this.url="/api2/extjs/access/tfa/",this.method="POST",this.callParent()},viewModel:{data:{valid:!1,userid:null}},controller:{xclass:"Ext.app.ViewController",control:{field:{validitychange:function(e,t){var a=this.getViewModel(),i=this.lookup("yubico_form");a.set("valid",i.isValid())}},"#":{show:function(){var e=this.getView();"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0))}}}},items:[{xtype:"form",reference:"yubico_form",layout:"anchor",border:!1,bodyPadding:10,fieldDefaults:{anchor:"100%"},items:[{xtype:"pmxDisplayEditField",name:"userid",cbind:{editable:e=>!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},renderer:Ext.String.htmlEncode,listeners:{change:function(e,t,a){this.up("window").getViewModel().set("userid",t)}}},{xtype:"textfield",fieldLabel:gettext("Description"),allowBlank:!1,name:"description",maxLength:256,emptyText:gettext("For example: TFA device ID, required to identify multiple factors.")},{xtype:"textfield",fieldLabel:gettext("Yubico OTP Key"),emptyText:gettext("A currently valid Yubico OTP value"),name:"otp_value",maxLength:44,enforceMaxLength:!0,regex:/^[a-zA-Z0-9]{44}$/,regexText:"44 characters",maskRe:/^[a-zA-Z0-9]$/},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}},{xtype:"box",html:`<span class='pmx-hint'>${gettext("Tip:")}</span> `+gettext("YubiKeys also support WebAuthn, which is often a better alternative.")}]}],getValues:function(e){var t=this.callParent(arguments),a=encodeURIComponent(t.userid),a=(this.url="/api2/extjs/access/tfa/"+a,delete t.userid,{description:t.description,type:"yubico",value:t.otp_value});return t.password&&(a.password=t.password),a}}),Ext.define("Proxmox.window.TfaEdit",{extend:"Proxmox.window.Edit",alias:"widget.pmxTfaEdit",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Modify a TFA entry's description"),width:512,layout:{type:"vbox",align:"stretch"},cbindData:function(e){var t=this,e=e["tfa-id"];return t.tfa_id=e,t.defaultFocus="textfield[name=description]",t.url="/api2/extjs/access/tfa/"+e,t.method="PUT",t.autoLoad=!0,{}},initComponent:function(){var e=this,t=(e.callParent(),"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0)),e.tfa_id.split("/")[0]);e.lookup("userid").setValue(t)},items:[{xtype:"displayfield",reference:"userid",editable:!1,fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},cbind:{value:()=>Proxmox.UserName}},{xtype:"proxmoxtextfield",name:"description",allowBlank:!1,fieldLabel:gettext("Description")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Enabled"),name:"enable",uncheckedValue:0,defaultValue:1,checked:!0},{xtype:"textfield",inputType:"password",fieldLabel:gettext("Password"),minLength:5,reference:"password",name:"password",allowBlank:!1,validateBlank:!0,emptyText:gettext("verify current password")}],getValues:function(){var e=this.callParent(arguments);return delete e.userid,e}}),Ext.define("Proxmox.tfa.confirmRemove",{extend:"Proxmox.window.Edit",mixins:["Proxmox.Mixin.CBind"],title:gettext("Confirm TFA Removal"),modal:!0,resizable:!1,width:600,isCreate:!0,isRemove:!0,url:"/access/tfa",initComponent:function(){var e=this;if("string"!=typeof e.type)throw"missing type";if(!e.callback)throw"missing callback";e.callParent(),"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0))},submit:function(){"root@pam"===Proxmox.UserName?this.callback(null):this.callback(this.lookup("password").getValue()),this.close()},items:[{xtype:"box",padding:"0 0 10 0",html:Ext.String.format(gettext("Are you sure you want to remove this {0} entry?"),"TFA")},{xtype:"container",layout:{type:"hbox",align:"begin"},defaults:{border:!1,layout:"anchor",flex:1,padding:5},items:[{xtype:"container",layout:{type:"vbox"},padding:"0 10 0 0",items:[{xtype:"displayfield",fieldLabel:gettext("User"),cbind:{value:"{userid}"}},{xtype:"displayfield",fieldLabel:gettext("Type"),cbind:{value:"{type}"}}]},{xtype:"container",layout:{type:"vbox"},padding:"0 0 0 10",items:[{xtype:"displayfield",fieldLabel:gettext("Created"),renderer:e=>Proxmox.Utils.render_timestamp(e),cbind:{value:"{created}"}},{xtype:"textfield",fieldLabel:gettext("Description"),cbind:{value:"{description}"},emptyText:Proxmox.Utils.NoneText,submitValue:!1,editable:!1}]}]},{xtype:"textfield",inputType:"password",fieldLabel:gettext("Password"),minLength:5,reference:"password",name:"password",allowBlank:!1,validateBlank:!0,padding:"10 0 0 0",cbind:{emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}),Ext.define("Proxmox.window.NotesEdit",{extend:"Proxmox.window.Edit",title:gettext("Notes"),onlineHelp:"markdown_basics",width:800,height:600,resizable:!0,layout:"fit",autoLoad:!0,defaultButton:void 0,setMaxLength:function(e){var t=this.down('textarea[name="description"]');return t.maxLength=e,t.validate(),this},items:{xtype:"textarea",name:"description",height:"100%",value:"",hideLabel:!0,emptyText:gettext("You can use Markdown for rich text formatting."),fieldStyle:{"white-space":"pre-wrap","font-family":"monospace"}}}),Ext.define("Proxmox.window.ThemeEditWindow",{extend:"Ext.window.Window",alias:"widget.pmxThemeEditWindow",viewModel:{parent:null,data:{}},controller:{xclass:"Ext.app.ViewController",init:function(e){let t="__default__";e=Ext.util.Cookies.get(e.cookieName);e&&e in Proxmox.Utils.theme_map&&(t=e),this.getViewModel().set("theme",t)},applyTheme:function(e){var t=this.getView(),a=this.getViewModel(),i=Ext.Date.add(new Date,Ext.Date.YEAR,10);Ext.util.Cookies.set(t.cookieName,a.get("theme"),i),t.mask(gettext("Please wait..."),"x-mask-loading"),window.location.reload()}},cookieName:"PVEThemeCookie",title:gettext("Color Theme"),modal:!0,bodyPadding:10,resizable:!1,items:[{xtype:"proxmoxThemeSelector",fieldLabel:gettext("Color Theme"),bind:{value:"{theme}"}}],buttons:[{text:gettext("Apply"),handler:"applyTheme"}]}),Ext.define("Proxmox.window.SyncWindow",{extend:"Ext.window.Window",title:gettext("Realm Sync"),width:600,bodyPadding:10,modal:!0,resizable:!1,controller:{xclass:"Ext.app.ViewController",control:{form:{validitychange:function(e,t){this.lookup("preview_btn").setDisabled(!t),this.lookup("sync_btn").setDisabled(!t)}},button:{click:function(e){this.sync_realm("preview_btn"===e.reference)}}},sync_realm:function(t){let a=this.getView();let i=this.lookup("ipanel").getValues(),o=[];["acl","entry","properties"].forEach(e=>{i["remove-vanished-"+e]&&o.push(e),delete i["remove-vanished-"+e]}),0<o.length&&(i["remove-vanished"]=o.join(";")),i["dry-run"]=t?1:0,Proxmox.Utils.API2Request({url:`/access/domains/${a.realm}/sync`,waitMsgTarget:a,method:"POST",params:i,failure:e=>{a.show(),Ext.Msg.alert(gettext("Error"),e.htmlStatus)},success:e=>{a.hide(),Ext.create("Proxmox.window.TaskViewer",{upid:e.result.data,listeners:{destroy:()=>{t?a.show():a.close()}}}).show()}})}},items:[{xtype:"form",reference:"form",border:!1,fieldDefaults:{labelWidth:100,anchor:"100%"},items:[{xtype:"inputpanel",reference:"ipanel",column1:[{xtype:"proxmoxKVComboBox",value:"true",deleteEmpty:!1,allowBlank:!1,comboItems:[["true",Proxmox.Utils.yesText],["false",Proxmox.Utils.noText]],name:"enable-new",fieldLabel:gettext("Enable new")}],column2:[],columnB:[{xtype:"fieldset",title:gettext("Remove Vanished Options"),items:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("ACL"),name:"remove-vanished-acl",boxLabel:gettext("Remove ACLs of vanished users and groups.")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Entry"),name:"remove-vanished-entry",boxLabel:gettext("Remove vanished user and group entries.")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Properties"),name:"remove-vanished-properties",boxLabel:gettext("Remove vanished properties from synced users.")}]},{xtype:"displayfield",reference:"defaulthint",value:gettext("Default sync options can be set by editing the realm."),userCls:"pmx-hint",hidden:!0}]}]}],buttons:["->",{text:gettext("Preview"),reference:"preview_btn"},{text:gettext("Sync"),reference:"sync_btn"}],initComponent:function(){if(!this.realm)throw"no realm defined";if(!this.type)throw"no realm type defined";this.callParent(),Proxmox.Utils.API2Request({url:`/config/access/${this.type}/`+this.realm,waitMsgTarget:this,method:"GET",failure:e=>{Ext.Msg.alert(gettext("Error"),e.htmlStatus),this.close()},success:e=>{e=e.result.data["sync-defaults-options"];if(e){var t=Proxmox.Utils.parsePropertyString(e);if(t["remove-vanished"])for(const a of t["remove-vanished"].split(";"))t["remove-vanished-"+a]=1;this.lookup("ipanel").setValues(t)}else this.lookup("defaulthint").setVisible(!0);this.lookup("form").isValid()}})}}),Ext.define("apt-pkglist",{extend:"Ext.data.Model",fields:["Package","Title","Description","Section","Arch","Priority","Version","OldVersion","Origin"],idProperty:"Package"}),Ext.define("Proxmox.node.APT",{extend:"Ext.grid.GridPanel",xtype:"proxmoxNodeAPT",upgradeBtn:void 0,columns:[{header:gettext("Package"),width:200,sortable:!0,dataIndex:"Package"},{text:gettext("Version"),columns:[{header:gettext("current"),width:100,sortable:!1,dataIndex:"OldVersion"},{header:gettext("new"),width:100,sortable:!1,dataIndex:"Version"}]},{header:gettext("Description"),sortable:!1,dataIndex:"Title",flex:1}],initComponent:function(){let n=this;if(!n.nodename)throw"no node name specified";let t=Ext.create("Ext.data.Store",{model:"apt-pkglist",groupField:"Origin",proxy:{type:"proxmox",url:`/api2/json/nodes/${n.nodename}/apt/update`},sorters:[{property:"Package",direction:"ASC"}]});Proxmox.Utils.monStoreErrors(n,t,!0);var e=Ext.create("Ext.grid.feature.Grouping",{groupHeaderTpl:'{[ "Origin: " + values.name ]} ({rows.length} Item{[values.rows.length > 1 ? "s" : ""]})',enableGroupingMenu:!1}),a=Ext.create("Ext.grid.feature.RowBody",{getAdditionalData:function(e,t,a,i){var o=this.view.headerCt.getColumnCount();return{rowBody:`<div style="padding: 1em">${Ext.htmlEncode(e.Description)}</div>`,rowBodyCls:n.full_description?"":Ext.baseCSSPrefix+"grid-row-body-hidden",rowBodyColspan:o}}});var i=Ext.create("Ext.selection.RowModel",{}),o=new Ext.Button({text:gettext("Refresh"),handler:()=>Proxmox.Utils.checked_command(function(){var e;e="update",Proxmox.Utils.API2Request({url:`/nodes/${n.nodename}/apt/`+e,method:"POST",success:({result:e})=>Ext.create("Proxmox.window.TaskViewer",{autoShow:!0,upid:e.data,listeners:{close:()=>t.load()}})})})});function r(e){if(e?.data?.Package){let a=Ext.createWidget("component",{autoScroll:!0,style:{"white-space":"pre","font-family":"monospace",padding:"5px"}}),i=Ext.create("Ext.window.Window",{title:gettext("Changelog")+": "+e.data.Package,width:800,height:600,layout:"fit",modal:!0,items:[a]});Proxmox.Utils.API2Request({waitMsgTarget:n,url:"/nodes/"+n.nodename+"/apt/changelog",params:{name:e.data.Package,version:e.data.Version},method:"GET",failure:function(e,t){i.close(),Ext.Msg.alert(gettext("Error"),e.htmlStatus)},success:function(e,t){i.show(),a.update(Ext.htmlEncode(e.result.data))}})}else console.debug("cannot show changelog, missing Package",e)}var l=new Proxmox.button.Button({text:gettext("Changelog"),selModel:i,disabled:!0,enableFn:e=>!!e?.data?.Package,handler:(e,t,a)=>r(a)}),s=new Ext.form.field.Checkbox({boxLabel:gettext("Show details"),value:!1,listeners:{change:(e,t)=>{n.full_description=t,n.getView().refresh()}}});n.upgradeBtn?n.tbar=[o,n.upgradeBtn,l,"->",s]:n.tbar=[o,l,"->",s],Ext.apply(n,{store:t,stateful:!0,stateId:"grid-update",selModel:i,viewConfig:{stripeRows:!1,emptyText:`<div style="display:flex;justify-content:center;"><p>${gettext("No updates available.")}</p></div>`},features:[e,a],listeners:{activate:()=>t.load(),itemdblclick:(e,t)=>r(t)}}),n.callParent()}}),Ext.define("apt-repolist",{extend:"Ext.data.Model",fields:["Path","Index","Origin","FileType","Enabled","Comment","Types","URIs","Suites","Components","Options"]}),Ext.define("Proxmox.window.APTRepositoryAdd",{extend:"Proxmox.window.Edit",alias:"widget.pmxAPTRepositoryAdd",isCreate:!0,isAdd:!0,subject:gettext("Repository"),width:600,initComponent:function(){let o=this;if(!o.repoInfo||0===o.repoInfo.length)throw"repository information not initialized";let i=Ext.create("Ext.form.field.Display",{fieldLabel:gettext("Description"),name:"description"}),n=Ext.create("Ext.form.field.Display",{fieldLabel:gettext("Status"),name:"status",renderer:function(e){let t=gettext("Not yet configured");return t=""!==e?Ext.String.format("{0}: {1}",gettext("Configured"),e?gettext("enabled"):gettext("disabled")):t}});var e=Ext.create("Proxmox.form.KVComboBox",{fieldLabel:gettext("Repository"),xtype:"proxmoxKVComboBox",name:"handle",allowBlank:!1,comboItems:o.repoInfo.map(e=>[e.handle,e.name]),validator:function(e){let t=this.value;var a,i=Proxmox.form.KVComboBox.prototype.validator.call(this,e);return!(!i||!t||!(a=o.repoInfo.find(e=>e.handle===t)))&&(a.status?Ext.String.format(gettext("{0} is already configured"),e):i)},listeners:{change:function(e,t){var a=o.repoInfo.find(e=>e.handle===t);i.setValue(a.description),n.setValue(a.status)}}});e.setValue(o.repoInfo[0].handle),Ext.apply(o,{items:[e,i,n],repoSelector:e}),o.callParent()}}),Ext.define("Proxmox.node.APTRepositoriesErrors",{extend:"Ext.grid.GridPanel",xtype:"proxmoxNodeAPTRepositoriesErrors",store:{},scrollable:!0,viewConfig:{stripeRows:!1,getRowClass:e=>{switch(e.data.status){case"warning":return"proxmox-warning-row";case"critical":return"proxmox-invalid-row";default:return""}}},hideHeaders:!0,columns:[{dataIndex:"status",renderer:e=>`<i class="fa fa-fw ${Proxmox.Utils.get_health_icon(e,!0)}"></i>`,width:50},{dataIndex:"message",flex:1}]}),Ext.define("Proxmox.node.APTRepositoriesGrid",{extend:"Ext.grid.GridPanel",xtype:"proxmoxNodeAPTRepositoriesGrid",mixins:["Proxmox.Mixin.CBind"],title:gettext("APT Repositories"),cls:"proxmox-apt-repos",border:!1,tbar:[{text:gettext("Reload"),iconCls:"fa fa-refresh",handler:function(){this.up("proxmoxNodeAPTRepositories").reload()}},{text:gettext("Add"),name:"addRepo",disabled:!0,repoInfo:void 0,cbind:{onlineHelp:"{onlineHelp}"},handler:function(e,t,a){Proxmox.Utils.checked_command(()=>{let e=this.up("proxmoxNodeAPTRepositories");var t={};void 0!==e.digest&&(t.digest=e.digest),Ext.create("Proxmox.window.APTRepositoryAdd",{repoInfo:this.repoInfo,url:`/api2/extjs/nodes/${e.nodename}/apt/repositories`,method:"PUT",extraRequestParams:t,onlineHelp:this.onlineHelp,listeners:{destroy:function(){e.reload()}}}).show()})}},"-",{xtype:"proxmoxAltTextButton",defaultText:gettext("Enable"),altText:gettext("Disable"),name:"repoEnable",disabled:!0,bind:{text:"{enableButtonText}"},handler:function(e,t,a){let i=this.up("proxmoxNodeAPTRepositories");a={path:a.data.Path,index:a.data.Index,enabled:a.data.Enabled?0:1};void 0!==i.digest&&(a.digest=i.digest),Proxmox.Utils.API2Request({url:`/nodes/${i.nodename}/apt/repositories`,method:"POST",params:a,failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus),i.reload()},success:function(e,t){i.reload()}})}}],sortableColumns:!1,viewConfig:{stripeRows:!1,getRowClass:(e,t)=>e.get("Enabled")?"":"proxmox-disabled-row"},columns:[{header:gettext("Enabled"),dataIndex:"Enabled",align:"center",renderer:Proxmox.Utils.renderEnabledIcon,width:90},{header:gettext("Types"),dataIndex:"Types",renderer:function(e,t,a){return e.join(" ")},width:100},{header:gettext("URIs"),dataIndex:"URIs",renderer:function(e,t,a){return e.join(" ")},width:350},{header:gettext("Suites"),dataIndex:"Suites",renderer:function(e,a,i){let o="";if(i.data.warnings&&0<i.data.warnings.length){let t=[gettext("Warning")];i.data.warnings.forEach(e=>{"Suites"===e.property&&t.push(Ext.htmlEncode(e.message))}),a.tdAttr=`data-qtip="${Ext.htmlEncode(t.join("<br>"))}"`,o=i.data.Enabled?(a.tdCls="proxmox-invalid-row",'<i class="fa fa-fw critical fa-exclamation-circle"></i> '):(a.tdCls="proxmox-warning-row",'<i class="fa fa-fw warning fa-exclamation-circle"></i> ')}return e.join(" ")+o},width:130},{header:gettext("Components"),dataIndex:"Components",renderer:function(e,t,a){if(void 0===e)return"";let i="";var o;return 1===e.length&&e[0].match(/\w+(-no-subscription|test)\s*$/i)&&(t.tdCls="proxmox-warning-row",i='<i class="fa fa-fw warning fa-exclamation-circle"></i> ',o=e[0].match(/no-subscription/)?gettext("The no-subscription repository is NOT production-ready"):gettext("The test repository may contain unstable updates"),t.tdAttr=`data-qtip="${Ext.htmlEncode(Ext.htmlEncode(o))}"`),e.join(" ")+i},width:170},{header:gettext("Options"),dataIndex:"Options",renderer:function(e,t,a){if(!e)return"";let i=a.data.FileType,o="";return e.forEach(function(e){var t=e.Key;if("list"===i){var a=e.Values.join(",");o+=t+`=${a} `}else{if("sources"!==i)throw"unknown file type";a=e.Values.join(" ");o+=t+`: ${a}<br>`}}),o},flex:1},{header:gettext("Origin"),dataIndex:"Origin",width:120,renderer:function(e,t,a){"string"==typeof e&&0!==e.length||(e=gettext("Other"));let i="fa fa-fw fa-question-circle-o";var o=this.up("proxmoxNodeAPTRepositories").classifyOrigin(e);return"Proxmox"===o?i="pmx-itype-icon pmx-itype-icon-proxmox-x":"Debian"===o&&(i="pmx-itype-icon pmx-itype-icon-debian-swirl"),`<i class='${i}'></i> `+e}},{header:gettext("Comment"),dataIndex:"Comment",flex:2,renderer:Ext.String.htmlEncode}],features:[{ftype:"grouping",groupHeaderTpl:'{[ "File: " + values.name ]} ({rows.length} repositor{[values.rows.length > 1 ? "ies" : "y"]})',enableGroupingMenu:!1}],store:{model:"apt-repolist",groupField:"Path",sorters:[{property:"Index",direction:"ASC"}]},initComponent:function(){if(!this.nodename)throw"no node name specified";this.callParent()}}),Ext.define("Proxmox.node.APTRepositories",{extend:"Ext.panel.Panel",xtype:"proxmoxNodeAPTRepositories",mixins:["Proxmox.Mixin.CBind"],digest:void 0,onlineHelp:void 0,product:"Proxmox VE",classifyOrigin:function(e){return(e||="").match(/^\s*Proxmox\s*$/i)?"Proxmox":e.match(/^\s*Debian\s*(:?Backports)?$/i)?"Debian":"Other"},controller:{xclass:"Ext.app.ViewController",selectionChange:function(e,t){var a;!t||t.length<1||(t=t[0],(a=this.getViewModel()).set("selectionenabled",t.get("Enabled")),a.notify())},updateState:function(){var e=this.getViewModel();let a=e.get("errorstore"),i=(a.removeAll(),"good"),o=gettext("All OK, you have production-ready repositories configured!");var t=e=>a.add({status:"good",message:e});let n=(e,t)=>{"critical"!==i&&(i="warning",o=t?e:gettext("Warning")),a.add({status:"warning",message:e})},r=(e,t)=>{i="critical",o=t?e:gettext("Error"),a.add({status:"critical",message:e})};var l=e.get("errors");l.forEach(e=>r(e.path+" - "+e.error));let s=e.get("subscriptionActive");var d=e.get("enterpriseRepo"),u=e.get("noSubscriptionRepo"),c=e.get("testRepo"),x={enterprise:e.get("cephEnterpriseRepo"),nosubscription:e.get("cephNoSubscriptionRepo"),test:e.get("cephTestRepo")},m=e.get("suitesWarning"),p=e.get("mixedSuites"),t=(d||u||c?0<l.length||(d&&!u&&!c&&s?t(Ext.String.format(gettext("You get supported updates for {0}"),e.get("product"))):(u||c)&&t(Ext.String.format(gettext("You get updates for {0}"),e.get("product")))):r(Ext.String.format(gettext("No {0} repository is enabled, you do not get any updates!"),e.get("product"))),m&&n(gettext("Some suites are misconfigured")),p&&n(gettext("Detected mixed suites before upgrade")),(e,t,a)=>{!s&&e.enterprise&&n(Ext.String.format(gettext("The {0}enterprise repository is enabled, but there is no active subscription!"),t)),e.nosubscription&&n(Ext.String.format(gettext("The {0}no-subscription{1} repository is not recommended for production use!"),t,a)),e.test&&n(Ext.String.format(gettext("The {0}test repository may pull in unstable updates and is not recommended for production use!"),t))}),m=(t({enterprise:d,nosubscription:u,test:c},"",""),t(x,"Ceph ","/main"),0<l.length&&(o=gettext("Fatal parsing error for at least one repository")),Proxmox.Utils.get_health_icon(i,!0));e.set("state",{iconCls:m,text:o})}},viewModel:{data:{product:"Proxmox VE",errors:[],suitesWarning:!1,mixedSuites:!1,subscriptionActive:"",noSubscriptionRepo:"",enterpriseRepo:"",testRepo:"",cephEnterpriseRepo:"",cephNoSubscriptionRepo:"",cephTestRepo:"",selectionenabled:!1,state:{}},formulas:{enableButtonText:e=>e("selectionenabled")?gettext("Disable"):gettext("Enable")},stores:{errorstore:{fields:["status","message"]}}},scrollable:!0,layout:{type:"vbox",align:"stretch"},items:[{xtype:"panel",border:!1,layout:{type:"hbox",align:"stretch"},height:200,title:gettext("Status"),items:[{xtype:"box",flex:2,margin:10,data:{iconCls:Proxmox.Utils.get_health_icon(void 0,!0),text:""},bind:{data:"{state}"},tpl:['<center class="centered-flex-column" style="font-size:15px;line-height: 25px;">','<i class="fa fa-4x {iconCls}"></i>',"{text}","</center>"]},{xtype:"proxmoxNodeAPTRepositoriesErrors",name:"repositoriesErrors",flex:7,margin:10,bind:{store:"{errorstore}"}}]},{xtype:"proxmoxNodeAPTRepositoriesGrid",name:"repositoriesGrid",flex:1,cbind:{nodename:"{nodename}",onlineHelp:"{onlineHelp}"},majorUpgradeAllowed:!1,listeners:{selectionchange:"selectionChange"}}],check_subscription:function(){let a=this,i=a.getViewModel();Proxmox.Utils.API2Request({url:`/nodes/${a.nodename}/subscription`,method:"GET",failure:(e,t)=>Ext.Msg.alert(gettext("Error"),e.htmlStatus),success:function(e,t){e=e.result,e=!(!e||!e.data||"active"!==e.data.status.toLowerCase());i.set("subscriptionActive",e),a.getController().updateState()}})},updateStandardRepos:function(e){var t=this.getViewModel(),a=this.down("button[name=addRepo]");a.repoInfo=[];for(const n of e){var i=n.handle,o=n.status;"enterprise"===i?t.set("enterpriseRepo",o):"no-subscription"===i?t.set("noSubscriptionRepo",o):"test"===i?t.set("testRepo",o):i.match(/^ceph-[a-zA-Z]+-enterprise$/)?t.set("cephEnterpriseRepo",o):i.match(/^ceph-[a-zA-Z]+-no-subscription$/)?t.set("cephNoSubscriptionRepo",o):i.match(/^ceph-[a-zA-Z]+-test$/)&&t.set("cephTestRepo",o),this.getController().updateState(),a.repoInfo.push(n),a.digest=this.digest}a.setDisabled(!1)},reload:function(){let m=this,o=m.getViewModel(),p=m.down("proxmoxNodeAPTRepositoriesGrid");m.store.load(function(e,t,a){let n=[],i=[],r,l=!1,s=!1,d=!1;if(a&&0<e.length){a=e[0].data,e=a.files;i=a.errors,r=a.digest;let o={};for(const x of a.infos){var u=x.path,c=x.index;o[u]||(o[u]={}),o[u][c]||(o[u][c]={origin:"",warnings:[],gotIgnorePreUpgradeWarning:!1}),"origin"===x.kind?o[u][c].origin=x.message:"warning"===x.kind?o[u][c].warnings.push(x):"ignore-pre-upgrade-warning"===x.kind&&(o[u][c].gotIgnorePreUpgradeWarning=!0,p.majorUpgradeAllowed?s=!0:o[u][c].warnings.push(x))}e.forEach(function(t){for(let e=0;e<t.repositories.length;e++){var a,i=t.repositories[e];i.Path=t.path,i.Index=e,o[t.path]&&o[t.path][e]&&(i.Origin=o[t.path][e].origin||Proxmox.Utils.unknownText,i.warnings=o[t.path][e].warnings||[],i.Enabled)&&(i.warnings.some(e=>"Suites"===e.property)&&(l=!0),a=m.classifyOrigin(i.Origin),!s||!i.Types.includes("deb")||"Proxmox"!==a&&"Debian"!==a||o[t.path][e].gotIgnorePreUpgradeWarning||(d=!0)),n.push(i)}}),p.store.loadData(n),m.updateStandardRepos(a["standard-repos"])}m.digest=r,o.set("errors",i),o.set("suitesWarning",l),o.set("mixedSuites",d),m.getController().updateState()}),m.check_subscription()},listeners:{activate:function(){this.reload()}},initComponent:function(){var e=this;if(!e.nodename)throw"no node name specified";var t=Ext.create("Ext.data.Store",{proxy:{type:"proxmox",url:`/api2/json/nodes/${e.nodename}/apt/repositories`}});Ext.apply(e,{store:t}),Proxmox.Utils.monStoreErrors(e,e.store,!0),e.callParent(),e.getViewModel().set("product",e.product)}}),Ext.define("Proxmox.node.NetworkEdit",{extend:"Proxmox.window.Edit",alias:["widget.proxmoxNodeNetworkEdit"],enableBridgeVlanIds:!1,initComponent:function(){let o=this;if(!o.nodename)throw"no node name specified";if(!o.iftype)throw"no network device type specified";o.isCreate=!o.iface;let n;if("bridge"===o.iftype)n="BridgeName";else if("bond"===o.iftype)n="BondName";else if("eth"!==o.iftype||o.isCreate)if("vlan"===o.iftype)n="VlanName";else if("OVSBridge"===o.iftype)n="BridgeName";else if("OVSBond"===o.iftype)n="BondName";else{if("OVSIntPort"!==o.iftype&&"OVSPort"!==o.iftype)throw console.log(o.iftype),"unknown network device type specified";n="InterfaceName"}else n="InterfaceName";o.subject=Proxmox.Utils.render_network_iface_type(o.iftype);var e,t=[],r=[],a=[],i=[],l=[];if("OVSIntPort"!==o.iftype&&"OVSPort"!==o.iftype&&"OVSBond"!==o.iftype&&r.push({xtype:"proxmoxcheckbox",fieldLabel:gettext("Autostart"),name:"autostart",uncheckedValue:0,checked:!!o.isCreate||void 0}),"bridge"===o.iftype){let a=o.enableBridgeVlanIds?Ext.create("Ext.form.field.Text",{fieldLabel:gettext("VLAN IDs"),name:"bridge_vids",emptyText:"2-4094",disabled:!0,autoEl:{tag:"div","data-qtip":gettext("List of VLAN IDs and ranges, useful for NICs with restricted VLAN offloading support. For example: '2 4 100-200'")},validator:function(e){if(e)for(const i of e.split(/\s+[,;]?/))if(i){var t=i.match(/^(\d+)(?:-(\d+))?$/);if(!t)return Ext.String.format(gettext("not a valid bridge VLAN ID entry: {0}"),i);var a=Number(t[1]),t=Number(t[2]??t[1]);if(Number.isNaN(a)||Number.isNaN(t))return Ext.String.format(gettext("VID range includes not-a-number: {0}"),i);if(t<a)return Ext.String.format(gettext("VID range must go from lower to higher tag: {0}"),i);if(a<2||4094<t)return Ext.String.format(gettext("VID range outside of allowed 2 and 4094 limit: {0}"),i)}return!0}}):void 0;r.push({xtype:"proxmoxcheckbox",fieldLabel:gettext("VLAN aware"),name:"bridge_vlan_aware",deleteEmpty:!o.isCreate,listeners:{change:function(e,t){a&&a.setDisabled(!t)}}}),r.push({xtype:"textfield",fieldLabel:gettext("Bridge ports"),name:"bridge_ports",autoEl:{tag:"div","data-qtip":gettext("Space-separated list of interfaces, for example: enp0s0 enp1s0")}}),a&&l.push(a)}else if("OVSBridge"===o.iftype)r.push({xtype:"textfield",fieldLabel:gettext("Bridge ports"),name:"ovs_ports",autoEl:{tag:"div","data-qtip":gettext("Space-separated list of interfaces, for example: enp0s0 enp1s0")}}),r.push({xtype:"textfield",fieldLabel:gettext("OVS options"),name:"ovs_options"});else if("OVSPort"===o.iftype||"OVSIntPort"===o.iftype)r.push({xtype:o.isCreate?"PVE.form.BridgeSelector":"displayfield",fieldLabel:Proxmox.Utils.render_network_iface_type("OVSBridge"),allowBlank:!1,nodename:o.nodename,bridgeType:"OVSBridge",name:"ovs_bridge"}),r.push({xtype:"proxmoxvlanfield",deleteEmpty:!o.isCreate,name:"ovs_tag",value:""}),r.push({xtype:"textfield",fieldLabel:gettext("OVS options"),name:"ovs_options"});else if("vlan"===o.iftype)o.isCreate?(o.disablevlanid=!0,o.disablevlanrawdevice=!0):(o.disablevlanid=!1,o.disablevlanrawdevice=!1,o.vlanrawdevicevalue="",o.vlanidvalue="",Proxmox.Utils.VlanInterface_match.test(o.iface)?(o.disablevlanid=!0,o.disablevlanrawdevice=!0,e=Proxmox.Utils.VlanInterface_match.exec(o.iface),o.vlanrawdevicevalue=e[1],o.vlanidvalue=e[2]):Proxmox.Utils.Vlan_match.test(o.iface)&&(o.disablevlanid=!0,e=Proxmox.Utils.Vlan_match.exec(o.iface),o.vlanidvalue=e[1])),r.push({xtype:"textfield",fieldLabel:gettext("Vlan raw device"),name:"vlan-raw-device",value:o.vlanrawdevicevalue,disabled:o.disablevlanrawdevice,allowBlank:!1}),r.push({xtype:"proxmoxvlanfield",name:"vlan-id",value:o.vlanidvalue,disabled:o.disablevlanid}),a.push({xtype:"label",userCls:"pmx-hint",text:"Either add the VLAN number to an existing interface name, or choose your own name and set the VLAN raw device (for the latter ifupdown1 supports vlanXY naming only)"});else if("bond"===o.iftype){r.push({xtype:"textfield",fieldLabel:gettext("Slaves"),name:"slaves"});let a=Ext.createWidget("bondPolicySelector",{fieldLabel:gettext("Hash policy"),name:"bond_xmit_hash_policy",deleteEmpty:!o.isCreate,disabled:!0}),i=Ext.createWidget("textfield",{fieldLabel:"bond-primary",name:"bond-primary",value:"",disabled:!0});r.push({xtype:"bondModeSelector",fieldLabel:gettext("Mode"),name:"bond_mode",value:o.isCreate?"balance-rr":void 0,listeners:{change:function(e,t){("balance-xor"===t||"802.3ad"===t?(a.setDisabled(!1),i.setDisabled(!0),i):"active-backup"===t?(i.setDisabled(!1),a.setDisabled(!0),a):(a.setDisabled(!0),a.setValue(""),i.setDisabled(!0),i)).setValue("")}},allowBlank:!1}),r.push(a),r.push(i)}else"OVSBond"===o.iftype&&(r.push({xtype:o.isCreate?"PVE.form.BridgeSelector":"displayfield",fieldLabel:Proxmox.Utils.render_network_iface_type("OVSBridge"),allowBlank:!1,nodename:o.nodename,bridgeType:"OVSBridge",name:"ovs_bridge"}),r.push({xtype:"proxmoxvlanfield",deleteEmpty:!o.isCreate,name:"ovs_tag",value:""}),r.push({xtype:"textfield",fieldLabel:gettext("OVS options"),name:"ovs_options"}));r.push({xtype:"textfield",fieldLabel:gettext("Comment"),allowBlank:!0,nodename:o.nodename,name:"comments"});let s,d;d=o.isCreate?(s="/api2/extjs/nodes/"+o.nodename+"/network","POST"):(s="/api2/extjs/nodes/"+o.nodename+"/network/"+o.iface,"PUT"),t.push({xtype:"hiddenfield",name:"type",value:o.iftype},{xtype:o.isCreate?"textfield":"displayfield",fieldLabel:gettext("Name"),name:"iface",value:o.iface,vtype:n,allowBlank:!1,maxLength:"BridgeName"===n?10:15,autoEl:{tag:"div","data-qtip":gettext("For example, vmbr0.100, vmbr0, vlan0.100, vlan0")},listeners:{change:function(e,t){var a,i;o.isCreate&&"VlanName"===n&&(a=o.down("field[name=vlan-id]"),i=o.down("field[name=vlan-raw-device]"),Proxmox.Utils.VlanInterface_match.test(t)?(a.setDisabled(!0),i.setDisabled(!0),a.setValue(t.match(Proxmox.Utils.VlanInterface_match)[2]),i.setValue(t.match(Proxmox.Utils.VlanInterface_match)[1])):(Proxmox.Utils.Vlan_match.test(t)?(a.setDisabled(!0),a.setValue(t.match(Proxmox.Utils.Vlan_match)[1])):a.setDisabled(!1),i.setDisabled(!1)))}}}),"OVSBond"===o.iftype?t.push({xtype:"bondModeSelector",fieldLabel:gettext("Mode"),name:"bond_mode",openvswitch:!0,value:o.isCreate?"active-backup":void 0,allowBlank:!1},{xtype:"textfield",fieldLabel:gettext("Slaves"),name:"ovs_bonds"}):t.push({xtype:"proxmoxtextfield",deleteEmpty:!o.isCreate,fieldLabel:"IPv4/CIDR",vtype:"IPCIDRAddress",name:"cidr"},{xtype:"proxmoxtextfield",deleteEmpty:!o.isCreate,fieldLabel:gettext("Gateway")+" (IPv4)",vtype:"IPAddress",name:"gateway"},{xtype:"proxmoxtextfield",deleteEmpty:!o.isCreate,fieldLabel:"IPv6/CIDR",vtype:"IP6CIDRAddress",name:"cidr6"},{xtype:"proxmoxtextfield",deleteEmpty:!o.isCreate,fieldLabel:gettext("Gateway")+" (IPv6)",vtype:"IP6Address",name:"gateway6"}),i.push({xtype:"proxmoxintegerfield",minValue:1280,maxValue:65520,deleteEmpty:!o.isCreate,emptyText:1500,fieldLabel:"MTU",name:"mtu"}),Ext.applyIf(o,{url:s,method:d,items:{xtype:"inputpanel",column1:t,column2:r,columnB:a,advancedColumn1:i,advancedColumn2:l}}),o.callParent(),o.isCreate?o.down("field[name=iface]").setValue(o.iface_default):o.load({success:function(e,t){e=e.result.data;e.type!==o.iftype?Ext.Msg.alert(gettext("Error"),"Got unexpected device type",function(){o.close()}):(o.setValues(e),o.isValid())}})}}),Ext.define("proxmox-networks",{extend:"Ext.data.Model",fields:["active","address","address6","autostart","bridge_ports","cidr","cidr6","comments","gateway","gateway6","iface","netmask","netmask6","slaves","type","vlan-id","vlan-raw-device"],idProperty:"iface"}),Ext.define("Proxmox.node.NetworkView",{extend:"Ext.panel.Panel",alias:["widget.proxmoxNodeNetworkView"],types:["bridge","bond","vlan","ovs"],showApplyBtn:!1,editOptions:{},initComponent:function(){let a=this;if(!a.nodename)throw"no node name specified";let t=`/nodes/${a.nodename}/network`,r=Ext.create("Ext.data.Store",{model:"proxmox-networks",proxy:{type:"proxmox",url:"/api2/json"+t},sorters:[{property:"iface",direction:"ASC"}]}),i=function(){let i=a.down("#changes"),o=a.down("#apply"),n=a.down("#revert");Proxmox.Utils.API2Request({url:t,failure:function(e,t){r.loadData({}),Proxmox.Utils.setErrorMask(a,e.htmlStatus),i.update(""),i.setHidden(!0)},success:function(e,t){e=Ext.decode(e.responseText);r.loadData(e.data);let a=e.changes;void 0===a||""===a?(a=gettext("No changes"),i.setHidden(!0),o.setDisabled(!0),n.setDisabled(!0)):(i.update("<pre>"+Ext.htmlEncode(a)+"</pre>"),i.setHidden(!1),o.setDisabled(!1),n.setDisabled(!1))}})};function e(){var e=a.down("gridpanel").getSelectionModel().getSelection()[0];e&&Ext.create("Proxmox.node.NetworkEdit",{autoShow:!0,nodename:a.nodename,iface:e.data.iface,iftype:e.data.type,...a.editOptions,listeners:{destroy:()=>i()}})}let o=new Ext.Button({text:gettext("Edit"),disabled:!0,handler:e}),n=Ext.create("Ext.selection.RowModel",{}),l=new Proxmox.button.StdRemoveButton({selModel:n,getUrl:({data:e})=>t+"/"+e.iface,callback:()=>i()});var s=Ext.create("Proxmox.button.Button",{text:gettext("Apply Configuration"),itemId:"apply",disabled:!0,confirmMsg:"Do you want to apply pending network changes?",hidden:!a.showApplyBtn,handler:function(){Proxmox.Utils.API2Request({url:t,method:"PUT",waitMsgTarget:a,success:function({result:e},t){Ext.create("Proxmox.window.TaskProgress",{autoShow:!0,taskDone:i,upid:e.data})},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})}});let d=function(t){for(let e=0;e<=9999;e++){var a=""+t+e.toString();if(!r.getById(a))return a}return Ext.Msg.alert("Error",`No free ID for ${t} found!`),""},u=[];function c(o){return function(e,t,a){var i=[];return a.data[o]&&i.push(a.data[o]),a.data[o+"6"]&&i.push(a.data[o+"6"]),i.join("<br>")||""}}var x=(e,t)=>{u.push({text:Proxmox.Utils.render_network_iface_type(e),handler:()=>Ext.create("Proxmox.node.NetworkEdit",{autoShow:!0,nodename:a.nodename,iftype:e,iface_default:d(t??e),...a.editOptions,onlineHelp:"sysadmin_network_configuration",listeners:{destroy:()=>i()}})})};-1!==a.types.indexOf("bridge")&&x("bridge","vmbr"),-1!==a.types.indexOf("bond")&&x("bond"),-1!==a.types.indexOf("vlan")&&x("vlan"),-1!==a.types.indexOf("ovs")&&(0<u.length&&u.push({xtype:"menuseparator"}),x("OVSBridge","vmbr"),x("OVSBond","bond"),u.push({text:Proxmox.Utils.render_network_iface_type("OVSIntPort"),handler:()=>Ext.create("Proxmox.node.NetworkEdit",{autoShow:!0,nodename:a.nodename,iftype:"OVSIntPort",listeners:{destroy:()=>i()}})}));Ext.apply(a,{layout:"border",tbar:[{text:gettext("Create"),menu:{plain:!0,items:u}},"-",{text:gettext("Revert"),itemId:"revert",handler:function(){Proxmox.Utils.API2Request({url:t,method:"DELETE",waitMsgTarget:a,callback:function(){i()},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})}},o,l,"-",s],items:[{xtype:"gridpanel",stateful:!0,stateId:"grid-node-network",store:r,selModel:n,region:"center",border:!1,columns:[{header:gettext("Name"),sortable:!0,dataIndex:"iface"},{header:gettext("Type"),sortable:!0,width:120,renderer:Proxmox.Utils.render_network_iface_type,dataIndex:"type"},{xtype:"booleancolumn",header:gettext("Active"),width:80,sortable:!0,dataIndex:"active",trueText:Proxmox.Utils.yesText,falseText:Proxmox.Utils.noText,undefinedText:Proxmox.Utils.noText},{xtype:"booleancolumn",header:gettext("Autostart"),width:80,sortable:!0,dataIndex:"autostart",trueText:Proxmox.Utils.yesText,falseText:Proxmox.Utils.noText,undefinedText:Proxmox.Utils.noText},{xtype:"booleancolumn",header:gettext("VLAN aware"),width:80,sortable:!0,dataIndex:"bridge_vlan_aware",trueText:Proxmox.Utils.yesText,falseText:Proxmox.Utils.noText,undefinedText:Proxmox.Utils.noText},{header:gettext("Ports/Slaves"),dataIndex:"type",renderer:(e,t,{data:a})=>"bridge"===e?a.bridge_ports:"bond"===e?a.slaves:"OVSBridge"===e?a.ovs_ports:"OVSBond"===e?a.ovs_bonds:""},{header:gettext("Bond Mode"),dataIndex:"bond_mode",renderer:Proxmox.Utils.render_bond_mode},{header:gettext("Hash Policy"),hidden:!0,dataIndex:"bond_xmit_hash_policy"},{header:gettext("IP address"),sortable:!0,width:120,hidden:!0,dataIndex:"address",renderer:c("address")},{header:gettext("Subnet mask"),width:120,sortable:!0,hidden:!0,dataIndex:"netmask",renderer:c("netmask")},{header:gettext("CIDR"),width:150,sortable:!0,dataIndex:"cidr",renderer:c("cidr")},{header:gettext("Gateway"),width:150,sortable:!0,dataIndex:"gateway",renderer:c("gateway")},{header:gettext("VLAN ID"),hidden:!0,sortable:!0,dataIndex:"vlan-id"},{header:gettext("VLAN raw device"),hidden:!0,sortable:!0,dataIndex:"vlan-raw-device"},{header:"MTU",hidden:!0,sortable:!0,dataIndex:"mtu"},{header:gettext("Comment"),dataIndex:"comments",flex:1,renderer:Ext.String.htmlEncode}],listeners:{selectionchange:function(){var e=n.getSelection()[0];o.setDisabled(!e),l.setDisabled(!e)},itemdblclick:e}},{border:!1,region:"south",autoScroll:!0,hidden:!0,itemId:"changes",tbar:[gettext("Pending changes")+" ("+gettext("Either reboot or use 'Apply Configuration' (needs ifupdown2) to activate")+")"],split:!0,bodyPadding:5,flex:.6,html:gettext("No changes")}]}),a.callParent(),i()}}),Ext.define("Proxmox.node.DNSEdit",{extend:"Proxmox.window.Edit",alias:["widget.proxmoxNodeDNSEdit"],deleteEmpty:!1,initComponent:function(){var e=this;if(!e.nodename)throw"no node name specified";e.items=[{xtype:"textfield",fieldLabel:gettext("Search domain"),name:"search",allowBlank:!1},{xtype:"proxmoxtextfield",fieldLabel:gettext("DNS server")+" 1",vtype:"IP64Address",skipEmptyText:!0,deleteEmpty:e.deleteEmpty,name:"dns1"},{xtype:"proxmoxtextfield",fieldLabel:gettext("DNS server")+" 2",vtype:"IP64Address",skipEmptyText:!0,deleteEmpty:e.deleteEmpty,name:"dns2"},{xtype:"proxmoxtextfield",fieldLabel:gettext("DNS server")+" 3",vtype:"IP64Address",skipEmptyText:!0,deleteEmpty:e.deleteEmpty,name:"dns3"}],Ext.applyIf(e,{subject:gettext("DNS"),url:"/api2/extjs/nodes/"+e.nodename+"/dns",fieldDefaults:{labelWidth:120}}),e.callParent(),e.load()}}),Ext.define("Proxmox.node.HostsView",{extend:"Ext.panel.Panel",xtype:"proxmoxNodeHostsView",reload:function(){this.store.load()},tbar:[{text:gettext("Save"),disabled:!0,itemId:"savebtn",handler:function(){let a=this.up("panel");Proxmox.Utils.API2Request({params:{digest:a.digest,data:a.down("#hostsfield").getValue()},method:"POST",url:"/nodes/"+a.nodename+"/hosts",waitMsgTarget:a,success:function(e,t){a.reload()},failure:function(e,t){Ext.Msg.alert("Error",e.htmlStatus)}})}},{text:gettext("Revert"),disabled:!0,itemId:"resetbtn",handler:function(){this.up("panel").down("#hostsfield").reset()}}],layout:"fit",items:[{xtype:"textarea",itemId:"hostsfield",fieldStyle:{"font-family":"monospace","white-space":"pre"},listeners:{dirtychange:function(e,t){var a=this.up("panel");a.down("#savebtn").setDisabled(!t),a.down("#resetbtn").setDisabled(!t)}}}],initComponent:function(){let i=this;if(!i.nodename)throw"no node name specified";i.store=Ext.create("Ext.data.Store",{proxy:{type:"proxmox",url:"/api2/json/nodes/"+i.nodename+"/hosts"}}),i.callParent(),Proxmox.Utils.monStoreErrors(i,i.store),i.mon(i.store,"load",function(e,t,a){!a||t.length<1||(i.digest=t[0].data.digest,a=t[0].data.data,i.down("#hostsfield").setValue(a),i.down("#hostsfield").resetOriginalValue())}),i.reload()}}),Ext.define("Proxmox.node.DNSView",{extend:"Proxmox.grid.ObjectGrid",alias:["widget.proxmoxNodeDNSView"],deleteEmpty:!1,initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";var t=()=>Ext.create("Proxmox.node.DNSEdit",{autoShow:!0,nodename:e.nodename,deleteEmpty:e.deleteEmpty});Ext.apply(e,{url:`/api2/json/nodes/${e.nodename}/dns`,cwidth1:130,interval:1e4,run_editor:t,rows:{search:{header:gettext("Search domain"),required:!0,renderer:Ext.htmlEncode},dns1:{header:gettext("DNS server")+" 1",required:!0,renderer:Ext.htmlEncode},dns2:{header:gettext("DNS server")+" 2",renderer:Ext.htmlEncode},dns3:{header:gettext("DNS server")+" 3",renderer:Ext.htmlEncode}},tbar:[{text:gettext("Edit"),handler:t}],listeners:{itemdblclick:t}}),e.callParent(),e.on("activate",e.rstore.startUpdate),e.on("deactivate",e.rstore.stopUpdate),e.on("destroy",e.rstore.stopUpdate)}}),Ext.define("Proxmox.node.Tasks",{extend:"Ext.grid.GridPanel",alias:"widget.proxmoxNodeTasks",stateful:!0,stateId:"pve-grid-node-tasks",loadMask:!0,sortableColumns:!1,extraFilter:[],preFilter:{},controller:{xclass:"Ext.app.ViewController",showTaskLog:function(){var e=this.getView().getSelection();e.length<1||(e=e[0],Ext.create("Proxmox.window.TaskViewer",{upid:e.data.upid,endtime:e.data.endtime}).show())},updateLayout:function(e,t,a,i){var o=this.getView().getView();Proxmox.Utils.setErrorMask(o,!1),this.getView().updateLayout(),a||Proxmox.Utils.setErrorMask(o,Proxmox.Utils.getResponseErrorMessage(i.getError()))},refresh:function(){var e=this.getView(),t=e.getSelection(),a=this.getViewModel().get("bufferedstore");t&&0<t.length&&(a.contains(t[0])||e.setSelection(void 0))},sinceChange:function(e,t){this.getViewModel().set("since",t)},untilChange:function(e,t,a){this.getViewModel().set("until",t)},reload:function(){this.getView().getStore().load()},showFilter:function(e,t){this.getViewModel().set("showFilter",t)},clearFilter:function(){this.lookup("filtertoolbar").query("field").forEach(e=>{e.setValue(void 0)})}},listeners:{itemdblclick:"showTaskLog"},viewModel:{data:{typefilter:"",statusfilter:"",showFilter:!1,extraFilter:{},since:null,until:null},formulas:{filterIcon:e=>"fa fa-filter"+(e("showFilter")?" info-blue":""),extraParams:function(e){var t={};if(e("typefilter")&&(t.typefilter=e("typefilter")),e("statusfilter")&&(t.statusfilter=e("statusfilter")),e("extraFilter")){var a,i,o=e("extraFilter");for([a,i]of Object.entries(o))void 0!==i&&null!==i&&""!==i&&(t[a]=i)}return e("since")&&(t.since=e("since").valueOf()/1e3),e("until")&&((o=new Date(e("until").getTime())).setDate(o.getDate()+1),t.until=o.valueOf()/1e3),this.getView().getStore().load(),t},filterCount:function(e){let t=0;e("typefilter")&&t++;var a=e("statusfilter");if((Ext.isArray(a)&&0<a.length||!Ext.isArray(a)&&a)&&t++,e("since")&&t++,e("until")&&t++,e("extraFilter")){var i,o,n=e("preFilter")||{},a=e("extraFilter");for([i,o]of Object.entries(a))void 0!==o&&null!==o&&""!==o&&void 0===n[i]&&t++}return t},clearFilterText:function(e){e=e("filterCount");let t="";return 1<e?t=` (${e} ${gettext("Fields")})`:0<e&&(t=` (1 ${gettext("Field")})`),gettext("Clear Filter")+t}},stores:{bufferedstore:{type:"buffered",pageSize:500,autoLoad:!0,remoteFilter:!0,model:"proxmox-tasks",proxy:{type:"proxmox",startParam:"start",limitParam:"limit",extraParams:"{extraParams}",url:"{url}"},listeners:{prefetch:"updateLayout",refresh:"refresh"}}}},bind:{store:"{bufferedstore}"},dockedItems:[{xtype:"toolbar",items:[{xtype:"proxmoxButton",text:gettext("View Task"),iconCls:"fa fa-window-restore",disabled:!0,handler:"showTaskLog"},{xtype:"button",text:gettext("Reload"),iconCls:"fa fa-refresh",handler:"reload"},"->",{xtype:"button",bind:{text:"{clearFilterText}",disabled:"{!filterCount}"},text:gettext("Clear Filter"),enabled:!1,handler:"clearFilter"},{xtype:"button",enableToggle:!0,bind:{iconCls:"{filterIcon}"},text:gettext("Filter"),stateful:!0,stateId:"task-showfilter",stateEvents:["toggle"],applyState:function(e){void 0!==e.pressed&&this.setPressed(e.pressed)},getState:function(){return{pressed:this.pressed}},listeners:{toggle:"showFilter"}}]},{xtype:"toolbar",dock:"top",reference:"filtertoolbar",layout:{type:"hbox",align:"top"},bind:{hidden:"{!showFilter}"},items:[{xtype:"container",padding:10,layout:{type:"vbox",align:"stretch"},defaults:{labelWidth:80},items:[{xtype:"datefield",fieldLabel:gettext("Since"),format:"Y-m-d",bind:{maxValue:"{until}"},listeners:{change:"sinceChange"}},{xtype:"datefield",fieldLabel:gettext("Until"),format:"Y-m-d",bind:{minValue:"{since}"},listeners:{change:"untilChange"}}]},{xtype:"container",padding:10,layout:{type:"vbox",align:"stretch"},defaults:{labelWidth:80},items:[{xtype:"pmxTaskTypeSelector",fieldLabel:gettext("Task Type"),emptyText:gettext("All"),bind:{value:"{typefilter}"}},{xtype:"combobox",fieldLabel:gettext("Task Result"),emptyText:gettext("All"),multiSelect:!0,store:[["ok",gettext("OK")],["unknown",Proxmox.Utils.unknownText],["warning",gettext("Warnings")],["error",gettext("Errors")]],bind:{value:"{statusfilter}"}}]}]}],viewConfig:{trackOver:!1,stripeRows:!1,emptyText:gettext("No Tasks found"),getRowClass:function(e,t){e=e.get("status");if(e){e=Proxmox.Utils.parse_task_status(e);if("error"===e)return"proxmox-invalid-row";if("warning"===e)return"proxmox-warning-row"}return""}},columns:[{header:gettext("Start Time"),dataIndex:"starttime",width:130,renderer:function(e){return Ext.Date.format(e,"M d H:i:s")}},{header:gettext("End Time"),dataIndex:"endtime",width:130,renderer:function(e,t,a){return e?Ext.Date.format(e,"M d H:i:s"):(t.tdCls="x-grid-row-loading","")}},{header:gettext("Duration"),hidden:!0,width:80,renderer:function(e,t,a){var i=a.data.starttime;if(i){let e=(a.data.endtime||Date.now())-i;return 0<e&&(e/=1e3),Proxmox.Utils.format_duration_human(e)}return Proxmox.Utils.unknownText}},{header:gettext("User name"),dataIndex:"user",width:150},{header:gettext("Description"),dataIndex:"upid",flex:1,renderer:Proxmox.Utils.render_upid},{header:gettext("Status"),dataIndex:"status",width:200,renderer:function(e,t,a){return void 0!==e||a.data.endtime?Proxmox.Utils.format_task_status(e):(t.tdCls="x-grid-row-loading","")}},{xtype:"actioncolumn",width:30,align:"center",tooltip:gettext("Actions"),items:[{iconCls:"fa fa-chevron-right",tooltip:gettext("View Task"),handler:function(e,t,a,i,o,n){Ext.create("Proxmox.window.TaskViewer",{autoShow:!0,upid:n.data.upid,endtime:n.data.endtime})}}]}],initComponent:function(){const o=this;var e,t,a=o.nodename||"localhost",a=o.url||`/api2/json/nodes/${a}/tasks`;o.getViewModel().set("url",a);function i(e,t){var a=o.getViewModel(),i=Ext.clone(a.get("extraFilter"));i[e]=t,a.set("extraFilter",i)}for([e,t]of Object.entries(o.preFilter))i(e,t);o.getViewModel().set("preFilter",o.preFilter),o.callParent();function n(e){o.lookup("filtertoolbar").add({xtype:"container",padding:10,layout:{type:"vbox",align:"stretch"},defaults:{labelWidth:80},items:e})}o.extraFilter=[{xtype:"textfield",fieldLabel:gettext("User name"),changeOptions:{buffer:500},name:"userfilter"},...o.extraFilter];let r=[];for(const l of o.extraFilter){let a=Ext.clone(l);a.listeners=a.listeners||{},a.listeners.change=Ext.apply(a.changeOptions||{},{fn:function(e,t){i(a.name,t)}}),r.push(a),2===r.length&&(n(r),r=[])}n(r)}}),Ext.define("proxmox-services",{extend:"Ext.data.Model",fields:["service","name","desc","state","unit-state","active-state"],idProperty:"service"}),Ext.define("Proxmox.node.ServiceView",{extend:"Ext.grid.GridPanel",alias:["widget.proxmoxNodeServiceView"],startOnlyServices:{},restartCommand:"restart",initComponent:function(){let i=this;if(!i.nodename)throw"no node name specified";let a=Ext.create("Proxmox.data.UpdateStore",{interval:1e3,model:"proxmox-services",proxy:{type:"proxmox",url:`/api2/json/nodes/${i.nodename}/services`}}),o=e=>"not-found"!==e.get("unit-state"),n=Ext.create("Proxmox.data.DiffStore",{rstore:a,sortAfterUpdate:!0,sorters:[{property:"name",direction:"ASC"}],filters:[o]});function e(){var e=i.getSelectionModel().getSelection()[0]["data"]["service"];Ext.create("Ext.window.Window",{title:gettext("Syslog")+": "+e,modal:!0,width:800,height:400,layout:"fit",items:{xtype:"proxmoxLogView",url:`/api2/extjs/nodes/${i.nodename}/syslog?service=`+e,log_select_timespan:1},autoShow:!0})}var t=Ext.create("Ext.form.field.Checkbox",{boxLabel:gettext("Show only installed services"),value:!0,boxLabelAlign:"before",listeners:{change:function(e,t){t?n.addFilter([o]):n.clearFilter()}}});function r(e){var t=i.getSelectionModel().getSelection()[0]["data"]["service"];Proxmox.Utils.API2Request({url:`/nodes/${i.nodename}/services/${t}/`+e,method:"POST",failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus),i.loading=!0},success:function(e,t){a.startUpdate(),Ext.create("Proxmox.window.TaskProgress",{upid:e.result.data,autoShow:!0})}})}let l=new Ext.Button({text:gettext("Start"),disabled:!0,handler:()=>r("start")}),s=new Ext.Button({text:gettext("Stop"),disabled:!0,handler:()=>r("stop")}),d=new Ext.Button({text:gettext("Restart"),disabled:!0,handler:()=>r(i.restartCommand||"restart")}),u=new Ext.Button({text:gettext("Syslog"),disabled:!0,handler:e});function c(){var e,t,a=i.getSelectionModel().getSelection()[0];a?(e=a.data.service,t=a.data.state,a=a.data["unit-state"],u.enable(),"running"===t?i.startOnlyServices[e]?(s.disable(),d.enable()):(s.enable(),d.enable(),l.disable()):(void 0===a||"masked"!==a&&"unknown"!==a&&"not-found"!==a?(l.enable(),s.disable(),d):(l.disable(),d.disable(),s)).disable()):(l.disable(),s.disable(),d.disable(),u.disable())}i.mon(n,"refresh",c),Proxmox.Utils.monStoreErrors(i,a),Ext.apply(i,{viewConfig:{trackOver:!1,stripeRows:!1,getRowClass:function(e,t){var a=e.get("unit-state");return a?"masked"===a||"not-found"===a?"proxmox-disabled-row":"unknown"===a?"syslog"===e.get("name")?"proxmox-disabled-row":"proxmox-warning-row":"":""}},store:n,stateful:!1,tbar:[l,s,d,"-",u,"->",t],columns:[{header:gettext("Name"),flex:1,sortable:!0,dataIndex:"name"},{header:gettext("Status"),width:100,sortable:!0,dataIndex:"state",renderer:(e,t,a)=>{a=a.get("unit-state");return"masked"===a?gettext("disabled"):"not-found"===a?gettext("not installed"):e}},{header:gettext("Active"),width:100,sortable:!0,hidden:!0,dataIndex:"active-state"},{header:gettext("Unit"),width:120,sortable:!0,hidden:!Ext.Array.contains(["PVEAuthCookie","PBSAuthCookie"],Proxmox?.Setup?.auth_cookie_name),dataIndex:"unit-state"},{header:gettext("Description"),renderer:Ext.String.htmlEncode,dataIndex:"desc",flex:2}],listeners:{selectionchange:c,itemdblclick:e,activate:a.startUpdate,destroy:a.stopUpdate}}),i.callParent()}}),Ext.define("Proxmox.node.TimeEdit",{extend:"Proxmox.window.Edit",alias:["widget.proxmoxNodeTimeEdit"],subject:gettext("Time zone"),width:400,autoLoad:!0,fieldDefaults:{labelWidth:70},items:{xtype:"combo",fieldLabel:gettext("Time zone"),name:"timezone",queryMode:"local",store:Ext.create("Proxmox.data.TimezoneStore"),displayField:"zone",editable:!0,anyMatch:!0,forceSelection:!0,allowBlank:!1},initComponent:function(){if(!this.nodename)throw"no node name specified";this.url="/api2/extjs/nodes/"+this.nodename+"/time",this.callParent()}}),Ext.define("Proxmox.node.TimeView",{extend:"Proxmox.grid.ObjectGrid",alias:["widget.proxmoxNodeTimeView"],initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";let t=6e4*(new Date).getTimezoneOffset();var a=()=>Ext.create("Proxmox.node.TimeEdit",{autoShow:!0,nodename:e.nodename});Ext.apply(e,{url:`/api2/json/nodes/${e.nodename}/time`,cwidth1:150,interval:1e3,run_editor:a,rows:{timezone:{header:gettext("Time zone"),required:!0},localtime:{header:gettext("Server time"),required:!0,renderer:function(e){e=new Date(1e3*e+t);return Ext.Date.format(e,"Y-m-d H:i:s")}}},tbar:[{text:gettext("Edit"),handler:a}],listeners:{itemdblclick:a}}),e.callParent(),e.on("activate",e.rstore.startUpdate),e.on("deactivate",e.rstore.stopUpdate),e.on("destroy",e.rstore.stopUpdate)}}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).marked={})}(this,function(o){"use strict";function n(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!=typeof e||null===e)return e;var a=e[Symbol.toPrimitive];if(void 0===a)return("string"===t?String:Number)(e);a=a.call(e,t||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"==typeof e?e:String(e)}(i.key),i)}}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=new Array(t);a<t;a++)i[a]=e[a];return i}function A(e,t){var a,i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(i)return(i=i.call(e)).next.bind(i);if(Array.isArray(e)||(i=function(e,t){var a;if(e)return"string"==typeof e?r(e,t):"Map"===(a="Object"===(a=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:a)||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?r(e,t):void 0}(e))||t&&e&&"number"==typeof e.length)return i&&(e=i),a=0,function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function e(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}o.defaults=e();function a(e){return t[e]}var i=/[&<>"']/,l=new RegExp(i.source,"g"),s=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,d=new RegExp(s.source,"g"),t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function u(e,t){if(t){if(i.test(e))return e.replace(l,a)}else if(s.test(e))return e.replace(d,a);return e}var c=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function v(e){return e.replace(c,function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""})}var x=/(^|[^\[])\^/g;function m(a,e){a="string"==typeof a?a:a.source,e=e||"";var i={replace:function(e,t){return t=(t=t.source||t).replace(x,"$1"),a=a.replace(e,t),i},getRegex:function(){return new RegExp(a,e)}};return i}var p=/[^\w:]/g,I=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function f(e,t,a){if(e){try{i=decodeURIComponent(v(a)).replace(p,"").toLowerCase()}catch(e){return null}if(0===i.indexOf("javascript:")||0===i.indexOf("vbscript:")||0===i.indexOf("data:"))return null}var i;t&&!I.test(a)&&(e=a,g[" "+(i=t)]||(R.test(i)?g[" "+i]=i+"/":g[" "+i]=w(i,"/",!0)),t=-1===(i=g[" "+i]).indexOf(":"),a="//"===e.substring(0,2)?t?e:i.replace(L,"$1")+e:"/"===e.charAt(0)?t?e:i.replace(N,"$1")+e:i+e);try{a=encodeURI(a).replace(/%25/g,"%")}catch(e){return null}return a}var g={},R=/^[^:]+:\/*[^/]*$/,L=/^([^:]+:)[\s\S]*$/,N=/^([^:]+:\/*[^/]*)[\s\S]*$/;var h={exec:function(){}};function b(e){for(var t,a,i=1;i<arguments.length;i++)for(a in t=arguments[i])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}function y(e,t){var a=e.replace(/\|/g,function(e,t,a){for(var i=!1,o=t;0<=--o&&"\\"===a[o];)i=!i;return i?"|":" |"}).split(/ \|/),i=0;if(a[0].trim()||a.shift(),0<a.length&&!a[a.length-1].trim()&&a.pop(),a.length>t)a.splice(t);else for(;a.length<t;)a.push("");for(;i<a.length;i++)a[i]=a[i].trim().replace(/\\\|/g,"|");return a}function w(e,t,a){var i=e.length;if(0===i)return"";for(var o=0;o<i;){var n=e.charAt(i-o-1);if((n!==t||a)&&(n===t||!a))break;o++}return e.slice(0,i-o)}function E(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function D(e,t){if(t<1)return"";for(var a="";1<t;)1&t&&(a+=e),t>>=1,e+=e;return a+e}function P(e,t,a,i){var o=t.href,t=t.title?u(t.title):null,n=e[1].replace(/\\([\[\]])/g,"$1");return"!"!==e[0].charAt(0)?(i.state.inLink=!0,e={type:"link",raw:a,href:o,title:t,text:n,tokens:i.inlineTokens(n)},i.state.inLink=!1,e):{type:"image",raw:a,href:o,title:t,text:u(n)}}var C=function(){function e(e){this.options=e||o.defaults}var t=e.prototype;return t.space=function(e){e=this.rules.block.newline.exec(e);if(e&&0<e[0].length)return{type:"space",raw:e[0]}},t.code=function(e){var t,e=this.rules.block.code.exec(e);if(e)return t=e[0].replace(/^ {1,4}/gm,""),{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:w(t,"\n")}},t.fences=function(e){var t,a,i,o,e=this.rules.block.fences.exec(e);if(e)return t=e[0],a=t,i=e[3]||"",a=null===(a=t.match(/^(\s+)(?:```)/))?i:(o=a[1],i.split("\n").map(function(e){var t=e.match(/^\s+/);return null!==t&&t[0].length>=o.length?e.slice(o.length):e}).join("\n")),{type:"code",raw:t,lang:e[2]&&e[2].trim().replace(this.rules.inline._escapes,"$1"),text:a}},t.heading=function(e){var t,a,e=this.rules.block.heading.exec(e);if(e)return t=e[2].trim(),/#$/.test(t)&&(a=w(t,"#"),!this.options.pedantic&&a&&!/ $/.test(a)||(t=a.trim())),{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}},t.hr=function(e){e=this.rules.block.hr.exec(e);if(e)return{type:"hr",raw:e[0]}},t.blockquote=function(e){var t,e=this.rules.block.blockquote.exec(e);if(e)return t=e[0].replace(/^ *>[ \t]?/gm,""),{type:"blockquote",raw:e[0],tokens:this.lexer.blockTokens(t,[]),text:t}},t.list=function(e){var t=this.rules.block.list.exec(e);if(t){var a,i,o,n,r,l,s,d,u,c,x,m=1<(f=t[1].trim()).length,p={type:"list",raw:"",ordered:m,start:m?+f.slice(0,-1):"",loose:!1,items:[]},f=m?"\\d{1,9}\\"+f.slice(-1):"\\"+f;this.options.pedantic&&(f=m?f:"[*+-]");for(var g=new RegExp("^( {0,3}"+f+")((?:[\t ][^\\n]*)?(?:\\n|$))");e&&(x=!1,t=g.exec(e))&&!this.rules.block.hr.test(e);){if(a=t[0],e=e.substring(a.length),s=t[2].split("\n",1)[0],d=e.split("\n",1)[0],this.options.pedantic?(n=2,c=s.trimLeft()):(n=t[2].search(/[^ ]/),c=s.slice(n=4<n?1:n),n+=t[1].length),r=!1,!s&&/^ *$/.test(d)&&(a+=d+"\n",e=e.substring(d.length+1),x=!0),!x)for(var h=new RegExp("^ {0,"+Math.min(3,n-1)+"}(?:[*+-]|\\d{1,9}[.)])((?: [^\\n]*)?(?:\\n|$))"),b=new RegExp("^ {0,"+Math.min(3,n-1)+"}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)"),y=new RegExp("^ {0,"+Math.min(3,n-1)+"}(?:```|~~~)"),w=new RegExp("^ {0,"+Math.min(3,n-1)+"}#");e&&(s=u=e.split("\n",1)[0],this.options.pedantic&&(s=s.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!y.test(s))&&!w.test(s)&&!h.test(s)&&!b.test(e);){if(s.search(/[^ ]/)>=n||!s.trim())c+="\n"+s.slice(n);else{if(r)break;c+="\n"+s}r||s.trim()||(r=!0),a+=u+"\n",e=e.substring(u.length+1)}p.loose||(l?p.loose=!0:/\n *\n *$/.test(a)&&(l=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(c))&&(o="[ ] "!==i[0],c=c.replace(/^\[[ xX]\] +/,"")),p.items.push({type:"list_item",raw:a,task:!!i,checked:o,loose:!1,text:c}),p.raw+=a}p.items[p.items.length-1].raw=a.trimRight(),p.items[p.items.length-1].text=c.trimRight(),p.raw=p.raw.trimRight();for(var E=p.items.length,v=0;v<E;v++){this.lexer.state.top=!1,p.items[v].tokens=this.lexer.blockTokens(p.items[v].text,[]);var D=p.items[v].tokens.filter(function(e){return"space"===e.type}),P=D.every(function(e){for(var t,a=0,i=A(e.raw.split(""));!(t=i()).done;)if("\n"===t.value&&(a+=1),1<a)return!0;return!1});!p.loose&&D.length&&P&&(p.loose=!0,p.items[v].loose=!0)}return p}},t.html=function(e){var t,e=this.rules.block.html.exec(e);if(e)return t={type:"html",raw:e[0],pre:!this.options.sanitizer&&("pre"===e[1]||"script"===e[1]||"style"===e[1]),text:e[0]},this.options.sanitize&&(e=this.options.sanitizer?this.options.sanitizer(e[0]):u(e[0]),t.type="paragraph",t.text=e,t.tokens=this.lexer.inline(e)),t},t.def=function(e){var t,a,i,e=this.rules.block.def.exec(e);if(e)return t=e[1].toLowerCase().replace(/\s+/g," "),a=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",i=e[3]&&e[3].substring(1,e[3].length-1).replace(this.rules.inline._escapes,"$1"),{type:"def",tag:t,raw:e[0],href:a,title:i}},t.table=function(e){e=this.rules.block.table.exec(e);if(e){var t={type:"table",header:y(e[1]).map(function(e){return{text:e}}),align:e[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(t.header.length===t.align.length){t.raw=e[0];for(var a,i,o,n=t.align.length,r=0;r<n;r++)/^ *-+: *$/.test(t.align[r])?t.align[r]="right":/^ *:-+: *$/.test(t.align[r])?t.align[r]="center":/^ *:-+ *$/.test(t.align[r])?t.align[r]="left":t.align[r]=null;for(n=t.rows.length,r=0;r<n;r++)t.rows[r]=y(t.rows[r],t.header.length).map(function(e){return{text:e}});for(n=t.header.length,a=0;a<n;a++)t.header[a].tokens=this.lexer.inline(t.header[a].text);for(n=t.rows.length,a=0;a<n;a++)for(o=t.rows[a],i=0;i<o.length;i++)o[i].tokens=this.lexer.inline(o[i].text);return t}}},t.lheading=function(e){e=this.rules.block.lheading.exec(e);if(e)return{type:"heading",raw:e[0],depth:"="===e[2].charAt(0)?1:2,text:e[1],tokens:this.lexer.inline(e[1])}},t.paragraph=function(e){var t,e=this.rules.block.paragraph.exec(e);if(e)return t="\n"===e[1].charAt(e[1].length-1)?e[1].slice(0,-1):e[1],{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}},t.text=function(e){e=this.rules.block.text.exec(e);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}},t.escape=function(e){e=this.rules.inline.escape.exec(e);if(e)return{type:"escape",raw:e[0],text:u(e[1])}},t.tag=function(e){e=this.rules.inline.tag.exec(e);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(e[0]):u(e[0]):e[0]}},t.link=function(e){e=this.rules.inline.link.exec(e);if(e){var t=e[2].trim();if(!this.options.pedantic&&/^</.test(t)){if(!/>$/.test(t))return;var a=w(t.slice(0,-1),"\\");if((t.length-a.length)%2==0)return}else{a=function(e,t){if(-1!==e.indexOf(t[1]))for(var a=e.length,i=0,o=0;o<a;o++)if("\\"===e[o])o++;else if(e[o]===t[0])i++;else if(e[o]===t[1]&&--i<0)return o;return-1}(e[2],"()");-1<a&&(o=(0===e[0].indexOf("!")?5:4)+e[1].length+a,e[2]=e[2].substring(0,a),e[0]=e[0].substring(0,o).trim(),e[3]="")}var i,a=e[2],o="";return this.options.pedantic?(i=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(a))&&(a=i[1],o=i[3]):o=e[3]?e[3].slice(1,-1):"",a=a.trim(),P(e,{href:(a=/^</.test(a)?this.options.pedantic&&!/>$/.test(t)?a.slice(1):a.slice(1,-1):a)&&a.replace(this.rules.inline._escapes,"$1"),title:o&&o.replace(this.rules.inline._escapes,"$1")},e[0],this.lexer)}},t.reflink=function(e,t){var a;if(a=(a=this.rules.inline.reflink.exec(e))||this.rules.inline.nolink.exec(e))return(e=t[(e=(a[2]||a[1]).replace(/\s+/g," ")).toLowerCase()])?P(a,e,a[0],this.lexer):{type:"text",raw:t=a[0].charAt(0),text:t}},t.emStrong=function(e,t,a){void 0===a&&(a="");var i=this.rules.inline.emStrong.lDelim.exec(e);if(i&&(!i[3]||!a.match(/(?:[0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xBC-\xBE\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u0660-\u0669\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0966-\u096F\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09F9\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AE6-\u0AEF\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B6F\u0B71-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0BE6-\u0BF2\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C78-\u0C7E\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D58-\u0D61\u0D66-\u0D78\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DE6-\u0DEF\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F20-\u0F33\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F-\u1049\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u1090-\u1099\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A20-\u1A54\u1A80-\u1A89\u1A90-\u1A99\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B50-\u1B59\u1B83-\u1BA0\u1BAE-\u1BE5\u1C00-\u1C23\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA830-\uA835\uA840-\uA873\uA882-\uA8B3\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA900-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF-\uA9D9\uA9E0-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA50-\uAA59\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDE80-\uDE9C\uDEA0-\uDED0\uDEE1-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE40-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE4\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD23\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF27\uDF30-\uDF45\uDF51-\uDF54\uDF70-\uDF81\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC52-\uDC6F\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD03-\uDD26\uDD36-\uDD3F\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDD0-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDEF0-\uDEF9\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC50-\uDC59\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE50-\uDE59\uDE80-\uDEAA\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDF50-\uDF59\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD834[\uDEC0-\uDED3\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB\uDEF0-\uDEF9]|\uD839[\uDCD0-\uDCEB\uDCF0-\uDCF9\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCCF\uDD00-\uDD43\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])/))){var o=i[1]||i[2]||"";if(!o||""===a||this.rules.inline.punctuation.exec(a)){var n=i[0].length-1,r=n,l=0,s="*"===i[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(s.lastIndex=0,t=t.slice(-1*e.length+n);null!=(i=s.exec(t));){var d,u=i[1]||i[2]||i[3]||i[4]||i[5]||i[6];if(u)if(d=u.length,i[3]||i[4])r+=d;else if((i[5]||i[6])&&n%3&&!((n+d)%3))l+=d;else if(!(0<(r-=d)))return d=Math.min(d,d+r+l),u=e.slice(0,n+i.index+(i[0].length-u.length)+d),Math.min(n,d)%2?(d=u.slice(1,-1),{type:"em",raw:u,text:d,tokens:this.lexer.inlineTokens(d)}):(d=u.slice(2,-2),{type:"strong",raw:u,text:d,tokens:this.lexer.inlineTokens(d)})}}}},t.codespan=function(e){var t,a,i,e=this.rules.inline.code.exec(e);if(e)return i=e[2].replace(/\n/g," "),t=/[^ ]/.test(i),a=/^ /.test(i)&&/ $/.test(i),i=u(i=t&&a?i.substring(1,i.length-1):i,!0),{type:"codespan",raw:e[0],text:i}},t.br=function(e){e=this.rules.inline.br.exec(e);if(e)return{type:"br",raw:e[0]}},t.del=function(e){e=this.rules.inline.del.exec(e);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}},t.autolink=function(e,t){var a,e=this.rules.inline.autolink.exec(e);if(e)return t="@"===e[2]?"mailto:"+(a=u(this.options.mangle?t(e[1]):e[1])):a=u(e[1]),{type:"link",raw:e[0],text:a,href:t,tokens:[{type:"text",raw:a,text:a}]}},t.url=function(e,t){var a,i,o,n;if(a=this.rules.inline.url.exec(e)){if("@"===a[2])o="mailto:"+(i=u(this.options.mangle?t(a[0]):a[0]));else{for(;n=a[0],a[0]=this.rules.inline._backpedal.exec(a[0])[0],n!==a[0];);i=u(a[0]),o="www."===a[1]?"http://"+i:i}return{type:"link",raw:a[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}},t.inlineText=function(e,t){e=this.rules.inline.text.exec(e);if(e)return t=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(e[0]):u(e[0]):e[0]:u(this.options.smartypants?t(e[0]):e[0]),{type:"text",raw:e[0],text:t}},e}(),k={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:h,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/},S=(k.def=m(k.def).replace("label",k._label).replace("title",k._title).getRegex(),k.bullet=/(?:[*+-]|\d{1,9}[.)])/,k.listItemStart=m(/^( *)(bull) */).replace("bull",k.bullet).getRegex(),k.list=m(k.list).replace(/bull/g,k.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+k.def.source+")").getRegex(),k._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",k._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,k.html=m(k.html,"i").replace("comment",k._comment).replace("tag",k._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),k.paragraph=m(k._paragraph).replace("hr",k.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k._tag).getRegex(),k.blockquote=m(k.blockquote).replace("paragraph",k.paragraph).getRegex(),k.normal=b({},k),k.gfm=b({},k.normal,{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),k.gfm.table=m(k.gfm.table).replace("hr",k.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k._tag).getRegex(),k.gfm.paragraph=m(k._paragraph).replace("hr",k.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",k.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",k._tag).getRegex(),k.pedantic=b({},k.normal,{html:m("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",k._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:h,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:m(k.normal._paragraph).replace("hr",k.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",k.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()}),{escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:h,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:h,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/});function O(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function F(e){for(var t,a="",i=e.length,o=0;o<i;o++)t=e.charCodeAt(o),a+="&#"+(t=.5<Math.random()?"x"+t.toString(16):t)+";";return a}S._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",S.punctuation=m(S.punctuation).replace(/punctuation/g,S._punctuation).getRegex(),S.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,S.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g,S._comment=m(k._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),S.emStrong.lDelim=m(S.emStrong.lDelim).replace(/punct/g,S._punctuation).getRegex(),S.emStrong.rDelimAst=m(S.emStrong.rDelimAst,"g").replace(/punct/g,S._punctuation).getRegex(),S.emStrong.rDelimUnd=m(S.emStrong.rDelimUnd,"g").replace(/punct/g,S._punctuation).getRegex(),S._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,S._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,S._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,S.autolink=m(S.autolink).replace("scheme",S._scheme).replace("email",S._email).getRegex(),S._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,S.tag=m(S.tag).replace("comment",S._comment).replace("attribute",S._attribute).getRegex(),S._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,S._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,S._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,S.link=m(S.link).replace("label",S._label).replace("href",S._href).replace("title",S._title).getRegex(),S.reflink=m(S.reflink).replace("label",S._label).replace("ref",k._label).getRegex(),S.nolink=m(S.nolink).replace("ref",k._label).getRegex(),S.reflinkSearch=m(S.reflinkSearch,"g").replace("reflink",S.reflink).replace("nolink",S.nolink).getRegex(),S.normal=b({},S),S.pedantic=b({},S.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:m(/^!?\[(label)\]\((.*?)\)/).replace("label",S._label).getRegex(),reflink:m(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",S._label).getRegex()}),S.gfm=b({},S.normal,{escape:m(S.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),S.gfm.url=m(S.gfm.url,"i").replace("email",S.gfm._extended_email).getRegex(),S.breaks=b({},S.gfm,{br:m(S.br).replace("{2,}","*").getRegex(),text:m(S.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});var T=function(){function a(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||o.defaults,this.options.tokenizer=this.options.tokenizer||new C,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,(this.tokenizer.lexer=this).inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};e={block:k.normal,inline:S.normal};this.options.pedantic?(e.block=k.pedantic,e.inline=S.pedantic):this.options.gfm&&(e.block=k.gfm,this.options.breaks?e.inline=S.breaks:e.inline=S.gfm),this.tokenizer.rules=e}a.lex=function(e,t){return new a(t).lex(e)},a.lexInline=function(e,t){return new a(t).inlineTokens(e)};var e,t,i=a.prototype;return i.lex=function(e){var t;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens},i.blockTokens=function(o,t){var a,e,n,i,r=this;for(void 0===t&&(t=[]),o=this.options.pedantic?o.replace(/\t/g,"    ").replace(/^ +$/gm,""):o.replace(/^( *)(\t+)/gm,function(e,t,a){return t+"    ".repeat(a.length)});o;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(function(e){return!!(a=e.call({lexer:r},o,t))&&(o=o.substring(a.raw.length),t.push(a),!0)})))if(a=this.tokenizer.space(o))o=o.substring(a.raw.length),1===a.raw.length&&0<t.length?t[t.length-1].raw+="\n":t.push(a);else if(a=this.tokenizer.code(o))o=o.substring(a.raw.length),!(e=t[t.length-1])||"paragraph"!==e.type&&"text"!==e.type?t.push(a):(e.raw+="\n"+a.raw,e.text+="\n"+a.text,this.inlineQueue[this.inlineQueue.length-1].src=e.text);else if(a=this.tokenizer.fences(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.heading(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.hr(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.blockquote(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.list(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.html(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.def(o))o=o.substring(a.raw.length),!(e=t[t.length-1])||"paragraph"!==e.type&&"text"!==e.type?this.tokens.links[a.tag]||(this.tokens.links[a.tag]={href:a.href,title:a.title}):(e.raw+="\n"+a.raw,e.text+="\n"+a.raw,this.inlineQueue[this.inlineQueue.length-1].src=e.text);else if(a=this.tokenizer.table(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.lheading(o))o=o.substring(a.raw.length),t.push(a);else if(n=o,this.options.extensions&&this.options.extensions.startBlock&&!function(){var t=1/0,a=o.slice(1),i=void 0;r.options.extensions.startBlock.forEach(function(e){"number"==typeof(i=e.call({lexer:this},a))&&0<=i&&(t=Math.min(t,i))}),t<1/0&&0<=t&&(n=o.substring(0,t+1))}(),this.state.top&&(a=this.tokenizer.paragraph(n)))e=t[t.length-1],i&&"paragraph"===e.type?(e.raw+="\n"+a.raw,e.text+="\n"+a.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=e.text):t.push(a),i=n.length!==o.length,o=o.substring(a.raw.length);else if(a=this.tokenizer.text(o))o=o.substring(a.raw.length),(e=t[t.length-1])&&"text"===e.type?(e.raw+="\n"+a.raw,e.text+="\n"+a.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=e.text):t.push(a);else if(o){var l="Infinite loop on byte: "+o.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}return this.state.top=!0,t},i.inline=function(e,t){return this.inlineQueue.push({src:e,tokens:t=void 0===t?[]:t}),t},i.inlineTokens=function(o,t){var a,e,n,i,r,l,s=this,d=(void 0===t&&(t=[]),o);if(this.tokens.links){var u=Object.keys(this.tokens.links);if(0<u.length)for(;null!=(i=this.tokenizer.rules.inline.reflinkSearch.exec(d));)u.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(d=d.slice(0,i.index)+"["+D("a",i[0].length-2)+"]"+d.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(i=this.tokenizer.rules.inline.blockSkip.exec(d));)d=d.slice(0,i.index)+"["+D("a",i[0].length-2)+"]"+d.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(i=this.tokenizer.rules.inline.escapedEmSt.exec(d));)d=d.slice(0,i.index+i[0].length-2)+"++"+d.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;o;)if(r||(l=""),r=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(function(e){return!!(a=e.call({lexer:s},o,t))&&(o=o.substring(a.raw.length),t.push(a),!0)})))if(a=this.tokenizer.escape(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.tag(o))o=o.substring(a.raw.length),(e=t[t.length-1])&&"text"===a.type&&"text"===e.type?(e.raw+=a.raw,e.text+=a.text):t.push(a);else if(a=this.tokenizer.link(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.reflink(o,this.tokens.links))o=o.substring(a.raw.length),(e=t[t.length-1])&&"text"===a.type&&"text"===e.type?(e.raw+=a.raw,e.text+=a.text):t.push(a);else if(a=this.tokenizer.emStrong(o,d,l))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.codespan(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.br(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.del(o))o=o.substring(a.raw.length),t.push(a);else if(a=this.tokenizer.autolink(o,F))o=o.substring(a.raw.length),t.push(a);else if(!this.state.inLink&&(a=this.tokenizer.url(o,F)))o=o.substring(a.raw.length),t.push(a);else if(n=o,this.options.extensions&&this.options.extensions.startInline&&!function(){var t=1/0,a=o.slice(1),i=void 0;s.options.extensions.startInline.forEach(function(e){"number"==typeof(i=e.call({lexer:this},a))&&0<=i&&(t=Math.min(t,i))}),t<1/0&&0<=t&&(n=o.substring(0,t+1))}(),a=this.tokenizer.inlineText(n,O))o=o.substring(a.raw.length),"_"!==a.raw.slice(-1)&&(l=a.raw.slice(-1)),r=!0,(e=t[t.length-1])&&"text"===e.type?(e.raw+=a.raw,e.text+=a.text):t.push(a);else if(o){var c="Infinite loop on byte: "+o.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}return t},i=a,t=[{key:"rules",get:function(){return{block:k,inline:S}}}],(e=null)&&n(i.prototype,e),t&&n(i,t),Object.defineProperty(i,"prototype",{writable:!1}),a}(),V=function(){function e(e){this.options=e||o.defaults}var t=e.prototype;return t.code=function(e,t,a){var i,t=(t||"").match(/\S*/)[0];return this.options.highlight&&null!=(i=this.options.highlight(e,t))&&i!==e&&(a=!0,e=i),e=e.replace(/\n$/,"")+"\n",t?'<pre><code class="'+this.options.langPrefix+u(t)+'">'+(a?e:u(e,!0))+"</code></pre>\n":"<pre><code>"+(a?e:u(e,!0))+"</code></pre>\n"},t.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},t.html=function(e){return e},t.heading=function(e,t,a,i){return this.options.headerIds?"<h"+t+' id="'+(this.options.headerPrefix+i.slug(a))+'">'+e+"</h"+t+">\n":"<h"+t+">"+e+"</h"+t+">\n"},t.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},t.list=function(e,t,a){var i=t?"ol":"ul";return"<"+i+(t&&1!==a?' start="'+a+'"':"")+">\n"+e+"</"+i+">\n"},t.listitem=function(e){return"<li>"+e+"</li>\n"},t.checkbox=function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},t.paragraph=function(e){return"<p>"+e+"</p>\n"},t.table=function(e,t){return"<table>\n<thead>\n"+e+"</thead>\n"+(t=t&&"<tbody>"+t+"</tbody>")+"</table>\n"},t.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},t.tablecell=function(e,t){var a=t.header?"th":"td";return(t.align?"<"+a+' align="'+t.align+'">':"<"+a+">")+e+"</"+a+">\n"},t.strong=function(e){return"<strong>"+e+"</strong>"},t.em=function(e){return"<em>"+e+"</em>"},t.codespan=function(e){return"<code>"+e+"</code>"},t.br=function(){return this.options.xhtml?"<br/>":"<br>"},t.del=function(e){return"<del>"+e+"</del>"},t.link=function(e,t,a){return null===(e=f(this.options.sanitize,this.options.baseUrl,e))?a:(e='<a href="'+e+'"',t&&(e+=' title="'+t+'"'),e+">"+a+"</a>")},t.image=function(e,t,a){return null===(e=f(this.options.sanitize,this.options.baseUrl,e))?a:(e='<img src="'+e+'" alt="'+a+'"',t&&(e+=' title="'+t+'"'),e+(this.options.xhtml?"/>":">"))},t.text=function(e){return e},e}(),_=function(){function e(){}var t=e.prototype;return t.strong=function(e){return e},t.em=function(e){return e},t.codespan=function(e){return e},t.del=function(e){return e},t.html=function(e){return e},t.text=function(e){return e},t.link=function(e,t,a){return""+a},t.image=function(e,t,a){return""+a},t.br=function(){return""},e}(),U=function(){function e(){this.seen={}}var t=e.prototype;return t.serialize=function(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")},t.getNextSafeSlug=function(e,t){var a=e,i=0;if(this.seen.hasOwnProperty(a))for(i=this.seen[e];a=e+"-"+ ++i,this.seen.hasOwnProperty(a););return t||(this.seen[e]=i,this.seen[a]=0),a},t.slug=function(e,t){void 0===t&&(t={});e=this.serialize(e);return this.getNextSafeSlug(e,t.dryrun)},e}(),B=function(){function a(e){this.options=e||o.defaults,this.options.renderer=this.options.renderer||new V,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new _,this.slugger=new U}a.parse=function(e,t){return new a(t).parse(e)},a.parseInline=function(e,t){return new a(t).parseInline(e)};var e=a.prototype;return e.parse=function(e,t){void 0===t&&(t=!0);for(var a,i,o,n,r,l,s,d,u,c,x,m,p,f,g,h,b="",y=e.length,w=0;w<y;w++)if(d=e[w],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[d.type]&&(!1!==(h=this.options.extensions.renderers[d.type].call({parser:this},d))||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(d.type)))b+=h||"";else switch(d.type){case"space":continue;case"hr":b+=this.renderer.hr();continue;case"heading":b+=this.renderer.heading(this.parseInline(d.tokens),d.depth,v(this.parseInline(d.tokens,this.textRenderer)),this.slugger);continue;case"code":b+=this.renderer.code(d.text,d.lang,d.escaped);continue;case"table":for(l=u="",o=d.header.length,a=0;a<o;a++)l+=this.renderer.tablecell(this.parseInline(d.header[a].tokens),{header:!0,align:d.align[a]});for(u+=this.renderer.tablerow(l),s="",o=d.rows.length,a=0;a<o;a++){for(l="",n=(r=d.rows[a]).length,i=0;i<n;i++)l+=this.renderer.tablecell(this.parseInline(r[i].tokens),{header:!1,align:d.align[i]});s+=this.renderer.tablerow(l)}b+=this.renderer.table(u,s);continue;case"blockquote":s=this.parse(d.tokens),b+=this.renderer.blockquote(s);continue;case"list":for(u=d.ordered,E=d.start,c=d.loose,o=d.items.length,s="",a=0;a<o;a++)p=(m=d.items[a]).checked,f=m.task,x="",m.task&&(g=this.renderer.checkbox(p),c?0<m.tokens.length&&"paragraph"===m.tokens[0].type?(m.tokens[0].text=g+" "+m.tokens[0].text,m.tokens[0].tokens&&0<m.tokens[0].tokens.length&&"text"===m.tokens[0].tokens[0].type&&(m.tokens[0].tokens[0].text=g+" "+m.tokens[0].tokens[0].text)):m.tokens.unshift({type:"text",text:g}):x+=g),x+=this.parse(m.tokens,c),s+=this.renderer.listitem(x,f,p);b+=this.renderer.list(s,u,E);continue;case"html":b+=this.renderer.html(d.text);continue;case"paragraph":b+=this.renderer.paragraph(this.parseInline(d.tokens));continue;case"text":for(s=d.tokens?this.parseInline(d.tokens):d.text;w+1<y&&"text"===e[w+1].type;)s+="\n"+((d=e[++w]).tokens?this.parseInline(d.tokens):d.text);b+=t?this.renderer.paragraph(s):s;continue;default:var E='Token with "'+d.type+'" type was not found.';if(this.options.silent)return void console.error(E);throw new Error(E)}return b},e.parseInline=function(e,t){t=t||this.renderer;for(var a,i,o="",n=e.length,r=0;r<n;r++)if(a=e[r],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]&&(!1!==(i=this.options.extensions.renderers[a.type].call({parser:this},a))||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)))o+=i||"";else switch(a.type){case"escape":o+=t.text(a.text);break;case"html":o+=t.html(a.text);break;case"link":o+=t.link(a.href,a.title,this.parseInline(a.tokens,t));break;case"image":o+=t.image(a.href,a.title,a.text);break;case"strong":o+=t.strong(this.parseInline(a.tokens,t));break;case"em":o+=t.em(this.parseInline(a.tokens,t));break;case"codespan":o+=t.codespan(a.text);break;case"br":o+=t.br();break;case"del":o+=t.del(this.parseInline(a.tokens,t));break;case"text":o+=t.text(a.text);break;default:var l='Token with "'+a.type+'" type was not found.';if(this.options.silent)return void console.error(l);throw new Error(l)}return o},a}();function M(e,a,i){if(null==e)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if("function"==typeof a&&(i=a,a=null),E(a=b({},M.defaults,a||{})),i){var o,n=a.highlight;try{o=T.lex(e,a)}catch(e){return i(e)}var r,l=function(t){var e;if(!t)try{a.walkTokens&&M.walkTokens(o,a.walkTokens),e=B.parse(o,a)}catch(e){t=e}return a.highlight=n,t?i(t):i(null,e)};return!n||n.length<3?l():(delete a.highlight,o.length?(r=0,M.walkTokens(o,function(a){"code"===a.type&&(r++,setTimeout(function(){n(a.text,a.lang,function(e,t){if(e)return l(e);null!=t&&t!==a.text&&(a.text=t,a.escaped=!0),0===--r&&l()})},0))}),void(0===r&&l())):l())}function t(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",a.silent)return"<p>An error occurred:</p><pre>"+u(e.message+"",!0)+"</pre>";throw e}try{var s=T.lex(e,a);if(a.walkTokens){if(a.async)return Promise.all(M.walkTokens(s,a.walkTokens)).then(function(){return B.parse(s,a)}).catch(t);M.walkTokens(s,a.walkTokens)}return B.parse(s,a)}catch(e){t(e)}}M.options=M.setOptions=function(e){return b(M.defaults,e),e=M.defaults,o.defaults=e,M},M.getDefaults=e,M.defaults=o.defaults,M.use=function(){for(var d=M.defaults.extensions||{renderers:{},childTokens:{}},e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];t.forEach(function(r){var a,e=b({},r);if(e.async=M.defaults.async||e.async,r.extensions&&(r.extensions.forEach(function(o){if(!o.name)throw new Error("extension name required");var n;if(o.renderer&&(n=d.renderers[o.name],d.renderers[o.name]=n?function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=o.renderer.apply(this,t);return i=!1===i?n.apply(this,t):i}:o.renderer),o.tokenizer){if(!o.level||"block"!==o.level&&"inline"!==o.level)throw new Error("extension level must be 'block' or 'inline'");d[o.level]?d[o.level].unshift(o.tokenizer):d[o.level]=[o.tokenizer],o.start&&("block"===o.level?d.startBlock?d.startBlock.push(o.start):d.startBlock=[o.start]:"inline"===o.level&&(d.startInline?d.startInline.push(o.start):d.startInline=[o.start]))}o.childTokens&&(d.childTokens[o.name]=o.childTokens)}),e.extensions=d),r.renderer){var t,l=M.defaults.renderer||new V;for(t in r.renderer)!function(o){var n=l[o];l[o]=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=r.renderer[o].apply(l,t);return i=!1===i?n.apply(l,t):i}}(t);e.renderer=l}if(r.tokenizer){var i,s=M.defaults.tokenizer||new C;for(i in r.tokenizer)!function(o){var n=s[o];s[o]=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];var i=r.tokenizer[o].apply(s,t);return i=!1===i?n.apply(s,t):i}}(i);e.tokenizer=s}r.walkTokens&&(a=M.defaults.walkTokens,e.walkTokens=function(e){var t=[];return t.push(r.walkTokens.call(this,e)),t=a?t.concat(a.call(this,e)):t}),M.setOptions(e)})},M.walkTokens=function(e,l){for(var s,d=[],t=A(e);!(s=t()).done;)!function(){var t=s.value;switch(d=d.concat(l.call(M,t)),t.type){case"table":for(var e=A(t.header);!(a=e()).done;){var a=a.value;d=d.concat(M.walkTokens(a.tokens,l))}for(var i,o=A(t.rows);!(i=o()).done;)for(var n=A(i.value);!(r=n()).done;){var r=r.value;d=d.concat(M.walkTokens(r.tokens,l))}break;case"list":d=d.concat(M.walkTokens(t.items,l));break;default:M.defaults.extensions&&M.defaults.extensions.childTokens&&M.defaults.extensions.childTokens[t.type]?M.defaults.extensions.childTokens[t.type].forEach(function(e){d=d.concat(M.walkTokens(t[e],l))}):t.tokens&&(d=d.concat(M.walkTokens(t.tokens,l)))}}();return d},M.parseInline=function(e,t){if(null==e)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");E(t=b({},M.defaults,t||{}));try{var a=T.lexInline(e,t);return t.walkTokens&&M.walkTokens(a,t.walkTokens),B.parseInline(a,t)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+u(e.message+"",!0)+"</pre>";throw e}},M.Parser=B,M.parser=B.parse,M.Renderer=V,M.TextRenderer=_,M.Lexer=T,M.lexer=T.lex,M.Tokenizer=C,M.Slugger=U;var h=(M.parse=M).options,z=M.setOptions,j=M.use,$=M.walkTokens,H=M.parseInline,W=M,q=B.parse,K=T.lex;o.Lexer=T,o.Parser=B,o.Renderer=V,o.Slugger=U,o.TextRenderer=_,o.Tokenizer=C,o.getDefaults=e,o.lexer=K,o.marked=M,o.options=h,o.parse=W,o.parseInline=H,o.parser=q,o.setOptions=z,o.use=j,o.walkTokens=$});