if(Ext.ns("Proxmox"),Ext.ns("Proxmox.Setup"),!Ext.isDefined(Proxmox.Setup.auth_cookie_name))throw"Proxmox library not initialized";if(!Ext.isDefined(Ext.global.console)){let e={dir:function(){},log:function(){},warn:function(){}};Ext.global.console=e}Ext.Ajax.defaultHeaders={Accept:"application/json"},Ext.Ajax.on("beforerequest",function(e,t){Proxmox.CSRFPreventionToken&&(t.headers||(t.headers={}),t.headers.CSRFPreventionToken=Proxmox.CSRFPreventionToken);let i=Proxmox.Utils.getStoredAuth();i.token&&(t.headers.Authorization=i.token)}),Ext.define("Proxmox.Utils",{utilities:{yesText:gettext("Yes"),noText:gettext("No"),enabledText:gettext("Enabled"),disabledText:gettext("Disabled"),noneText:gettext("none"),NoneText:gettext("None"),errorText:gettext("Error"),warningsText:gettext("Warnings"),unknownText:gettext("Unknown"),defaultText:gettext("Default"),daysText:gettext("days"),dayText:gettext("day"),runningText:gettext("running"),stoppedText:gettext("stopped"),neverText:gettext("never"),totalText:gettext("Total"),usedText:gettext("Used"),directoryText:gettext("Directory"),stateText:gettext("State"),groupText:gettext("Group"),language_map:{ar:`العربية - ${gettext("Arabic")}`,bg:`Български - ${gettext("Bulgarian")}`,ca:`Català - ${gettext("Catalan")}`,cs:`Czech - ${gettext("Czech")}`,da:`Dansk - ${gettext("Danish")}`,de:`Deutsch - ${gettext("German")}`,en:`English - ${gettext("English")}`,es:`Español - ${gettext("Spanish")}`,eu:`Euskera (Basque) - ${gettext("Euskera (Basque)")}`,fa:`فارسی - ${gettext("Persian (Farsi)")}`,fr:`Français - ${gettext("French")}`,hr:`Hrvatski - ${gettext("Croatian")}`,he:`עברית - ${gettext("Hebrew")}`,it:`Italiano - ${gettext("Italian")}`,ja:`日本語 - ${gettext("Japanese")}`,ka:`ქართული - ${gettext("Georgian")}`,ko:`한국어 - ${gettext("Korean")}`,nb:`Bokmål - ${gettext("Norwegian (Bokmal)")}`,nl:`Nederlands - ${gettext("Dutch")}`,nn:`Nynorsk - ${gettext("Norwegian (Nynorsk)")}`,pl:`Polski - ${gettext("Polish")}`,pt_BR:`Português Brasileiro - ${gettext("Portuguese (Brazil)")}`,ru:`Русский - ${gettext("Russian")}`,sl:`Slovenščina - ${gettext("Slovenian")}`,sv:`Svenska - ${gettext("Swedish")}`,tr:`Türkçe - ${gettext("Turkish")}`,ukr:`Українська - ${gettext("Ukrainian")}`,zh_CN:`中文（简体）- ${gettext("Chinese (Simplified)")}`,zh_TW:`中文（繁體）- ${gettext("Chinese (Traditional)")}`},render_language:function(e){if(!e||"__default__"===e)return Proxmox.Utils.defaultText+" (English)";"kr"===e&&(e="ko");let t=Proxmox.Utils.language_map[e];return t?t+" ("+e+")":e},renderEnabledIcon:e=>`<i class="fa fa-${e?"check":"minus"}"></i>`,language_array:function(){let e=[["__default__",Proxmox.Utils.render_language("")]];return Ext.Object.each(Proxmox.Utils.language_map,function(t,i){e.push([t,Proxmox.Utils.render_language(i)])}),e},theme_map:{crisp:"Light theme","proxmox-dark":"Proxmox Dark"},render_theme:function(e){if(!e||"__default__"===e)return Proxmox.Utils.defaultText+" (auto)";let t=Proxmox.Utils.theme_map[e];return t||e},theme_array:function(){let e=[["__default__",Proxmox.Utils.render_theme("")]];return Ext.Object.each(Proxmox.Utils.theme_map,function(t,i){e.push([t,Proxmox.Utils.render_theme(i)])}),e},bond_mode_gettext_map:{"802.3ad":"LACP (802.3ad)","lacp-balance-slb":"LACP (balance-slb)","lacp-balance-tcp":"LACP (balance-tcp)"},render_bond_mode:e=>Proxmox.Utils.bond_mode_gettext_map[e]||e||"",bond_mode_array:function(e){return e.map(e=>[e,Proxmox.Utils.render_bond_mode(e)])},getNoSubKeyHtml:function(e){let t=Ext.String.format('<a target="_blank" href="{0}">www.proxmox.com</a>',e||"https://www.proxmox.com");return Ext.String.format(gettext("You do not have a valid subscription for this server. Please visit {0} to get a list of available options."),t)},format_boolean_with_default:function(e){return Ext.isDefined(e)&&"__default__"!==e?e?Proxmox.Utils.yesText:Proxmox.Utils.noText:Proxmox.Utils.defaultText},format_boolean:function(e){return e?Proxmox.Utils.yesText:Proxmox.Utils.noText},format_neg_boolean:function(e){return e?Proxmox.Utils.noText:Proxmox.Utils.yesText},format_enabled_toggle:function(e){return e?Proxmox.Utils.enabledText:Proxmox.Utils.disabledText},format_expire:function(e){return e?Ext.Date.format(e,"Y-m-d"):Proxmox.Utils.neverText},format_duration_human:function(e){let t=0,i=0,a=0,n=0,o=0;if(e<=.1)return"<0.1s";let r=e;t=Number((r%60).toFixed(1)),r=Math.trunc(r/60),r>0&&(i=r%60,r=Math.trunc(r/60),r>0&&(a=r%24,r=Math.trunc(r/24),r>0&&(n=r%365,r=Math.trunc(r/365),r>0&&(o=r))));let l=[],s=(e,t)=>(e>0&&l.push(e+t),e>0),d=!s(o,"y"),u=!s(n,"d");return s(a,"h"),d&&(s(i,"m"),u&&s(t,"s")),l.join(" ")},format_duration_long:function(e){let t=Math.floor(e/86400);e-=86400*t;let i=Math.floor(e/3600);e-=3600*i;let a=Math.floor(e/60);e-=60*a;let n="00"+i.toString();n=n.substr(n.length-2);let o="00"+a.toString();o=o.substr(o.length-2);let r="00"+e.toString();if(r=r.substr(r.length-2),t){let e=t>1?Proxmox.Utils.daysText:Proxmox.Utils.dayText;return t.toString()+" "+e+" "+n+":"+o+":"+r}return n+":"+o+":"+r},format_subscription_level:function(e){return"c"===e?"Community":"b"===e?"Basic":"s"===e?"Standard":"p"===e?"Premium":Proxmox.Utils.noneText},compute_min_label_width:function(e,t){void 0===t&&(t=100);let i=(new Ext.util.TextMetrics).getWidth(e+":");return i<t?t:i},parse_userid:function(e){if(!Ext.isString(e))return[void 0,void 0];let t=e.match(/^(.+)@([^@]+)$/);return null!==t?[t[1],t[2]]:[void 0,void 0]},render_username:function(e){let t=Proxmox.Utils.parse_userid(e)[0]||"";return Ext.htmlEncode(t)},render_realm:function(e){let t=Proxmox.Utils.parse_userid(e)[1]||"";return Ext.htmlEncode(t)},getStoredAuth:function(){return JSON.parse(window.localStorage.getItem("ProxmoxUser"))||{}},setAuthData:function(e){Proxmox.UserName=e.username,Proxmox.LoggedOut=e.LoggedOut,e.ticket&&(Proxmox.CSRFPreventionToken=e.CSRFPreventionToken,Ext.util.Cookies.set(Proxmox.Setup.auth_cookie_name,e.ticket,null,"/",null,!0,"lax")),e.token&&window.localStorage.setItem("ProxmoxUser",JSON.stringify(e))},authOK:function(){if(Proxmox.LoggedOut)return;let e=Proxmox.Utils.getStoredAuth(),t=Ext.util.Cookies.get(Proxmox.Setup.auth_cookie_name);return!!(""!==Proxmox.UserName&&t&&!t.startsWith("PVE:tfa!")||e.token)&&(t||e.token)},authClear:function(){Proxmox.LoggedOut||(Ext.util.Cookies.set(Proxmox.Setup.auth_cookie_name,"",new Date(0),null,null,!0,"lax"),window.localStorage.removeItem("ProxmoxUser"))},getOpenIDRedirectionAuthorization:function(){const e=Ext.Object.fromQueryString(window.location.search);if(void 0!==e.state&&void 0!==e.code)return e},setErrorMask:function(e,t){let i=e.el;i&&(t?!0===t?i.mask(gettext("Loading...")):i.mask(t):i.unmask())},getResponseErrorMessage:e=>{if(!e.statusText)return gettext("Connection error");let t=[`${e.statusText} (${e.status})`];if(e.response&&e.response.responseText){let i=e.response.responseText;try{let e=JSON.parse(i);if(e.errors&&"object"==typeof e.errors)for(let[i,a]of Object.entries(e.errors))t.push(Ext.String.htmlEncode(`${i}: ${a}`))}catch(e){t.push(Ext.String.htmlEncode(i))}}return t.join("<br>")},monStoreErrors:function(e,t,i,a){i?e.mon(t,"beforeload",function(t,i,a){Proxmox.Utils.setErrorMask(e,!1)}):e.mon(t,"beforeload",function(t,i,a){e.loadCount||(e.loadCount=0,Proxmox.Utils.setErrorMask(e,!0))}),e.mon(t.proxy,"afterload",function(t,i,n){if(e.loadCount++,n)return void Proxmox.Utils.setErrorMask(e,!1);let o=i._operation.getError(),r=Proxmox.Utils.getResponseErrorMessage(o);a&&a(o,r)||Proxmox.Utils.setErrorMask(e,Ext.htmlEncode(r))})},extractRequestError:function(e,t){let i=gettext("Successful");return e.success||(i=gettext("Unknown error"),e.message&&(i=Ext.htmlEncode(e.message),e.status&&(i+=` (${e.status})`)),t&&Ext.isObject(e.errors)&&(i+="<br>",Ext.Object.each(e.errors,(e,t)=>{i+=`<br><b>${Ext.htmlEncode(e)}</b>: ${Ext.htmlEncode(t)}`}))),i},API2Request:function(e){let t=Ext.apply({waitMsg:gettext("Please wait...")},e),i=e.autoErrorAlert??("function"!=typeof e.failure&&"function"!=typeof e.callback);t.url.match(/^\/api2/)||(t.url="/api2/extjs"+t.url),delete t.callback;let a=e=>{(void 0===e.waitMsgTargetCount||--e.waitMsgTargetCount<=0)&&(e.setLoading(!1),delete e.waitMsgTargetCount)};var n,o,r;n=e.success,o=e.callback,r=e.failure,Ext.apply(t,{success:function(e,t){t.waitMsgTarget&&("touch"===Proxmox.Utils.toolkit?t.waitMsgTarget.setMasked(!1):a(t.waitMsgTarget));let l=Ext.decode(e.responseText);if(e.result=l,!l.success)return e.htmlStatus=Proxmox.Utils.extractRequestError(l,!0),Ext.callback(o,t.scope,[t,!1,e]),Ext.callback(r,t.scope,[e,t]),void(i&&Ext.Msg.alert(gettext("Error"),e.htmlStatus));Ext.callback(o,t.scope,[t,!0,e]),Ext.callback(n,t.scope,[e,t])},failure:function(e,t){t.waitMsgTarget&&("touch"===Proxmox.Utils.toolkit?t.waitMsgTarget.setMasked(!1):a(t.waitMsgTarget)),e.result={};try{e.result=Ext.decode(e.responseText)}catch(e){}let i=gettext("Connection error")+" - server offline?";e.aborted?i=gettext("Connection error")+" - aborted.":e.timedout?i=gettext("Connection error")+" - Timeout.":e.status&&e.statusText&&(i=gettext("Connection error")+" "+e.status+": "+e.statusText),e.htmlStatus=Ext.htmlEncode(i),Ext.callback(o,t.scope,[t,!1,e]),Ext.callback(r,t.scope,[e,t])}});let l=t.waitMsgTarget;l&&("touch"===Proxmox.Utils.toolkit?l.setMasked({xtype:"loadmask",message:t.waitMsg}):l.rendered?(l.waitMsgTargetCount=(l.waitMsgTargetCount??0)+1,l.setLoading(t.waitMsg)):(l.waitMsgTargetCount=(l.waitMsgTargetCount??0)+1,l.on("afterlayout",function(){(l.waitMsgTargetCount??0)>0&&l.setLoading(t.waitMsg)},l,{single:!0}))),Ext.Ajax.request(t)},alertResponseFailure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus||e.result.message),checked_command:function(e){e()},assemble_field_data:function(e,t){Ext.isObject(t)&&Ext.Object.each(t,function(t,i){if(Object.prototype.hasOwnProperty.call(e,t)){let a=e[t];Ext.isArray(a)||(a=e[t]=[a]),Ext.isArray(i)?e[t]=a.concat(i):a.push(i)}else e[t]=i})},updateColumnWidth:function(e,t){let i,a=Ext.state.Manager.get("summarycolumns")||"auto";if("auto"!==a?(i=parseInt(a,10),Number.isNaN(i)&&(i=1)):(t=(t||1400)+1,i=Math.ceil(e.getSize().width/t)),e.oldFactor===i)return;let n=e.query(">");i=Math.min(i,n.length),e.oldFactor=i,n.forEach(e=>{e.columnWidth=1/i}),e.updateLayout(),e.updateLayout()},updateColumns:e=>Proxmox.Utils.updateColumnWidth(e),dialog_title:function(e,t,i){return t?i?gettext("Add")+": "+e:gettext("Create")+": "+e:gettext("Edit")+": "+e},network_iface_types:{eth:gettext("Network Device"),bridge:"Linux Bridge",bond:"Linux Bond",vlan:"Linux VLAN",OVSBridge:"OVS Bridge",OVSBond:"OVS Bond",OVSPort:"OVS Port",OVSIntPort:"OVS IntPort"},render_network_iface_type:function(e){return Proxmox.Utils.network_iface_types[e]||Proxmox.Utils.unknownText},notificationFieldName:{type:gettext("Notification type"),hostname:gettext("Hostname")},formatNotificationFieldName:e=>Proxmox.Utils.notificationFieldName[e]||e,overrideNotificationFieldName:function(e){for(const[t,i]of Object.entries(e))Proxmox.Utils.notificationFieldName[t]=i},notificationFieldValue:{"system-mail":gettext("Forwarded mails to the local root user")},formatNotificationFieldValue:e=>Proxmox.Utils.notificationFieldValue[e]||e,overrideNotificationFieldValue:function(e){for(const[t,i]of Object.entries(e))Proxmox.Utils.notificationFieldValue[t]=i},task_desc_table:{aptupdate:["",gettext("Update package database")],diskinit:["Disk",gettext("Initialize Disk with GPT")],spiceshell:["",gettext("Shell")+" (Spice)"],srvreload:["SRV",gettext("Reload")],srvrestart:["SRV",gettext("Restart")],srvstart:["SRV",gettext("Start")],srvstop:["SRV",gettext("Stop")],termproxy:["",gettext("Console")+" (xterm.js)"],vncshell:["",gettext("Shell")]},override_task_descriptions:function(e){for(const[t,i]of Object.entries(e))Proxmox.Utils.task_desc_table[t]=i},format_task_description:function(e,t){let i,a=Proxmox.Utils.task_desc_table[e];if(!a)return i=e,t&&(e+=" "+t),i;if(Ext.isFunction(a))return a(e,t);let n=a[0];return i=a[1],n&&void 0!==t?n+" "+t+" - "+i:i},format_size:function(e,t){let i=[gettext("B"),gettext("KB"),gettext("MB"),gettext("GB"),gettext("TB"),gettext("PB"),gettext("EB"),gettext("ZB"),gettext("YB")],a=[gettext("B"),gettext("KiB"),gettext("MiB"),gettext("GiB"),gettext("TiB"),gettext("PiB"),gettext("EiB"),gettext("ZiB"),gettext("YiB")],n=0,o=2;const r=t?1e3:1024;for(;e>=r&&n<i.length;)e/=r,n++;let l=t?i[n]:a[n];return 0===n&&(o=0),`${e.toFixed(o)} ${l}`},SizeUnits:{B:1,KiB:1024,MiB:1048576,GiB:1073741824,TiB:1099511627776,PiB:0x4000000000000,KB:1e3,MB:1e6,GB:1e9,TB:1e12,PB:1e15},parse_size_unit:function(e){let t=e.match(/(\d+(?:\.\d+)?)\s?([KMGTP]?)(i?)B?\s*$/i),i=parseFloat(t[1]),a=t[2].toUpperCase(),n=t[3].toLowerCase(),o=`${a}${n}B`;return{size:i,factor:Proxmox.Utils.SizeUnits[o],unit:o,binary:n}},size_unit_to_bytes:function(e){let{size:t,factor:i}=Proxmox.Utils.parse_size_unit(e);return t*i},autoscale_size_unit:function(e){let{size:t,factor:i,binary:a}=Proxmox.Utils.parse_size_unit(e);return Proxmox.Utils.format_size(t*i,"i"!==a)},size_unit_ratios:function(e,t){return t=void 0!==t?t:1/0,("number"==typeof(e=void 0!==e?e:0)?e:Proxmox.Utils.size_unit_to_bytes(e))/(("number"==typeof t?t:Proxmox.Utils.size_unit_to_bytes(t))||1/0)},render_upid:function(e,t,i){let a=i.data,n=a.type||a.worker_type,o=a.id||a.worker_id;return Ext.htmlEncode(Proxmox.Utils.format_task_description(n,o))},render_uptime:function(e){let t=e;return void 0===t?"":t<=0?"-":Proxmox.Utils.format_duration_long(t)},systemd_unescape:function(e){const t="0".charCodeAt(0),i="9".charCodeAt(0),a="A".charCodeAt(0),n="F".charCodeAt(0),o="a".charCodeAt(0),r="f".charCodeAt(0),l="x".charCodeAt(0),s="-".charCodeAt(0),d="/".charCodeAt(0),u="\\".charCodeAt(0);let c=function(e){if(e>=t&&e<=i)return e-t;if(e>=a&&e<=n)return e-a+10;if(e>=o&&e<=r)return e-o+10;throw"got invalid hex digit"},x=(new TextEncoder).encode(e),m=new Uint8Array(x.length),p=0,f=0;for(;p<x.length;){let e=x[p];if(e!==s){if(p+4<x.length){let t=x[p+1];if(e===u&&t===l){let e=16*c(x[p+2])+c(x[p+3]);m.set([e],f),f+=1,p+=4;continue}}m.set([e],f),f+=1,p+=1}else m.set([d],f),f+=1,p+=1}return(new TextDecoder).decode(m.slice(0,m.len))},parse_task_upid:function(e){let t={},i=e.match(/^UPID:([^\s:]+):([0-9A-Fa-f]{8}):([0-9A-Fa-f]{8,9}):(([0-9A-Fa-f]{8,16}):)?([0-9A-Fa-f]{8}):([^:\s]+):([^:\s]*):([^:\s]+):$/);if(!i)throw"unable to parse upid '"+e+"'";return t.node=i[1],t.pid=parseInt(i[2],16),t.pstart=parseInt(i[3],16),void 0!==i[5]&&(t.task_id=parseInt(i[5],16)),t.starttime=parseInt(i[6],16),t.type=i[7],t.id=Proxmox.Utils.systemd_unescape(i[8]),t.user=i[9],t.desc=Proxmox.Utils.format_task_description(t.type,t.id),t},parse_task_status:function(e){if("OK"===e)return"ok";if("unknown"===e)return"unknown";return e.match(/^WARNINGS: (.*)$/)?"warning":"error"},format_task_status:function(e){switch(Proxmox.Utils.parse_task_status(e)){case"unknown":return Proxmox.Utils.unknownText;case"error":return Proxmox.Utils.errorText+": "+Ext.htmlEncode(e);case"warning":return e.replace("WARNINGS",Proxmox.Utils.warningsText);default:return e}},render_duration:function(e){return void 0===e?"-":Proxmox.Utils.format_duration_human(e)},render_timestamp:function(e,t,i,a,n,o){let r=new Date(1e3*e);return Ext.Date.format(r,"Y-m-d H:i:s")},render_zfs_health:function(e){if(void 0===e)return"";var t="question-circle";switch(e){case"AVAIL":case"ONLINE":t="check-circle good";break;case"REMOVED":case"DEGRADED":t="exclamation-circle warning";break;case"UNAVAIL":case"FAULTED":case"OFFLINE":t="times-circle critical"}return'<i class="fa fa-'+t+'"></i> '+e},get_help_info:function(e){let t;if("undefined"!=typeof proxmoxOnlineHelpInfo)t=proxmoxOnlineHelpInfo;else{if("undefined"==typeof pveOnlineHelpInfo)throw"no global OnlineHelpInfo map declared";t=pveOnlineHelpInfo}if(t[e])return t[e];let i=e.replace(/_/g,"-");return t[i]?t[i]:t[e.replace(/-/g,"_")]},get_help_link:function(e){let t=Proxmox.Utils.get_help_info(e);if(t)return window.location.origin+t.link},openXtermJsViewer:function(e,t,i,a,n){let o=Ext.Object.toQueryString({console:e,xtermjs:1,vmid:t,vmname:a,node:i,cmd:n}),r=window.open("?"+o,"_blank","toolbar=no,location=no,status=no,menubar=no,resizable=yes,width=800,height=420");r&&r.focus()},render_optional_url:function(e){return e&&null!==e.match(/^https?:\/\//)?'<a target="_blank" href="'+e+'">'+e+"</a>":e},render_san:function(e){var t=[];return Ext.isArray(e)?(e.forEach(function(e){Ext.isNumber(e)||t.push(e)}),t.join("<br>")):e},render_usage:e=>(100*e).toFixed(2)+"%",render_cpu_usage:function(e,t){return Ext.String.format(`${gettext("{0}% of {1}")} ${gettext("CPU(s)")}`,(100*e).toFixed(2),t)},render_size_usage:function(e,t,i){if(0===t)return gettext("N/A");let a=e=>Proxmox.Utils.format_size(e,i);return(100*e/t).toFixed(2)+"% ("+Ext.String.format(gettext("{0} of {1}"),a(e),a(t))+")"},render_cpu:function(e,t,i,a,n,o){if(!i.data.uptime||!Ext.isNumeric(e))return"";let r=i.data.maxcpu||1;if(!Ext.isNumeric(r)||r<1)return"";let l=r>1?"CPUs":"CPU";return`${(100*e).toFixed(1)}% of ${r.toString()} ${l}`},render_size:function(e,t,i,a,n,o){return Ext.isNumeric(e)?Proxmox.Utils.format_size(e):""},render_cpu_model:function(e){let t=e.sockets>1?gettext("Sockets"):gettext("Socket");return`${e.cpus} x ${e.model} (${e.sockets.toString()} ${t})`},render_node_cpu_usage:function(e,t){return Proxmox.Utils.render_cpu_usage(e,t.cpus)},render_node_size_usage:function(e){return Proxmox.Utils.render_size_usage(e.used,e.total)},loadTextFromFile:function(e,t,i){let a=i||8192;if(e.size>a)return void Ext.Msg.alert(gettext("Error"),gettext("Invalid file size: ")+e.size);let n=new FileReader;n.onload=e=>t(e.target.result),n.readAsText(e)},parsePropertyString:function(e,t){var i,a={};return"string"!=typeof e||""===e?a:(Ext.Array.each(e.split(","),function(e){var n=e.split("=",2);if(Ext.isDefined(n[1]))a[n[0]]=n[1];else{if(!Ext.isDefined(t))return i="invalid propertyString, not a key=value pair and no defaultKey defined",!1;if(Ext.isDefined(a[t]))return i="defaultKey may be only defined once in propertyString",!1;a[t]=n[0]}return!0}),void 0===i?a:void console.error(i))},printPropertyString:function(e,t){var i,a=[],n=!1;return Ext.Object.each(e,function(e,o){void 0!==t&&e===t?(n=!0,i=o):Ext.isArray(o)?a.push(e+"="+o.join(";")):""!==o&&a.push(e+"="+o)}),a=a.sort(),n&&a.unshift(i),a.join(",")},acmedomain_count:5,parseACMEPluginData:function(e){let t={},i=[];return e.split("\n").forEach(e=>{let[a,n]=e.split("=");void 0!==n?t[a]=n:i.push(e)}),[t,i]},delete_if_default:function(e,t,i,a){""!==e[t]&&e[t]!==i||(a||(e.delete?Ext.isArray(e.delete)?e.delete.push(t):e.delete+=","+t:e.delete=t),delete e[t])},printACME:function(e){return Ext.isArray(e.domains)&&(e.domains=e.domains.join(";")),Proxmox.Utils.printPropertyString(e)},parseACME:function(e){if(!e)return{};var t,i={};if(Ext.Array.each(e.split(","),function(e){var a=e.split("=",2);return Ext.isDefined(a[1])?(i[a[0]]=a[1],!0):(t="Failed to parse key-value pair: "+e,!1)}),void 0===t)return void 0!==i.domains&&(i.domains=i.domains.split(/;/)),i;console.error(t)},add_domain_to_acme:function(e,t){return void 0===e.domains?e.domains=[t]:(e.domains.push(t),e.domains=e.domains.filter((e,t,i)=>i.indexOf(e)===t)),e},remove_domain_from_acme:function(e,t){return void 0!==e.domains&&(e.domains=e.domains.filter((e,i,a)=>a.indexOf(e)===i&&e!==t)),e},get_health_icon:function(e,t){void 0===t&&(t=!1),void 0===e&&(e="uknown");var i="faded fa-question";switch(e){case"good":i="good fa-check";break;case"upgrade":i="warning fa-upload";break;case"old":i="warning fa-refresh";break;case"warning":i="warning fa-exclamation";break;case"critical":i="critical fa-times"}return t&&(i+="-circle"),i},formatNodeRepoStatus:function(e,t){let i=(e,t)=>`<i class="fa fa-fw fa-lg fa-${t}"></i>${e}`,a=Ext.String.format(gettext("{0} updates"),t),n=Ext.String.format(gettext("No {0} repository enabled!"),t);return"ok"===e?i(a,"check-circle good")+" "+i(gettext("Production-ready Enterprise repository enabled"),"check-circle good"):"no-sub"===e?i(gettext("Production-ready Enterprise repository enabled"),"check-circle good")+" "+i(gettext("Enterprise repository needs valid subscription"),"exclamation-circle warning"):"non-production"===e?i(a,"check-circle good")+" "+i(gettext("Non production-ready repository enabled!"),"exclamation-circle warning"):"no-repo"===e?i(n,"exclamation-circle critical"):Proxmox.Utils.unknownText},render_u2f_error:function(e){return"U2F Error: "+{1:gettext("Other Error"),2:gettext("Bad Request"),3:gettext("Configuration Unsupported"),4:gettext("Device Ineligible"),5:gettext("Timeout")}[e]||Proxmox.Utils.unknownText},bytes_to_base64url:function(e){return null===e?null:btoa(Array.from(new Uint8Array(e)).map(e=>String.fromCharCode(e)).join("")).replace(/\+/g,"-").replace(/\//g,"_").replace(/[=]/g,"")},base64url_to_bytes:function(e){return null===e?null:new Uint8Array(atob(e.replace(/-/g,"+").replace(/_/g,"/")).split("").map(e=>e.charCodeAt(0)))},utf8ToBase64:function(e){let t=(new TextEncoder).encode(e);const i=Array.from(t,e=>String.fromCodePoint(e)).join("");return btoa(i)},base64ToUtf8:function(e){let t=atob(e),i=Uint8Array.from(t,e=>e.codePointAt(0));return(new TextDecoder).decode(i)},stringToRGB:function(e){let t=0;if(!e)return t;e+="prox";for(let i=0;i<e.length;i++)t=e.charCodeAt(i)+((t<<5)-t),t&=t;let i=.7;return[(255&t)*i+255*(1-i),(t>>8&255)*i+255*(1-i),(t>>16&255)*i+255*(1-i)]},rgbToCss:function(e){return`rgb(${e[0]}, ${e[1]}, ${e[2]})`},rgbToHex:function(e){return`${Math.round(e[0]).toString(16)}${Math.round(e[1]).toString(16)}${Math.round(e[2]).toString(16)}`},hexToRGB:function(e){if(!e)return;return 7===e.length&&(e=e.slice(1)),[parseInt(e.slice(0,2),16),parseInt(e.slice(2,4),16),parseInt(e.slice(4,6),16)]},getTextContrastClass:function(e){let t=.2126729*(e[0]/255)**2.4+.7151522*(e[1]/255)**2.4+.072175*(e[2]/255)**2.4;t=t>.022?t:t+(.022-t)**1.414;let i=t**.65-1,a=t**.56-.046134502;return Math.abs(i)>=Math.abs(a)?"light":"dark"},getTagElement:function(e,t){let i,a=t?.[e]||Proxmox.Utils.stringToRGB(e),n=`background-color: ${Proxmox.Utils.rgbToCss(a)};`;if(a.length>3)n+=`color: ${Proxmox.Utils.rgbToCss([a[3],a[4],a[5]])}`,i="proxmox-tag-dark";else{i=`proxmox-tag-${Proxmox.Utils.getTextContrastClass(a)}`}return`<span class="${i}" style="${n}">${e}</span>`},downloadAsFile:function(e,t){let i=document.createElement("a");i.href=e,i.target="_blank",t&&(i.download=t),i.click()}},singleton:!0,constructor:function(){let e=this;Ext.apply(e,e.utilities);let t="(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])",i="(?:(?:"+t+"\\.){3}"+t+")",a="(?:[0-9a-fA-F]{1,4})",n="(?:(?:"+a+":"+a+")|"+i+")",o="([0-9]{1,2})",r="([0-9]{1,3})";e.IP4_match=new RegExp("^(?:"+i+")$"),e.IP4_cidr_match=new RegExp("^(?:"+i+")/"+o+"$");let l="(?:(?:(?:(?:"+a+":){6})"+n+")|(?:(?:::(?:"+a+":){5})"+n+")|(?:(?:(?:"+a+")?::(?:"+a+":){4})"+n+")|(?:(?:(?:(?:"+a+":){0,1}"+a+")?::(?:"+a+":){3})"+n+")|(?:(?:(?:(?:"+a+":){0,2}"+a+")?::(?:"+a+":){2})"+n+")|(?:(?:(?:(?:"+a+":){0,3}"+a+")?::(?:"+a+":){1})"+n+")|(?:(?:(?:(?:"+a+":){0,4}"+a+")?::)"+n+")|(?:(?:(?:(?:"+a+":){0,5}"+a+")?::)"+a+")|(?:(?:(?:(?:"+a+":){0,7}"+a+")?::)))";e.IP6_match=new RegExp("^(?:"+l+")$"),e.IP6_cidr_match=new RegExp("^(?:"+l+")/"+r+"$"),e.IP6_bracket_match=new RegExp("^\\[("+l+")\\]"),e.IP64_match=new RegExp("^(?:"+l+"|"+i+")$"),e.IP64_cidr_match=new RegExp("^(?:"+l+"/"+r+")|(?:"+i+"/"+o+")$");let s="(?:(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9\\-]*[a-zA-Z0-9])?)\\.)*(?:[A-Za-z0-9](?:[A-Za-z0-9\\-]*[A-Za-z0-9])?))";e.DnsName_match=new RegExp("^"+s+"$"),e.DnsName_or_Wildcard_match=new RegExp("^(?:\\*\\.)?"+s+"$"),e.CpuSet_match=/^[0-9]+(?:-[0-9]+)?(?:,[0-9]+(?:-[0-9]+)?)*$/,e.HostPort_match=new RegExp("^("+i+"|"+s+")(?::(\\d+))?$"),e.HostPortBrackets_match=new RegExp("^\\[("+l+"|"+i+"|"+s+")\\](?::(\\d+))?$"),e.IP6_dotnotation_match=new RegExp("^("+l+")(?:\\.(\\d+))?$"),e.Vlan_match=/^vlan(\d+)/,e.VlanInterface_match=/(\w+)\.(\d+)/;let d="^https?://(?:(?:(?:"+("(?:(?:"+s+"\\.)*"+s+")")+"|"+("(?:"+i+"|\\[(?:"+l+")\\])")+")(?::(?:[0-9]{1,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?)|"+l+")(?:/[^\0-]*)?$";e.httpUrlRegex=new RegExp(d),e.safeIdRegex=/^(?:[A-Za-z0-9_][A-Za-z0-9._\\-]*)$/}}),Ext.define("Proxmox.Async",{singleton:!0,api2:function(e){return new Promise((t,i)=>{delete e.callback,e.success=e=>t(e),e.failure=e=>i(e),Proxmox.Utils.API2Request(e)})},sleep:function(e){return new Promise((t,i)=>setTimeout(t,e))}}),Ext.override(Ext.data.Store,{onProxyLoad:function(e){let t=this;"touch"===Proxmox.Utils.toolkit||e.getProxy()===t.getProxy()?t.callParent(arguments):console.log(`ignored outdated response: ${e.getRequest().getUrl()}`)}}),Ext.define("Proxmox.Schema",{singleton:!0,authDomains:{pam:{name:"Linux PAM",ipanel:"pmxAuthSimplePanel",onlineHelp:"user-realms-pam",add:!1,edit:!0,pwchange:!0,sync:!1,useTypeInUrl:!1},openid:{name:gettext("OpenID Connect Server"),ipanel:"pmxAuthOpenIDPanel",add:!0,edit:!0,tfa:!1,pwchange:!1,sync:!1,iconCls:"pmx-itype-icon-openid-logo",useTypeInUrl:!0},ldap:{name:gettext("LDAP Server"),ipanel:"pmxAuthLDAPPanel",syncipanel:"pmxAuthLDAPSyncPanel",add:!0,edit:!0,tfa:!0,pwchange:!1,sync:!0,useTypeInUrl:!0},ad:{name:gettext("Active Directory Server"),ipanel:"pmxAuthADPanel",syncipanel:"pmxAuthADSyncPanel",add:!0,edit:!0,tfa:!0,pwchange:!1,sync:!0,useTypeInUrl:!0}},overrideAuthDomains:function(e){for(const[t,i]of Object.entries(e))Proxmox.Schema.authDomains[t]=i},notificationEndpointTypes:{sendmail:{name:"Sendmail",ipanel:"pmxSendmailEditPanel",iconCls:"fa-envelope-o",defaultMailAuthor:"Proxmox VE"},smtp:{name:"SMTP",ipanel:"pmxSmtpEditPanel",iconCls:"fa-envelope-o",defaultMailAuthor:"Proxmox VE"},gotify:{name:"Gotify",ipanel:"pmxGotifyEditPanel",iconCls:"fa-bell-o"},webhook:{name:"Webhook",ipanel:"pmxWebhookEditPanel",iconCls:"fa-bell-o"}},overrideEndpointTypes:function(e){for(const[t,i]of Object.entries(e))Proxmox.Schema.notificationEndpointTypes[t]=i},pxarFileTypes:{b:{icon:"cube",label:gettext("Block Device")},c:{icon:"tty",label:gettext("Character Device")},d:{icon:"folder-o",label:gettext("Directory")},f:{icon:"file-text-o",label:gettext("File")},h:{icon:"file-o",label:gettext("Hardlink")},l:{icon:"link",label:gettext("Softlink")},p:{icon:"exchange",label:gettext("Pipe/Fifo")},s:{icon:"plug",label:gettext("Socket")},v:{icon:"cube",label:gettext("Virtual")}}}),Ext.Ajax.disableCaching=!1,Ext.apply(Ext.form.field.VTypes,{IPAddress:function(e){return Proxmox.Utils.IP4_match.test(e)},IPAddressText:gettext("Example")+": ***********",IPAddressMask:/[\d.]/i,IPCIDRAddress:function(e){let t=Proxmox.Utils.IP4_cidr_match.exec(e);return null!==t&&t[1]>=8&&t[1]<=32},IPCIDRAddressText:gettext("Example")+": ***********/24<br>"+gettext("Valid CIDR Range")+": 8-32",IPCIDRAddressMask:/[\d./]/i,IP6Address:function(e){return Proxmox.Utils.IP6_match.test(e)},IP6AddressText:gettext("Example")+": 2001:DB8::42",IP6AddressMask:/[A-Fa-f0-9:]/,IP6CIDRAddress:function(e){let t=Proxmox.Utils.IP6_cidr_match.exec(e);return null!==t&&t[1]>=8&&t[1]<=128},IP6CIDRAddressText:gettext("Example")+": 2001:DB8::42/64<br>"+gettext("Valid CIDR Range")+": 8-128",IP6CIDRAddressMask:/[A-Fa-f0-9:/]/,IP6PrefixLength:function(e){return e>=0&&e<=128},IP6PrefixLengthText:gettext("Example")+": X, where 0 <= X <= 128",IP6PrefixLengthMask:/[0-9]/,IP64Address:function(e){return Proxmox.Utils.IP64_match.test(e)},IP64AddressText:gettext("Example")+": *********** 2001:DB8::42",IP64AddressMask:/[A-Fa-f0-9.:]/,IP64CIDRAddress:function(e){let t=Proxmox.Utils.IP64_cidr_match.exec(e);return null!==t&&(void 0!==t[1]?t[1]>=8&&t[1]<=128:void 0!==t[2]&&(t[2]>=8&&t[2]<=32))},IP64CIDRAddressText:gettext("Example")+": ***********/24 2001:DB8::42/64",IP64CIDRAddressMask:/[A-Fa-f0-9.:/]/,MacAddress:function(e){return/^([a-fA-F0-9]{2}:){5}[a-fA-F0-9]{2}$/.test(e)},MacAddressMask:/[a-fA-F0-9:]/,MacAddressText:gettext("Example")+": 01:23:45:67:89:ab",MacPrefix:function(e){return/^[a-f0-9][02468ace](?::[a-f0-9]{2}){0,2}:?$/i.test(e)},MacPrefixMask:/[a-fA-F0-9:]/,MacPrefixText:gettext("Example")+": 02:8f - "+gettext("only unicast addresses are allowed"),BridgeName:function(e){return/^[a-zA-Z][a-zA-Z0-9_]{0,9}$/.test(e)},VlanName:function(e){return Proxmox.Utils.VlanInterface_match.test(e)||Proxmox.Utils.Vlan_match.test(e),!0},BridgeNameText:gettext("Format")+": alphanumeric string starting with a character",BondName:function(e){return/^bond\d{1,4}$/.test(e)},BondNameText:gettext("Format")+": bond<b>N</b>, where 0 <= <b>N</b> <= 9999",InterfaceName:function(e){return/^[a-z][a-z0-9_]{1,20}$/.test(e)},InterfaceNameText:gettext("Allowed characters")+": 'a-z', '0-9', '_'<br />"+gettext("Minimum characters")+": 2<br />"+gettext("Maximum characters")+": 21<br />"+gettext("Must start with")+": 'a-z'",StorageId:function(e){return/^[a-z][a-z0-9\-_.]*[a-z0-9]$/i.test(e)},StorageIdText:gettext("Allowed characters")+":  'A-Z', 'a-z', '0-9', '-', '_', '.'<br />"+gettext("Minimum characters")+": 2<br />"+gettext("Must start with")+": 'A-Z', 'a-z'<br />"+gettext("Must end with")+": 'A-Z', 'a-z', '0-9'<br />",ConfigId:function(e){return/^[a-z][a-z0-9_-]+$/i.test(e)},ConfigIdText:gettext("Allowed characters")+": 'A-Z', 'a-z', '0-9', '_'<br />"+gettext("Minimum characters")+": 2<br />"+gettext("Must start with")+": "+gettext("letter"),HttpProxy:function(e){return/^http:\/\/.*$/.test(e)},HttpProxyText:gettext("Example")+": http://username:password&#64;host:port/",CpuSet:function(e){return Proxmox.Utils.CpuSet_match.test(e)},CpuSetText:gettext("This is not a valid CpuSet"),DnsName:function(e){return Proxmox.Utils.DnsName_match.test(e)},DnsNameText:gettext("This is not a valid hostname"),DnsNameOrWildcard:function(e){return Proxmox.Utils.DnsName_or_Wildcard_match.test(e)},DnsNameOrWildcardText:gettext("This is not a valid hostname"),proxmoxMail:function(e){return/^[\w+-~]+(\.[\w+-~]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*$/.test(e)},proxmoxMailText:gettext("Example")+": <EMAIL>",DnsOrIp:function(e){return!(!Proxmox.Utils.DnsName_match.test(e)&&!Proxmox.Utils.IP64_match.test(e))},DnsOrIpText:gettext("Not a valid DNS name or IP address."),HostPort:function(e){return Proxmox.Utils.HostPort_match.test(e)||Proxmox.Utils.HostPortBrackets_match.test(e)||Proxmox.Utils.IP6_dotnotation_match.test(e)},HostPortText:gettext("Host/IP address or optional port is invalid"),HostList:function(e){let t,i=e.split(/[ ,;]+/);for(t=0;t<i.length;t++)if(""!==i[t]&&!Proxmox.Utils.HostPort_match.test(i[t])&&!Proxmox.Utils.HostPortBrackets_match.test(i[t])&&!Proxmox.Utils.IP6_dotnotation_match.test(i[t]))return!1;return!0},HostListText:gettext("Not a valid list of hosts"),password:function(e,t){if(t.initialPassField){return e===t.up("form").down(`[name=${t.initialPassField}]`).getValue()}return!0},passwordText:gettext("Passwords do not match"),email:function(e){return/^[\w+~-]+(\.[\w+~-]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*$/.test(e)}}),Ext.define("PVE.form.field.Number",{override:"Ext.form.field.Number",submitLocaleSeparator:!1}),Ext.define("PVE.draw.Container",{override:"Ext.draw.Container",defaultDownloadServerUrl:document.location.origin,applyDownloadServerUrl:function(e){return e=e||this.defaultDownloadServerUrl}}),Ext.define("Proxmox.UnderlayPool",{override:"Ext.dom.UnderlayPool",checkOut:function(){let e,t=this.cache,i=t.length;for(;i--;)t[i].destroyed&&t.splice(i,1);return e=t.shift(),e||(e=Ext.Element.create(this.elementConfig),e.setVisibilityMode(2),e.dom.setAttribute("data-sticky",!0)),e}}),Ext.define("Proxmox.form.ComboBox",{override:"Ext.form.field.ComboBox",reset:function(){let e=this;e.callParent();let t=e.getValue();Ext.isArray(e.originalValue)&&Ext.isArray(t)&&!Ext.Array.equals(t,e.originalValue)&&(e.clearValue(),e.setValue(e.originalValue))},initComponent:function(){let e=this;e.callParent(),e.editable&&(Ext.override(e.triggers.picker,{onMouseDown:function(t){"touch"===t.pointerType||this.field.owns(Ext.Element.getActiveElement())||(e.skip_expand_on_focus=!0),this.callParent(arguments)}}),e.on("focus",function(e){e.isExpanded||e.skip_expand_on_focus||e.expand(),e.skip_expand_on_focus=!1}))}}),Ext.define(null,{override:"Ext.view.Table",jumpToFocus:!1,saveFocusState:function(){var e,t,i,a=this,n=a.dataSource,o=a.actionableMode,r=a.getNavigationModel(),l=o?a.actionPosition:r.getPosition(!0),s=Ext.fly(Ext.Element.getActiveElement()),d=l&&l.view===a&&Ext.fly(l.getCell(!0));return!a.skipSaveFocusState&&d&&d.contains(s)?(l=l.clone(),s.suspendFocusEvents(),o&&d.dom!==s.dom?a.suspendActionableMode():(o=!1,r.setPosition()),s.resumeFocusEvents(),n.isExpandingOrCollapsing?Ext.emptyFn:function(){var s;if((n=a.dataSource).getCount()){if(s=a.all,e=Math.min(Math.max(l.rowIdx,s.startIndex),s.endIndex),t=Math.min(l.colIdx,a.getVisibleColumnManager().getColumns().length-1),i=l.record,(l=new Ext.grid.CellContext(a).setPosition(i&&n.contains(i)&&!i.isCollapsedPlaceholder?i:e,t)).getCell(!0))if(o)a.resumeActionableMode(l);else{let e=a.getScrollX(),t=a.getScrollY();r.setPosition(l,null,null,null,!0),r.getPosition()||l.column.focus(),a.jumpToFocus||a.scrollTo(e,t)}}else l.column.focus()}):Ext.emptyFn}}),Ext.define("Proxmox.form.field.Text",{override:"Ext.form.field.Text",setSubmitValue:function(e){this.submitValue=e}}),Ext.define(null,{override:"Ext.layout.container.boxOverflow.Scroller",wheelIncrement:1,getWheelDelta:function(e){return-e.getWheelDelta(e)},onOwnerRender:function(e){var t=this,i={isBoxOverflowScroller:!0,x:!1,y:!1,listeners:{scrollend:this.onScrollEnd,scope:this}};Ext.scrollbar.width()||Ext.platformTags.desktop?t.wheelListener=t.layout.innerCt.on("wheel",t.onMouseWheel,t,{destroyable:!0}):i[e.layout.horizontal?"x":"y"]=!0,e.setScrollable(i)}}),Ext.define("Proxmox.form.field.Spinner",{override:"Ext.form.field.Spinner",onRender:function(){let e=this;e.callParent(),e.mouseWheelEnabled&&(e.mun(e.bodyEl,"mousewheel",e.onMouseWheel,e),e.mon(e.bodyEl,"wheel",e.onMouseWheel,e))},onMouseWheel:function(e){var t,i=this;i.hasFocus&&((t=e.getWheelDelta())>0?i.spinDown():t<0&&i.spinUp(),e.stopEvent(),i.onSpinEnd())}}),Ext.define("Proxmox.validIdReOverride",{override:"Ext.Component",validIdRe:/^[a-z_][a-z0-9\-_@]*$/i}),Ext.define("Proxmox.selection.CheckboxModel",{override:"Ext.selection.CheckboxModel",checkSelector:".x-grid-cell-row-checker",doDeselect:function(e,t){var i,a,n,o=this,r=o.selected,l=0;if(o.locked||!o.store)return!1;if("number"==typeof e){if(!(a=o.store.getAt(e)))return!1;e=[a]}else Ext.isArray(e)||(e=[e]);let s=!1;n=function(){s=!0,a===o.selectionStart&&(o.selectionStart=null)};let d=[];for(i=e.length,o.suspendChanges();l<i;l++)if(a=e[l],o.isSelected(a)&&(s=!1,o.onSelectChange(a,!1,t,n),s||d.push(a),o.destroyed))return!1;return d.length>0&&e.remove(d),r.remove(e),o.lastSelected=r.last(),o.resumeChanges(),o.maybeFireSelectionChange(e.length>0&&!t),e.length},doMultiSelect:function(e,t,i){var a,n,o,r,l,s=this,d=s.selected,u=!1;if(s.locked)return;if(o=(e=Ext.isArray(e)?e:[e]).length,!t&&d.getCount()>0){if(a=s.deselectDuringSelect(e,i),s.destroyed)return;if(a[0])return void s.maybeFireSelectionChange(a[1]>0&&!i);u=a[1]>0}let c,x=[];for(l=function(){d.getCount()||(s.selectionStart=r),c=!1,u=!0},n=0;n<o;n++)if(r=e[n],!s.isSelected(r)){if(c=!0,s.onSelectChange(r,!0,i,l),s.destroyed)return;c&&x.push(r)}x.length>0&&e.remove(x),d.add(e),s.lastSelected=r,s.maybeFireSelectionChange(u&&!i)},deselectDuringSelect:function(e,t){var i=this,a=i.selected.getRange(),n=0,o=!1;i.suspendChanges(),i.deselectingDuringSelect=!0;let r=a.filter(t=>!Ext.Array.contains(e,t));return r.length>0&&((n=i.doDeselect(r,t))||(o=!0),i.destroyed&&(o=!0,n=0)),i.deselectingDuringSelect=!1,i.resumeChanges(),[o,n]}}),Ext.define("Proxmox.Component",{override:"Ext.Component",clearPropertiesOnDestroy:!1}),Ext.define("Proxmox.view.DragZone",{override:"Ext.view.DragZone",onItemMouseDown:function(e,t,i,a,n){"touch"!==n.pointerType&&this.onTriggerGesture(e,t,i,a,n)}}),Ext.define("Proxmox.dd.DragDropManager",{override:"Ext.dd.DragDropManager",stopEvent:function(e){this.stopPropagation&&e.stopPropagation(),this.preventDefault&&e.preventDefault()}}),Ext.define("Proxmox.Cookies",{override:"Ext.util.Cookies",set:function(e,t,i,a,n,o,r){let l=[];i&&l.push("expires="+i.toUTCString()),void 0===a?l.push("path=/"):a&&l.push("path="+a),n&&l.push("domain="+n),!0===o&&l.push("secure"),r&&["lax","none","strict"].includes(r.toLowerCase())&&l.push("samesite="+r),document.cookie=e+"="+escape(t)+"; "+l.join("; ")}}),Ext.onReady(function(){Ext.override(Ext.Msg,{alert:function(e,t,i,a){if(Ext.isString(e)){let n={title:e,message:t,icon:this.ERROR,buttons:this.OK,fn:i,scope:a,minWidth:this.minWidth};return this.show(n)}}})}),Ext.define("Ext.ux.IFrame",{extend:"Ext.Component",alias:"widget.uxiframe",loadMask:"Loading...",src:"about:blank",renderTpl:['<iframe src="{src}" id="{id}-iframeEl" data-ref="iframeEl" name="{frameName}" width="100%" height="100%" frameborder="0" allowfullscreen="true"></iframe>'],childEls:["iframeEl"],initComponent:function(){this.callParent(),this.frameName=this.frameName||this.id+"-frame"},initEvents:function(){let e=this;e.callParent(),e.iframeEl.on("load",e.onLoad,e)},initRenderData:function(){return Ext.apply(this.callParent(),{src:this.src,frameName:this.frameName})},getBody:function(){let e=this.getDoc();return e.body||e.documentElement},getDoc:function(){try{return this.getWin().document}catch(e){return null}},getWin:function(){let e=this.frameName;return Ext.isIE?this.iframeEl.dom.contentWindow:window.frames[e]},getFrame:function(){return this.iframeEl.dom},onLoad:function(){this.getDoc()?(this.el.unmask(),this.fireEvent("load",this)):this.src&&(this.el.unmask(),this.fireEvent("error",this))},load:function(e){let t=this,i=t.loadMask,a=t.getFrame();!1!==t.fireEvent("beforeload",t,e)&&(i&&t.el&&t.el.mask(i),a.src=t.src=e||t.src)}}),Ext.define("PMX.image.Logo",{extend:"Ext.Img",xtype:"proxmoxlogo",height:30,width:172,src:"/images/proxmox_logo.png",alt:"Proxmox",autoEl:{tag:"a",href:"https://www.proxmox.com",target:"_blank"},initComponent:function(){let e=this,t=void 0!==e.prefix?e.prefix:"/pve2";e.src=t+e.src,e.callParent()}}),Ext.define("Proxmox.Markdown",{alternateClassName:"Px.Markdown",singleton:!0,sanitizeHTML:function(e){if(!e)return e;let t,i=e=>e.match(/^\s*https?:/i);t=e=>{if(3!==e.nodeType)if(1!==e.nodeType||/^(script|style|form|select|option|optgroup|map|area|canvas|textarea|applet|font|iframe|audio|video|object|embed|svg)$/i.test(e.tagName))e.outerHTML=Ext.String.htmlEncode(e.outerHTML);else{for(let t=e.attributes.length;t--;){const a=e.attributes[t].name,n=e.attributes[t].value,o=e.tagName.toLowerCase();if(/^(class|id|name|href|src|alt|align|valign|disabled|checked|start|type|target)$/i.test(a))if("href"!==a&&"src"!==a||i(n))"target"===a&&"a"!==o&&e.attributes.removeNamedItem(a);else{let r=!1;try{let l=new URL(n,window.location.origin);r=i(l.protocol),"img"===o&&"data:"===l.protocol.toLowerCase()?r=!0:"a"===o&&(r="javascript:"!==l.protocol.toLowerCase()),r?e.attributes[t].value=l.href:e.attributes.removeNamedItem(a)}catch(t){e.attributes.removeNamedItem(a)}}else e.attributes.removeNamedItem(a)}for(let i=e.childNodes.length;i--;)t(e.childNodes[i])}};const a=(new DOMParser).parseFromString(`<!DOCTYPE html><html><body>${e}`,"text/html");return a.normalize(),t(a.body),a.body.innerHTML},parse:function(e){let t=marked.parse(e);return`<div class="pmx-md">${this.sanitizeHTML(t)}</div>`}}),Ext.define("Proxmox.Mixin.CBind",{extend:"Ext.Mixin",mixinConfig:{before:{initComponent:"cloneTemplates"}},cloneTemplates:function(){let e=this;"function"==typeof e.cbindData&&(e.cbindData=e.cbindData(e.initialConfig)),e.cbindData=e.cbindData||{};let t,i=function(t){if(t in e.initialConfig)return e.initialConfig[t];if(t in e.cbindData){let i=e.cbindData[t];return"function"==typeof i?i(e.initialConfig):i}if(t in e)return e[t];throw"unable to get cbind data for '"+t+"'"},a=function(e){let t,a=e.cbind;if(a)for(const n in a){let o,r;if(t=a[n],r=!1,"function"==typeof t)e[n]=t(i,n),r=!0;else if(o=/^\{(!)?([a-z_][a-z0-9_]*)\}$/i.exec(t)){let t=i(o[2]);o[1]&&(t=!t),e[n]=t,r=!0}else if(o=/^\{(!)?([a-z_][a-z0-9_]*(\.[a-z_][a-z0-9_]*)+)\}$/i.exec(t)){let t=o[2].split("."),a=i(t.shift());t.forEach(function(e){if(!(e in a))throw"unable to get cbind data for '"+o[2]+"'";a=a[e]}),o[1]&&(a=!a),e[n]=a,r=!0}else e[n]=t.replace(/{([a-z_][a-z0-9_]*)\}/gi,(e,t)=>{let a=i(t);return r=!0,a});if(!r)throw"unable to parse cbind template '"+t+"'"}};e.cbind&&a(e);let n=function(e){let i,o,r,l,s,d;for(d=e.length,r=!1,o=0;o<d;o++)if(l=e[o],l.constructor===Object&&(l.xtype||l.cbind)){r=!0;break}if(!r)return e;for(i=[],o=0;o<d;o++)l=e[o],l.constructor===Object&&(l.xtype||l.cbind)?(s=t(l),s.cbind&&a(s),i.push(s)):l.constructor===Array?(s=n(l),i.push(s)):i.push(l);return i};t=function(e){let i,o,r,l={};for(i in e)o=e[i],null!=o?o.constructor===Object&&(o.xtype||o.cbind)?(r=t(o),r.cbind&&a(r),l[i]=r):o.constructor===Array?(r=n(o),l[i]=r):l[i]=o:l[i]=o;return l};!function(){let i,a,o;for(i in e)a=e[i],null!=a&&("object"==typeof a&&a.constructor===Object?(a.xtype||a.cbind)&&"config"!==i&&(e[i]=t(a)):a.constructor===Array&&(o=n(a),e[i]=o))}()}}),Ext.define("Proxmox.data.reader.JsonObject",{extend:"Ext.data.reader.Json",alias:"reader.jsonobject",readArray:!1,responseType:void 0,rows:void 0,constructor:function(e){Ext.apply(this,e||{}),this.callParent([e])},getResponseData:function(e){let t=this,i=[];try{let a=Ext.decode(e.responseText)[t.getRootProperty()];if(t.readArray){null===a&&(a=[]);let e={};Ext.Array.each(a,function(t){Ext.isDefined(t.key)&&(e[t.key]=t)}),t.rows?Ext.Object.each(t.rows,function(t,a){let n=e[t];Ext.isDefined(n)?(Ext.isDefined(n.value)||(n.value=a.defaultValue),i.push(n)):Ext.isDefined(a.defaultValue)?i.push({key:t,value:a.defaultValue}):a.required&&i.push({key:t,value:void 0})}):Ext.Array.each(a,function(e){Ext.isDefined(e.key)&&i.push(e)})}else null===a?a={}:Ext.isArray(a)&&(a=1===a.length?a[0]:{}),t.rows?Ext.Object.each(t.rows,function(e,t){Ext.isDefined(a[e])?i.push({key:e,value:a[e]}):Ext.isDefined(t.defaultValue)?i.push({key:e,value:t.defaultValue}):t.required&&i.push({key:e,value:void 0})}):Ext.Object.each(a,function(e,t){i.push({key:e,value:t})})}catch(t){Ext.Error.raise({response:e,json:e.responseText,parseError:t,msg:"Unable to parse the JSON returned by the server: "+t.toString()})}return i}}),Ext.define("Proxmox.RestProxy",{extend:"Ext.data.RestProxy",alias:"proxy.proxmox",pageParam:null,startParam:null,limitParam:null,groupParam:null,sortParam:null,filterParam:null,noCache:!1,afterRequest:function(e,t){this.fireEvent("afterload",this,e,t)},constructor:function(e){Ext.applyIf(e,{reader:{responseType:void 0,type:"json",rootProperty:e.root||"data"}}),this.callParent([e])}},function(){Ext.define("KeyValue",{extend:"Ext.data.Model",fields:["key","value"],idProperty:"key"}),Ext.define("KeyValuePendingDelete",{extend:"Ext.data.Model",fields:["key","value","pending","delete"],idProperty:"key"}),Ext.define("proxmox-tasks",{extend:"Ext.data.Model",fields:[{name:"starttime",type:"date",dateFormat:"timestamp"},{name:"endtime",type:"date",dateFormat:"timestamp"},{name:"pid",type:"int"},{name:"duration",sortType:"asInt",calculate:function(e){let t=e.endtime,i=e.starttime;return void 0!==t?(t-i)/1e3:0}},"node","upid","user","tokenid","status","type","id"],idProperty:"upid"}),Ext.define("proxmox-cluster-log",{extend:"Ext.data.Model",fields:[{name:"uid",type:"int"},{name:"time",type:"date",dateFormat:"timestamp"},{name:"pri",type:"int"},{name:"pid",type:"int"},"node","user","tag","msg",{name:"id",convert:function(e,t){let i=t.data;return e||i.uid+":"+i.node}}],idProperty:"id"})}),Ext.define("Proxmox.data.UpdateStore",{extend:"Ext.data.Store",alias:"store.update",config:{interval:3e3,isStopped:!0,autoStart:!1},destroy:function(){this.stopUpdate(),this.callParent()},constructor:function(e){let t=this;void 0===(e=e||{}).interval&&delete e.interval;let i=new Ext.util.DelayedTask,a=function(){if(!t.getIsStopped())if(Proxmox.Utils.authOK()){let e=new Date;t.load(function(){let n=new Date-e,o=t.getInterval()+2*n;i.delay(o,a)})}else i.delay(200,a)};Ext.apply(e,{startUpdate:function(){t.setIsStopped(!1),i.delay(1,a)},stopUpdate:function(){t.setIsStopped(!0),i.cancel()}}),t.callParent([e]),t.load_task=i,t.getAutoStart()&&t.startUpdate()}}),Ext.define("Proxmox.data.DiffStore",{extend:"Ext.data.Store",alias:"store.diff",sortAfterUpdate:!1,autoDestroyRstore:!1,doDestroy:function(){let e=this;e.autoDestroyRstore&&(Ext.isFunction(e.rstore.destroy)&&e.rstore.destroy(),delete e.rstore),e.callParent()},constructor:function(e){let t,i=this;if(!(e=e||{}).rstore)throw"no rstore specified";if(!e.rstore.model)throw"no rstore model specified";if(e.rstore.isInstance)t=e.rstore;else{if(!e.rstore.type)throw'rstore is not an instance, and cannot autocreate without "type"';Ext.applyIf(e.rstore,{autoDestroyRstore:!0}),t=Ext.create(`store.${e.rstore.type}`,e.rstore)}Ext.apply(e,{model:t.model,proxy:{type:"memory"}}),i.callParent([e]),i.rstore=t;let a=!0,n=function(e,t,n){if(!n)return;i.suspendEvents(),(i.getData().getSource()||i.getData()).each(function(e){i.rstore.getById(e.getId())||i.remove(e)}),i.rstore.each(function(e){!function(e,t){let n=i.getById(t);if(n)n.beginEdit(),Ext.Array.each(i.model.prototype.fields,function(t){n.data[t.name]!==e[t.name]&&n.set(t.name,e[t.name])}),n.endEdit(!0),n.commit();else{let t=Ext.create(i.model,e),n=i.appendAtStart&&!a?0:i.data.length;i.insert(n,t)}}(e.data,e.getId())}),i.filter(),i.sortAfterUpdate&&i.sort(),a=!1,i.resumeEvents(),i.fireEvent("refresh",i),i.fireEvent("datachanged",i)};i.rstore.isLoaded()&&n(i.rstore,0,!0),i.mon(i.rstore,"load",n)}}),Ext.define("Proxmox.data.ObjectStore",{extend:"Proxmox.data.UpdateStore",getRecord:function(){let e=Ext.create("Ext.data.Model");return this.getData().each(function(t){e.set(t.data.key,t.data.value)}),e.commit(!0),e},constructor:function(e){e=e||{},Ext.applyIf(e,{model:"KeyValue",proxy:{type:"proxmox",url:e.url,extraParams:e.extraParams,reader:{type:"jsonobject",rows:e.rows,readArray:e.readArray,rootProperty:e.root||"data"}}}),this.callParent([e])}}),Ext.define("Proxmox.data.RRDStore",{extend:"Proxmox.data.UpdateStore",alias:"store.proxmoxRRDStore",setRRDUrl:function(e,t){let i=this;e||(e=i.timeframe),t||(t=i.cf),i.proxy.url=i.rrdurl+"?timeframe="+e+"&cf="+t},proxy:{type:"proxmox"},timeframe:"hour",cf:"AVERAGE",constructor:function(e){let t=this;if((e=e||{}).interval||(e.interval=3e4),!e.rrdurl)throw"no rrdurl specified";let i="proxmoxRRDTypeSelection",a=Ext.state.Manager.getProvider(),n=a.get(i);n&&(n.timeframe===t.timeframe&&n.cf===t.rrdcffn||(t.timeframe=n.timeframe,t.rrdcffn=n.cf)),t.callParent([e]),t.setRRDUrl(),t.mon(a,"statechange",function(e,a,n){a===i&&n&&n.id&&(n.timeframe===t.timeframe&&n.cf===t.cf||(t.timeframe=n.timeframe,t.cf=n.cf,t.setRRDUrl(),t.reload()))})}}),Ext.define("Timezone",{extend:"Ext.data.Model",fields:["zone"]}),Ext.define("Proxmox.data.TimezoneStore",{extend:"Ext.data.Store",model:"Timezone",data:[["Africa/Abidjan"],["Africa/Accra"],["Africa/Addis_Ababa"],["Africa/Algiers"],["Africa/Asmara"],["Africa/Bamako"],["Africa/Bangui"],["Africa/Banjul"],["Africa/Bissau"],["Africa/Blantyre"],["Africa/Brazzaville"],["Africa/Bujumbura"],["Africa/Cairo"],["Africa/Casablanca"],["Africa/Ceuta"],["Africa/Conakry"],["Africa/Dakar"],["Africa/Dar_es_Salaam"],["Africa/Djibouti"],["Africa/Douala"],["Africa/El_Aaiun"],["Africa/Freetown"],["Africa/Gaborone"],["Africa/Harare"],["Africa/Johannesburg"],["Africa/Kampala"],["Africa/Khartoum"],["Africa/Kigali"],["Africa/Kinshasa"],["Africa/Lagos"],["Africa/Libreville"],["Africa/Lome"],["Africa/Luanda"],["Africa/Lubumbashi"],["Africa/Lusaka"],["Africa/Malabo"],["Africa/Maputo"],["Africa/Maseru"],["Africa/Mbabane"],["Africa/Mogadishu"],["Africa/Monrovia"],["Africa/Nairobi"],["Africa/Ndjamena"],["Africa/Niamey"],["Africa/Nouakchott"],["Africa/Ouagadougou"],["Africa/Porto-Novo"],["Africa/Sao_Tome"],["Africa/Tripoli"],["Africa/Tunis"],["Africa/Windhoek"],["America/Adak"],["America/Anchorage"],["America/Anguilla"],["America/Antigua"],["America/Araguaina"],["America/Argentina/Buenos_Aires"],["America/Argentina/Catamarca"],["America/Argentina/Cordoba"],["America/Argentina/Jujuy"],["America/Argentina/La_Rioja"],["America/Argentina/Mendoza"],["America/Argentina/Rio_Gallegos"],["America/Argentina/Salta"],["America/Argentina/San_Juan"],["America/Argentina/San_Luis"],["America/Argentina/Tucuman"],["America/Argentina/Ushuaia"],["America/Aruba"],["America/Asuncion"],["America/Atikokan"],["America/Bahia"],["America/Bahia_Banderas"],["America/Barbados"],["America/Belem"],["America/Belize"],["America/Blanc-Sablon"],["America/Boa_Vista"],["America/Bogota"],["America/Boise"],["America/Cambridge_Bay"],["America/Campo_Grande"],["America/Cancun"],["America/Caracas"],["America/Cayenne"],["America/Cayman"],["America/Chicago"],["America/Chihuahua"],["America/Costa_Rica"],["America/Cuiaba"],["America/Curacao"],["America/Danmarkshavn"],["America/Dawson"],["America/Dawson_Creek"],["America/Denver"],["America/Detroit"],["America/Dominica"],["America/Edmonton"],["America/Eirunepe"],["America/El_Salvador"],["America/Fortaleza"],["America/Glace_Bay"],["America/Godthab"],["America/Goose_Bay"],["America/Grand_Turk"],["America/Grenada"],["America/Guadeloupe"],["America/Guatemala"],["America/Guayaquil"],["America/Guyana"],["America/Halifax"],["America/Havana"],["America/Hermosillo"],["America/Indiana/Indianapolis"],["America/Indiana/Knox"],["America/Indiana/Marengo"],["America/Indiana/Petersburg"],["America/Indiana/Tell_City"],["America/Indiana/Vevay"],["America/Indiana/Vincennes"],["America/Indiana/Winamac"],["America/Inuvik"],["America/Iqaluit"],["America/Jamaica"],["America/Juneau"],["America/Kentucky/Louisville"],["America/Kentucky/Monticello"],["America/La_Paz"],["America/Lima"],["America/Los_Angeles"],["America/Maceio"],["America/Managua"],["America/Manaus"],["America/Marigot"],["America/Martinique"],["America/Matamoros"],["America/Mazatlan"],["America/Menominee"],["America/Merida"],["America/Mexico_City"],["America/Miquelon"],["America/Moncton"],["America/Monterrey"],["America/Montevideo"],["America/Montreal"],["America/Montserrat"],["America/Nassau"],["America/New_York"],["America/Nipigon"],["America/Nome"],["America/Noronha"],["America/North_Dakota/Center"],["America/North_Dakota/New_Salem"],["America/Ojinaga"],["America/Panama"],["America/Pangnirtung"],["America/Paramaribo"],["America/Phoenix"],["America/Port-au-Prince"],["America/Port_of_Spain"],["America/Porto_Velho"],["America/Puerto_Rico"],["America/Rainy_River"],["America/Rankin_Inlet"],["America/Recife"],["America/Regina"],["America/Resolute"],["America/Rio_Branco"],["America/Santa_Isabel"],["America/Santarem"],["America/Santiago"],["America/Santo_Domingo"],["America/Sao_Paulo"],["America/Scoresbysund"],["America/Shiprock"],["America/St_Barthelemy"],["America/St_Johns"],["America/St_Kitts"],["America/St_Lucia"],["America/St_Thomas"],["America/St_Vincent"],["America/Swift_Current"],["America/Tegucigalpa"],["America/Thule"],["America/Thunder_Bay"],["America/Tijuana"],["America/Toronto"],["America/Tortola"],["America/Vancouver"],["America/Whitehorse"],["America/Winnipeg"],["America/Yakutat"],["America/Yellowknife"],["Antarctica/Casey"],["Antarctica/Davis"],["Antarctica/DumontDUrville"],["Antarctica/Macquarie"],["Antarctica/Mawson"],["Antarctica/McMurdo"],["Antarctica/Palmer"],["Antarctica/Rothera"],["Antarctica/South_Pole"],["Antarctica/Syowa"],["Antarctica/Vostok"],["Arctic/Longyearbyen"],["Asia/Aden"],["Asia/Almaty"],["Asia/Amman"],["Asia/Anadyr"],["Asia/Aqtau"],["Asia/Aqtobe"],["Asia/Ashgabat"],["Asia/Baghdad"],["Asia/Bahrain"],["Asia/Baku"],["Asia/Bangkok"],["Asia/Beirut"],["Asia/Bishkek"],["Asia/Brunei"],["Asia/Choibalsan"],["Asia/Chongqing"],["Asia/Colombo"],["Asia/Damascus"],["Asia/Dhaka"],["Asia/Dili"],["Asia/Dubai"],["Asia/Dushanbe"],["Asia/Gaza"],["Asia/Harbin"],["Asia/Ho_Chi_Minh"],["Asia/Hong_Kong"],["Asia/Hovd"],["Asia/Irkutsk"],["Asia/Jakarta"],["Asia/Jayapura"],["Asia/Jerusalem"],["Asia/Kabul"],["Asia/Kamchatka"],["Asia/Karachi"],["Asia/Kashgar"],["Asia/Kathmandu"],["Asia/Kolkata"],["Asia/Krasnoyarsk"],["Asia/Kuala_Lumpur"],["Asia/Kuching"],["Asia/Kuwait"],["Asia/Macau"],["Asia/Magadan"],["Asia/Makassar"],["Asia/Manila"],["Asia/Muscat"],["Asia/Nicosia"],["Asia/Novokuznetsk"],["Asia/Novosibirsk"],["Asia/Omsk"],["Asia/Oral"],["Asia/Phnom_Penh"],["Asia/Pontianak"],["Asia/Pyongyang"],["Asia/Qatar"],["Asia/Qyzylorda"],["Asia/Rangoon"],["Asia/Riyadh"],["Asia/Sakhalin"],["Asia/Samarkand"],["Asia/Seoul"],["Asia/Shanghai"],["Asia/Singapore"],["Asia/Taipei"],["Asia/Tashkent"],["Asia/Tbilisi"],["Asia/Tehran"],["Asia/Thimphu"],["Asia/Tokyo"],["Asia/Ulaanbaatar"],["Asia/Urumqi"],["Asia/Vientiane"],["Asia/Vladivostok"],["Asia/Yakutsk"],["Asia/Yekaterinburg"],["Asia/Yerevan"],["Atlantic/Azores"],["Atlantic/Bermuda"],["Atlantic/Canary"],["Atlantic/Cape_Verde"],["Atlantic/Faroe"],["Atlantic/Madeira"],["Atlantic/Reykjavik"],["Atlantic/South_Georgia"],["Atlantic/St_Helena"],["Atlantic/Stanley"],["Australia/Adelaide"],["Australia/Brisbane"],["Australia/Broken_Hill"],["Australia/Currie"],["Australia/Darwin"],["Australia/Eucla"],["Australia/Hobart"],["Australia/Lindeman"],["Australia/Lord_Howe"],["Australia/Melbourne"],["Australia/Perth"],["Australia/Sydney"],["Europe/Amsterdam"],["Europe/Andorra"],["Europe/Athens"],["Europe/Belgrade"],["Europe/Berlin"],["Europe/Bratislava"],["Europe/Brussels"],["Europe/Bucharest"],["Europe/Budapest"],["Europe/Chisinau"],["Europe/Copenhagen"],["Europe/Dublin"],["Europe/Gibraltar"],["Europe/Guernsey"],["Europe/Helsinki"],["Europe/Isle_of_Man"],["Europe/Istanbul"],["Europe/Jersey"],["Europe/Kaliningrad"],["Europe/Kiev"],["Europe/Lisbon"],["Europe/Ljubljana"],["Europe/London"],["Europe/Luxembourg"],["Europe/Madrid"],["Europe/Malta"],["Europe/Mariehamn"],["Europe/Minsk"],["Europe/Monaco"],["Europe/Moscow"],["Europe/Oslo"],["Europe/Paris"],["Europe/Podgorica"],["Europe/Prague"],["Europe/Riga"],["Europe/Rome"],["Europe/Samara"],["Europe/San_Marino"],["Europe/Sarajevo"],["Europe/Simferopol"],["Europe/Skopje"],["Europe/Sofia"],["Europe/Stockholm"],["Europe/Tallinn"],["Europe/Tirane"],["Europe/Uzhgorod"],["Europe/Vaduz"],["Europe/Vatican"],["Europe/Vienna"],["Europe/Vilnius"],["Europe/Volgograd"],["Europe/Warsaw"],["Europe/Zagreb"],["Europe/Zaporozhye"],["Europe/Zurich"],["Indian/Antananarivo"],["Indian/Chagos"],["Indian/Christmas"],["Indian/Cocos"],["Indian/Comoro"],["Indian/Kerguelen"],["Indian/Mahe"],["Indian/Maldives"],["Indian/Mauritius"],["Indian/Mayotte"],["Indian/Reunion"],["Pacific/Apia"],["Pacific/Auckland"],["Pacific/Chatham"],["Pacific/Chuuk"],["Pacific/Easter"],["Pacific/Efate"],["Pacific/Enderbury"],["Pacific/Fakaofo"],["Pacific/Fiji"],["Pacific/Funafuti"],["Pacific/Galapagos"],["Pacific/Gambier"],["Pacific/Guadalcanal"],["Pacific/Guam"],["Pacific/Honolulu"],["Pacific/Johnston"],["Pacific/Kiritimati"],["Pacific/Kosrae"],["Pacific/Kwajalein"],["Pacific/Majuro"],["Pacific/Marquesas"],["Pacific/Midway"],["Pacific/Nauru"],["Pacific/Niue"],["Pacific/Norfolk"],["Pacific/Noumea"],["Pacific/Pago_Pago"],["Pacific/Palau"],["Pacific/Pitcairn"],["Pacific/Pohnpei"],["Pacific/Port_Moresby"],["Pacific/Rarotonga"],["Pacific/Saipan"],["Pacific/Tahiti"],["Pacific/Tarawa"],["Pacific/Tongatapu"],["Pacific/Wake"],["Pacific/Wallis"],["UTC"]]}),Ext.define("proxmox-notification-endpoints",{extend:"Ext.data.Model",fields:["name","type","comment","disable","origin"],proxy:{type:"proxmox"},idProperty:"name"}),Ext.define("proxmox-notification-matchers",{extend:"Ext.data.Model",fields:["name","comment","disable","origin"],proxy:{type:"proxmox"},idProperty:"name"}),Ext.define("proxmox-notification-fields",{extend:"Ext.data.Model",fields:["name","description"],idProperty:"name"}),Ext.define("proxmox-notification-field-values",{extend:"Ext.data.Model",fields:["value","comment","field"],idProperty:"value"}),Ext.define("pmx-domains",{extend:"Ext.data.Model",fields:["realm","type","comment","default",{name:"tfa",allowNull:!0},{name:"descr",convert:function(e,{data:t={}}){if(e)return Ext.String.htmlEncode(e);let i=t.comment||t.realm;return t.tfa&&(i+=` (+ ${t.tfa})`),Ext.String.htmlEncode(i)}}],idProperty:"realm",proxy:{type:"proxmox",url:"/api2/json/access/domains"}}),Ext.define("proxmox-certificate",{extend:"Ext.data.Model",fields:["filename","fingerprint","issuer","notafter","notbefore","subject","san","public-key-bits","public-key-type"],idProperty:"filename"}),Ext.define("proxmox-acme-accounts",{extend:"Ext.data.Model",fields:["name"],proxy:{type:"proxmox"},idProperty:"name"}),Ext.define("proxmox-acme-challenges",{extend:"Ext.data.Model",fields:["id","type","schema"],proxy:{type:"proxmox"},idProperty:"id"}),Ext.define("proxmox-acme-plugins",{extend:"Ext.data.Model",fields:["type","plugin","api"],proxy:{type:"proxmox"},idProperty:"plugin"}),Ext.define("Proxmox.form.SizeField",{extend:"Ext.form.FieldContainer",alias:"widget.pmxSizeField",mixins:["Proxmox.Mixin.CBind"],viewModel:{data:{unit:"MiB",unitPostfix:""},formulas:{unitlabel:e=>e("unit")+e("unitPostfix")}},emptyText:"",layout:"hbox",defaults:{hideLabel:!0},unit:"MiB",unitPostfix:"",backendUnit:void 0,submitAutoScaledSizeUnit:!1,allowZero:!1,emptyValue:null,items:[{xtype:"numberfield",cbind:{name:"{name}",emptyText:"{emptyText}",allowZero:"{allowZero}",emptyValue:"{emptyValue}"},minValue:0,step:1,submitLocaleSeparator:!1,fieldStyle:"text-align: right",flex:1,enableKeyEvents:!0,setValue:function(e){if(!this._transformed){let t=this.up("fieldcontainer"),i=t.getViewModel().get("unit");"string"==typeof e&&(e=Proxmox.Utils.size_unit_to_bytes(e)),e/=Proxmox.Utils.SizeUnits[i],e*=t.backendFactor,this._transformed=!0}return 0!==Number(e)||this.allowZero||(e=void 0),Ext.form.field.Text.prototype.setValue.call(this,e)},getSubmitValue:function(){let e=this.processRawValue(this.getRawValue());if(e=e.replace(this.decimalSeparator,"."),void 0===e||""===e)return this.emptyValue;if(0===Number(e))return this.allowZero?0:null;let t=this.up("fieldcontainer"),i=t.getViewModel().get("unit");return e=parseFloat(e)*Proxmox.Utils.SizeUnits[i],t.submitAutoScaledSizeUnit?Proxmox.Utils.format_size(e,!i.endsWith("iB")):String(Math.floor(e/t.backendFactor))},listeners:{keydown:function(){this._transformed=!0}}},{xtype:"displayfield",name:"unit",submitValue:!1,padding:"0 0 0 10",bind:{value:"{unitlabel}"},listeners:{change:(e,t)=>{e.originalValue=t}},width:40}],initComponent:function(){let e=this;if(e.unit=e.unit||"MiB",!(e.unit in Proxmox.Utils.SizeUnits))throw"unknown unit: "+e.unit;if(e.backendFactor=1,void 0!==e.backendUnit){if(!(e.unit in Proxmox.Utils.SizeUnits))throw"unknown backend unit: "+e.backendUnit;e.backendFactor=Proxmox.Utils.SizeUnits[e.backendUnit]}e.callParent(arguments),e.getViewModel().set("unit",e.unit),e.getViewModel().set("unitPostfix",e.unitPostfix)}}),Ext.define("Proxmox.form.BandwidthField",{extend:"Proxmox.form.SizeField",alias:"widget.pmxBandwidthField",unitPostfix:"/s"}),Ext.define("Proxmox.form.field.DisplayEdit",{extend:"Ext.form.FieldContainer",alias:"widget.pmxDisplayEditField",viewModel:{parent:null,data:{editable:!1,value:void 0}},displayType:"displayfield",editConfig:{},editable:!1,setEditable:function(e){let t=this.getViewModel();this.editable=e,t.set("editable",e)},getEditable:function(){return this.getViewModel().get("editable")},setValue:function(e){let t=this.getViewModel();this.value=e,t.set("value",e)},getValue:function(){this.getViewModel().get("value")},setEmptyText:function(e){this.editField.setEmptyText(e)},getEmptyText:function(){return this.editField.getEmptyText()},layout:"fit",defaults:{hideLabel:!0},initComponent:function(){let e=this,t={xtype:e.displayType,bind:{}};Ext.applyIf(t,e.initialConfig),delete t.editConfig,delete t.editable;let i=Ext.apply({},e.editConfig);Ext.applyIf(i,{xtype:"textfield",bind:{}}),Ext.applyIf(i,t),e.initialConfig&&e.initialConfig.displayConfig&&(Ext.applyIf(t,e.initialConfig.displayConfig),delete t.displayConfig),Ext.applyIf(t,{renderer:e=>Ext.htmlEncode(e)}),Ext.applyIf(t.bind,{hidden:"{editable}",disabled:"{editable}",value:"{value}"}),Ext.applyIf(i.bind,{hidden:"{!editable}",disabled:"{!editable}",value:"{value}"}),i.disabled=i.hidden=!e.editable,t.disabled=t.hidden=!!e.editable,i.name=t.name=e.name,Ext.apply(e,{items:[i,t]}),e.callParent(),e.editField=e.down(i.xtype),e.displayField=e.down(t.xtype),e.getViewModel().set("editable",e.editable)}}),Ext.define("Proxmox.form.field.ExpireDate",{extend:"Ext.form.field.Date",alias:["widget.pmxExpireDate"],name:"expire",fieldLabel:gettext("Expire"),emptyText:"never",format:"Y-m-d",submitFormat:"U",getSubmitValue:function(){let e=this.callParent();return e||(e=0),e},setValue:function(e){Ext.isDefined(e)&&(e?Ext.isDate(e)||(e=new Date(1e3*e)):e=null),this.callParent([e])}}),Ext.define("Proxmox.form.field.Integer",{extend:"Ext.form.field.Number",alias:"widget.proxmoxintegerfield",config:{deleteEmpty:!1},allowDecimals:!1,allowExponential:!1,step:1,getSubmitData:function(){let e=this,t=null;if(!e.disabled&&e.submitValue&&!e.isFileUpload()){let i=e.getSubmitValue();null!=i&&""!==i?(t={},t[e.getName()]=i):e.getDeleteEmpty()&&(t={},t.delete=e.getName())}return t}}),Ext.define("Proxmox.form.field.Textfield",{extend:"Ext.form.field.Text",alias:["widget.proxmoxtextfield"],config:{skipEmptyText:!0,deleteEmpty:!1,trimValue:!1},getSubmitData:function(){let e,t=this,i=null;return t.disabled||!t.submitValue||t.isFileUpload()||(e=t.getSubmitValue(),null!==e?(i={},i[t.getName()]=e):t.getDeleteEmpty()&&(i={},i.delete=t.getName())),i},getSubmitValue:function(){let e=this.processRawValue(this.getRawValue());return this.getTrimValue()&&"string"==typeof e&&(e=e.trim()),""!==e?e:this.getSkipEmptyText()?null:e},setAllowBlank:function(e){this.allowBlank=e,this.validate()}}),Ext.define("Proxmox.form.field.Base64TextArea",{extend:"Ext.form.field.TextArea",alias:["widget.proxmoxBase64TextArea"],config:{skipEmptyText:!1,deleteEmpty:!1,trimValue:!1,editable:!0,width:600,height:400,scrollable:"y",emptyText:gettext("You can use Markdown for rich text formatting.")},setValue:function(e){this.callParent([Proxmox.Utils.base64ToUtf8(e)])},processRawValue:function(e){return Proxmox.Utils.utf8ToBase64(e)},getSubmitData:function(){let e,t=this,i=null;return t.disabled||!t.submitValue||t.isFileUpload()||(e=t.getSubmitValue(),null!==e?(i={},i[t.getName()]=e):t.getDeleteEmpty()&&(i={},i.delete=t.getName())),i},getSubmitValue:function(){let e=this.processRawValue(this.getRawValue());return this.getTrimValue()&&"string"==typeof e&&(e=e.trim()),""!==e?e:this.getSkipEmptyText()?null:e},setAllowBlank:function(e){this.allowBlank=e,this.validate()}}),Ext.define("Proxmox.form.field.VlanField",{extend:"Ext.form.field.Number",alias:["widget.proxmoxvlanfield"],deleteEmpty:!1,emptyText:gettext("no VLAN"),fieldLabel:gettext("VLAN Tag"),allowBlank:!0,getSubmitData:function(){var e,t=this,i=null;return!t.disabled&&t.submitValue&&((e=t.getSubmitValue())?(i={})[t.getName()]=e:t.deleteEmpty&&((i={}).delete=t.getName())),i},initComponent:function(){Ext.apply(this,{minValue:1,maxValue:4094}),this.callParent()}}),Ext.define("Proxmox.DateTimeField",{extend:"Ext.form.FieldContainer",alias:["widget.promxoxDateTimeField"],xtype:"proxmoxDateTimeField",layout:"hbox",viewModel:{data:{datetime:null,minDatetime:null,maxDatetime:null},formulas:{date:{get:function(e){return e("datetime")},set:function(e){if(!e)return void this.set("datetime",null);let t=new Date(this.get("datetime"));t.setFullYear(e.getFullYear(),e.getMonth(),e.getDate()),this.set("datetime",t)}},time:{get:function(e){return e("datetime")},set:function(e){if(!e)return void this.set("datetime",null);let t=new Date(this.get("datetime"));t.setHours(e.getHours()),t.setMinutes(e.getMinutes()),t.setSeconds(e.getSeconds()),t.setMilliseconds(e.getMilliseconds()),this.set("datetime",t)}},minDate:{get:function(e){let t=e("minDatetime");return t?new Date(t):null}},maxDate:{get:function(e){let t=e("maxDatetime");return t?new Date(t):null}},minTime:{get:function(e){let t=e("datetime"),i=e("minDatetime");return i&&t&&!this.isSameDay(t,i)?new Date(i).setHours("00","00","00","000"):i}},maxTime:{get:function(e){let t=e("datetime"),i=e("maxDatetime");return i&&t&&!this.isSameDay(t,i)?new Date(i).setHours("23","59","59","999"):i}}},isSameDay:function(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}},config:{value:null,submitFormat:"U",disabled:!1},setValue:function(e){this.getViewModel().set("datetime",e)},getValue:function(){return this.getViewModel().get("datetime")},getSubmitValue:function(){let e=this.getValue();return e?Ext.Date.format(e,this.submitFormat):null},setMinValue:function(e){this.getViewModel().set("minDatetime",e)},getMinValue:function(){return this.getViewModel().get("minDatetime")},setMaxValue:function(e){this.getViewModel().set("maxDatetime",e)},getMaxValue:function(){return this.getViewModel().get("maxDatetime")},initComponent:function(){let e=this;e.callParent();let t=e.getViewModel();t.set("datetime",e.config.value),t.bind("{datetime}",function(t){e.publishState("value",t),e.fireEvent("change",t)})},items:[{xtype:"datefield",editable:!1,flex:1,format:"Y-m-d",bind:{value:"{date}",minValue:"{minDate}",maxValue:"{maxDate}"}},{xtype:"timefield",format:"H:i",width:80,value:"00:00",increment:60,bind:{value:"{time}",minValue:"{minTime}",maxValue:"{maxTime}"}}]}),Ext.define("Proxmox.form.Checkbox",{extend:"Ext.form.field.Checkbox",alias:["widget.proxmoxcheckbox"],config:{defaultValue:void 0,deleteDefaultValue:!1,deleteEmpty:!1,clearOnDisable:!1},inputValue:"1",getSubmitData:function(){let e,t=this,i=null;return!t.disabled&&t.submitValue&&(e=t.getSubmitValue(),null!==e?(i={},e===t.getDefaultValue()&&t.getDeleteDefaultValue()?i.delete=t.getName():i[t.getName()]=e):t.getDeleteEmpty()&&(i={},i.delete=t.getName())),i},setDisabled:function(e){let t=this,i=t.clearOnDisable&&!t.disabled&&e;t.callParent(arguments),i&&t.setValue(!1)},setRawValue:function(e){let t=this;1===e?t.callParent([!0]):t.callParent([e])}}),Ext.define("Proxmox.form.KVComboBox",{extend:"Ext.form.field.ComboBox",alias:"widget.proxmoxKVComboBox",config:{deleteEmpty:!0},comboItems:void 0,displayField:"value",valueField:"key",queryMode:"local",getSubmitData:function(){let e,t=this,i=null;return!t.disabled&&t.submitValue&&(e=t.getSubmitValue(),null!==e&&""!==e&&"__default__"!==e?(i={},i[t.getName()]=e):t.getDeleteEmpty()&&(i={},i.delete=t.getName())),i},validator:function(e){let t=this;if(t.editable||null===e||""===e)return!0;if(t.store.getCount()>0){let i=t.multiSelect?e.split(t.delimiter):[e],a=t.store.getData().collect("value","data");if(Ext.Array.every(i,function(e){return Ext.Array.contains(a,e)}))return!0}return"value '"+e+"' not allowed!"},initComponent:function(){let e=this;e.store=Ext.create("Ext.data.ArrayStore",{model:"KeyValue",data:e.comboItems}),void 0===e.initialConfig.editable&&(e.editable=!1),e.callParent()},setComboItems:function(e){this.getStore().setData(e)}}),Ext.define("Proxmox.form.LanguageSelector",{extend:"Proxmox.form.KVComboBox",xtype:"proxmoxLanguageSelector",comboItems:Proxmox.Utils.language_array(),matchFieldWidth:!1,listConfig:{width:300}}),Ext.define("Proxmox.form.ComboGrid",{extend:"Ext.form.field.ComboBox",alias:["widget.proxmoxComboGrid"],preferredValue:void 0,onKeyUp:function(e,t){let i=this,a=e.getKey();i.editable||!i.allowBlank||i.multiSelect||a!==e.BACKSPACE&&a!==e.DELETE||i.setValue(""),i.callParent(arguments)},config:{skipEmptyText:!1,notFoundIsValid:!1,deleteEmpty:!1,errorHeight:100,showClearTrigger:!1},enableKeyEvents:!0,editable:!1,triggers:{clear:{cls:"pmx-clear-trigger",weight:-1,hidden:!0,handler:function(){this.setValue("")}}},setValue:function(e){let t=this,i=Ext.isArray(e)?!e.length:!e;return t.triggers.clear.setVisible(!i&&(t.allowBlank||t.showClearTrigger)),t.callParent([e])},getRawValue:function(){let e=this;return e.multiSelect?e.rawValue:e.callParent()},getSubmitData:function(){let e=this,t=null;if(!e.disabled&&e.submitValue){let i=e.getSubmitValue();null!==i?(t={},t[e.getName()]=i):e.getDeleteEmpty()&&(t={},t.delete=e.getName())}return t},getSubmitValue:function(){let e=this.callParent();return""!==e?e:this.getSkipEmptyText()?null:e},setAllowBlank:function(e){this.allowBlank=e,this.validate()},onBindStore:function(e,t){let i,a,n=this,o=n.picker;e&&(e.autoCreated&&(n.queryMode="local",n.valueField=n.displayField="field1",e.expanded||(n.displayField="field2"),n.setDisplayTpl(null)),Ext.isDefined(n.valueField)||(n.valueField=n.displayField),i={byValue:{rootProperty:"data",unique:!1}},i.byValue.property=n.valueField,e.setExtraKeys(i),n.displayField===n.valueField?e.byText=e.byValue:(i.byText={rootProperty:"data",unique:!1},i.byText.property=n.displayField,e.setExtraKeys(i)),a={rootProperty:"data",extraKeys:{byInternalId:{property:"internalId"},byValue:{property:n.valueField,rootProperty:"data"}},listeners:{beginupdate:n.onValueCollectionBeginUpdate,endupdate:n.onValueCollectionEndUpdate,scope:n}},n.valueCollection=new Ext.util.Collection(a),n.pickerSelectionModel=new Ext.selection.RowModel({mode:n.multiSelect?"SIMPLE":"SINGLE",deselectOnContainerClick:!1,enableInitialSelection:!1,pruneRemoved:!1,selected:n.valueCollection,store:e,listeners:{scope:n,lastselectedchanged:n.updateBindSelection}}),t||n.resetToDefault(),o&&(o.setSelectionModel(n.pickerSelectionModel),o.getStore()!==e&&o.bindStore(e)))},createPicker:function(){let e,t=this,i=Ext.apply({xtype:"gridpanel",id:t.pickerId,pickerField:t,floating:!0,hidden:!0,store:t.store,displayField:t.displayField,preserveScrollOnRefresh:!0,pageSize:t.pageSize,tpl:t.tpl,selModel:t.pickerSelectionModel,focusOnToFront:!1},t.listConfig,t.defaultListConfig);return e=t.picker||Ext.widget(i),e.getStore()!==t.store&&e.bindStore(t.store),t.pageSize&&e.pagingToolbar.on("beforechange",t.onPageChange,t),e.refresh=function(){e.getSelectionModel().select(t.valueCollection.getRange()),e.getView().refresh()},e.getNodeByRecord=function(){e.getView().getNodeByRecord(arguments)},e.initialConfig.maxHeight||e.on({beforeshow:t.onBeforePickerShow,scope:t}),e.getSelectionModel().on({beforeselect:t.onBeforeSelect,beforedeselect:t.onBeforeDeselect,focuschange:t.onFocusChange,selectionChange:function(e,i){i.length&&(this.setValue(i),this.fireEvent("select",t,i))},scope:t}),t.multiSelect||e.on("itemclick",function(i,a){e.getSelection()[0]===a&&t.collapse()}),e.on("show",function(){t.store.fireEvent("refresh"),t.enableLoadMask&&(t.savedMinHeight=t.savedMinHeight??e.getMinHeight(),e.setMinHeight(t.errorHeight)),t.loadError&&(Proxmox.Utils.setErrorMask(e.getView(),t.loadError),delete t.loadError,e.updateLayout())}),e.getNavigationModel().navigateOnSpace=!1,e},clearLocalFilter:function(){let e=this;e.queryFilter&&(e.changingFilters=!0,e.store.removeFilter(e.queryFilter,!0),e.queryFilter=null,e.changingFilters=!1)},isValueInStore:function(e){let t=this,i=t.store,a=!1;return i?(t.queryFilter&&"local"===t.queryMode&&t.clearFilterOnBlur&&t.clearLocalFilter(),Ext.isArray(e)?Ext.Array.each(e,function(e){return!i.findRecord(t.valueField,e,0,!1,!0,!0)||(a=!0,!1)}):a=!!i.findRecord(t.valueField,e,0,!1,!0,!0),a):a},validator:function(e){let t=this;return!e||((t.valueField&&t.valueField!==t.displayField||t.multiSelect&&!Ext.isArray(e))&&(e=t.getValue()),!(!t.notFoundIsValid&&!t.isValueInStore(e))||gettext("Invalid Value"))},setDisabled:function(e){this.callParent([e]),this.validate()},initComponent:function(){let e=this;Ext.apply(e,{queryMode:"local",matchFieldWidth:!1}),Ext.applyIf(e,{value:[]}),Ext.applyIf(e.listConfig,{width:400}),e.callParent(),e.picker||e.getPicker(),e.mon(e.store,"beforeload",function(){e.isDisabled()||(e.enableLoadMask=!0)}),e.mon(e.store,"load",function(t,i,a,n){if(a){e.clearInvalid(),delete e.loadError,e.enableLoadMask&&(delete e.enableLoadMask,e.picker&&(e.picker.setMinHeight(e.savedMinHeight||0),Proxmox.Utils.setErrorMask(e.picker.getView()),delete e.savedMinHeight,e.picker.updateLayout()));let t=e.getValue()||e.preferredValue;t&&e.setValue(t,!0);let i=!1;if(t&&(i=e.isValueInStore(t)),!i)if(Ext.isArray(t)?t.length:t)e.notFoundIsValid||e.isDisabled()||e.markInvalid(gettext("Invalid Value"));else{let i=e.store.first();e.autoSelect&&i&&i.data?(t=i.data[e.valueField],e.setValue(t,!0)):e.allowBlank||(e.setValue(t),e.isDisabled()||e.markInvalid(e.blankText))}}else{let t=Proxmox.Utils.getResponseErrorMessage(n.getError());e.picker&&(e.savedMinHeight=e.savedMinHeight??e.picker.getMinHeight(),e.picker.setMinHeight(e.errorHeight),Proxmox.Utils.setErrorMask(e.picker.getView(),t),e.picker.updateLayout()),e.loadError=t}})}}),Ext.define("Proxmox.form.RRDTypeSelector",{extend:"Ext.form.field.ComboBox",alias:["widget.proxmoxRRDTypeSelector"],displayField:"text",valueField:"id",editable:!1,queryMode:"local",value:"hour",stateEvents:["select"],stateful:!0,stateId:"proxmoxRRDTypeSelection",store:{type:"array",fields:["id","timeframe","cf","text"],data:[["hour","hour","AVERAGE",gettext("Hour")+" ("+gettext("average")+")"],["hourmax","hour","MAX",gettext("Hour")+" ("+gettext("maximum")+")"],["day","day","AVERAGE",gettext("Day")+" ("+gettext("average")+")"],["daymax","day","MAX",gettext("Day")+" ("+gettext("maximum")+")"],["week","week","AVERAGE",gettext("Week")+" ("+gettext("average")+")"],["weekmax","week","MAX",gettext("Week")+" ("+gettext("maximum")+")"],["month","month","AVERAGE",gettext("Month")+" ("+gettext("average")+")"],["monthmax","month","MAX",gettext("Month")+" ("+gettext("maximum")+")"],["year","year","AVERAGE",gettext("Year")+" ("+gettext("average")+")"],["yearmax","year","MAX",gettext("Year")+" ("+gettext("maximum")+")"]]},getState:function(){let e=this.getStore().findExact("id",this.getValue()),t=this.getStore().getAt(e);if(t)return{id:t.data.id,timeframe:t.data.timeframe,cf:t.data.cf}},applyState:function(e){e&&e.id&&this.setValue(e.id)}}),Ext.define("Proxmox.form.BondModeSelector",{extend:"Proxmox.form.KVComboBox",alias:["widget.bondModeSelector"],openvswitch:!1,initComponent:function(){let e=this;e.openvswitch?e.comboItems=Proxmox.Utils.bond_mode_array(["active-backup","balance-slb","lacp-balance-slb","lacp-balance-tcp"]):e.comboItems=Proxmox.Utils.bond_mode_array(["balance-rr","active-backup","balance-xor","broadcast","802.3ad","balance-tlb","balance-alb"]),e.callParent()}}),Ext.define("Proxmox.form.BondPolicySelector",{extend:"Proxmox.form.KVComboBox",alias:["widget.bondPolicySelector"],comboItems:[["layer2","layer2"],["layer2+3","layer2+3"],["layer3+4","layer3+4"]]}),Ext.define("Proxmox.form.NetworkSelectorController",{extend:"Ext.app.ViewController",alias:"controller.proxmoxNetworkSelectorController",init:function(e){if(!e.nodename)throw"missing custom view config: nodename";e.getStore().getProxy().setUrl("/api2/json/nodes/"+e.nodename+"/network")}}),Ext.define("Proxmox.data.NetworkSelector",{extend:"Ext.data.Model",fields:[{name:"active"},{name:"cidr"},{name:"cidr6"},{name:"address"},{name:"address6"},{name:"comments"},{name:"iface"},{name:"slaves"},{name:"type"}]}),Ext.define("Proxmox.form.NetworkSelector",{extend:"Proxmox.form.ComboGrid",alias:"widget.proxmoxNetworkSelector",controller:"proxmoxNetworkSelectorController",nodename:"localhost",setNodename:function(e){this.nodename=e;let t=this.getStore();t.removeAll(),this.getPicker().refresh(),t&&"function"==typeof t.getProxy&&(t.getProxy().setUrl("/api2/json/nodes/"+e+"/network"),t.load())},valueField:"cidr",displayField:"cidr",store:{autoLoad:!0,model:"Proxmox.data.NetworkSelector",proxy:{type:"proxmox"},sorters:[{property:"iface",direction:"ASC"}],filters:[function(e){return e.data.cidr}],listeners:{load:function(e,t,i){i&&t.forEach(function(t){if(t.data.cidr6){let i=t.data.cidr?t.copy(null):t;i.data.cidr=t.data.cidr6,i.data.address=t.data.address6,delete t.data.cidr6,i.data.comments=t.data.comments6,delete t.data.comments6,e.add(i)}})}}},listConfig:{width:600,columns:[{header:gettext("CIDR"),dataIndex:"cidr",hideable:!1,flex:1},{header:gettext("IP"),dataIndex:"address",hidden:!0},{header:gettext("Interface"),width:90,dataIndex:"iface"},{header:gettext("Active"),renderer:Proxmox.Utils.format_boolean,width:60,dataIndex:"active"},{header:gettext("Type"),width:80,hidden:!0,dataIndex:"type"},{header:gettext("Comment"),flex:2,dataIndex:"comments",renderer:Ext.String.htmlEncode}]}}),Ext.define("Proxmox.form.RealmComboBox",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxRealmComboBox",controller:{xclass:"Ext.app.ViewController",init:function(e){let t=e.getStore();t.proxy.url=`/api2/json${e.baseUrl}`,e.storeFilter&&t.setFilters(e.storeFilter),t.on("load",this.onLoad,e),t.load()},onLoad:function(e,t,i){if(!i)return;let a=this,n=a.getValue();if(!n||!a.store.findRecord("realm",n,0,!1,!0,!0)){let e="pam";Ext.each(t,function(t){t.data&&t.data.default&&(e=t.data.realm)}),a.setValue(e)}}},storeFilter:void 0,fieldLabel:gettext("Realm"),name:"realm",queryMode:"local",allowBlank:!1,editable:!1,forceSelection:!0,autoSelect:!1,triggerAction:"all",valueField:"realm",displayField:"descr",baseUrl:"/access/domains",getState:function(){return{value:this.getValue()}},applyState:function(e){e&&e.value&&this.setValue(e.value)},stateEvents:["select"],stateful:!0,id:"pveloginrealm",stateID:"pveloginrealm",store:{model:"pmx-domains",autoLoad:!1}}),Ext.define("Proxmox.form.field.PruneKeep",{extend:"Proxmox.form.field.Integer",xtype:"pmxPruneKeepField",allowBlank:!0,minValue:1,listeners:{dirtychange:(e,t)=>e.triggers.clear.setVisible(t)},triggers:{clear:{cls:"pmx-clear-trigger",weight:-1,hidden:!0,handler:function(){this.triggers.clear.setVisible(!1),this.setValue(this.originalValue)}}}}),Ext.define("pmx-roles",{extend:"Ext.data.Model",fields:["roleid","privs"],proxy:{type:"proxmox",url:"/api2/json/access/roles"},idProperty:"roleid"}),Ext.define("Proxmox.form.RoleSelector",{extend:"Proxmox.form.ComboGrid",alias:"widget.pmxRoleSelector",allowBlank:!1,autoSelect:!1,valueField:"roleid",displayField:"roleid",listConfig:{width:560,resizable:!0,columns:[{header:gettext("Role"),sortable:!0,dataIndex:"roleid",flex:2},{header:gettext("Privileges"),dataIndex:"privs",cellWrap:!0,renderer:e=>Ext.isArray(e)?e.join(", "):e.replaceAll(",",", "),flex:5}]},store:{autoLoad:!0,model:"pmx-roles",sorters:"roleid"}}),Ext.define("Proxmox.form.DiskSelector",{extend:"Proxmox.form.ComboGrid",xtype:"pmxDiskSelector",diskType:void 0,includePartitions:!1,typeProperty:"type",valueField:"devpath",displayField:"devpath",emptyText:gettext("No Disks unused"),listConfig:{width:600,columns:[{header:gettext("Device"),flex:3,sortable:!0,dataIndex:"devpath"},{header:gettext("Size"),flex:2,sortable:!1,renderer:Proxmox.Utils.format_size,dataIndex:"size"},{header:gettext("Serial"),flex:5,sortable:!0,dataIndex:"serial"}]},initComponent:function(){var e=this,t=e.nodename;if(!t)throw"no node name specified";let i={};e.diskType&&(i[e.typeProperty]=e.diskType),e.includePartitions&&(i["include-partitions"]=1);var a=Ext.create("Ext.data.Store",{filterOnLoad:!0,model:"pmx-disk-list",proxy:{type:"proxmox",url:`/api2/json/nodes/${t}/disks/list`,extraParams:i},sorters:[{property:"devpath",direction:"ASC"}]});Ext.apply(e,{store:a}),e.callParent(),a.load()}}),Ext.define("Proxmox.form.MultiDiskSelector",{extend:"Ext.grid.Panel",alias:"widget.pmxMultiDiskSelector",mixins:{field:"Ext.form.field.Field"},selModel:"checkboxmodel",store:{data:[],proxy:{type:"proxmox"}},valueField:"devpath",typeParameter:"type",diskType:"unused",includePartitions:!1,disks:[],allowBlank:!1,getValue:function(){return this.disks},setValue:function(e){let t=this;e??=[],Ext.isArray(e)||(e=e.split(/;, /));let i=t.getStore(),a=[],n=t.valueField;return e.forEach(e=>{let t=i.findRecord(n,e,0,!1,!0,!0);t&&a.push(t)}),t.setSelection(a),t.mixins.field.setValue.call(t,e)},getErrors:function(e){let t=this;return!1===t.allowBlank&&0===t.getSelectionModel().getCount()?(t.addBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[gettext("No Disk selected")]):(t.removeBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[])},update_disklist:function(){var e=this,t=e.getSelection(),i=[];t.sort(function(e,t){return(e.get("order")||0)-(t.get("order")||0)}),t.forEach(function(t){i.push(t.get(e.valueField))}),e.validate(),e.disks=i},columns:[{text:gettext("Device"),dataIndex:"devpath",flex:2},{text:gettext("Model"),dataIndex:"model",flex:2},{text:gettext("Serial"),dataIndex:"serial",flex:2},{text:gettext("Size"),dataIndex:"size",renderer:Proxmox.Utils.format_size,flex:1},{header:gettext("Order"),xtype:"widgetcolumn",dataIndex:"order",sortable:!0,flex:1,widget:{xtype:"proxmoxintegerfield",minValue:1,isFormField:!1,listeners:{change:function(e,t,i){let a=this.up("pmxMultiDiskSelector");var n=e.getWidgetRecord();n.set("order",t),a.update_disklist(n)}}}}],listeners:{selectionchange:function(){this.update_disklist()}},initComponent:function(){let e=this,t={};if(!e.url){if(!e.nodename)throw"no url or nodename given";e.url=`/api2/json/nodes/${e.nodename}/disks/list`,t[e.typeParameter]=e.diskType,e.includePartitions&&(t["include-partitions"]=1)}e.disks=[],e.callParent();let i=e.getStore();i.setProxy({type:"proxmox",url:e.url,extraParams:t}),i.load(),i.sort({property:e.valueField})}}),Ext.define("Proxmox.form.TaskTypeSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxTaskTypeSelector",anyMatch:!0,initComponent:function(){this.store=Object.keys(Proxmox.Utils.task_desc_table).sort(),this.callParent()},listeners:{change:function(e,t,i){t!==this.originalValue&&this.triggers.clear.setVisible(!0)}},triggers:{clear:{cls:"pmx-clear-trigger",weight:-1,hidden:!0,handler:function(){this.triggers.clear.setVisible(!1),this.setValue(this.originalValue)}}}}),Ext.define("Proxmox.form.ACMEApiSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxACMEApiSelector",fieldLabel:gettext("DNS API"),displayField:"name",valueField:"id",store:{model:"proxmox-acme-challenges",autoLoad:!0},triggerAction:"all",queryMode:"local",allowBlank:!1,editable:!0,forceSelection:!0,anyMatch:!0,selectOnFocus:!0,getSchema:function(){let e=this,t=e.getValue();if(t){let i=e.getStore().findRecord("id",t,0,!1,!0,!0);if(i)return i.data.schema}return{}},initComponent:function(){let e=this;if(!e.url)throw"no url given";e.callParent(),e.getStore().getProxy().setUrl(e.url)}}),Ext.define("Proxmox.form.ACMEAccountSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxACMEAccountSelector",displayField:"name",valueField:"name",store:{model:"proxmox-acme-accounts",autoLoad:!0},triggerAction:"all",queryMode:"local",allowBlank:!1,editable:!1,forceSelection:!0,isEmpty:function(){return 0===this.getStore().getData().length},initComponent:function(){let e=this;if(!e.url)throw"no url given";e.callParent(),e.getStore().getProxy().setUrl(e.url)}}),Ext.define("Proxmox.form.ACMEPluginSelector",{extend:"Ext.form.field.ComboBox",alias:"widget.pmxACMEPluginSelector",fieldLabel:gettext("Plugin"),displayField:"plugin",valueField:"plugin",store:{model:"proxmox-acme-plugins",autoLoad:!0,filters:e=>"dns"===e.data.type},triggerAction:"all",queryMode:"local",allowBlank:!1,editable:!1,initComponent:function(){let e=this;if(!e.url)throw"no url given";e.callParent(),e.getStore().getProxy().setUrl(e.url)}}),Ext.define("Proxmox.form.UserSelector",{extend:"Proxmox.form.ComboGrid",alias:"widget.pmxUserSelector",allowBlank:!1,autoSelect:!1,valueField:"userid",displayField:"userid",editable:!0,anyMatch:!0,forceSelection:!0,store:{model:"pmx-users",autoLoad:!0,params:{enabled:1},sorters:"userid"},listConfig:{columns:[{header:gettext("User"),sortable:!0,dataIndex:"userid",renderer:Ext.String.htmlEncode,flex:1},{header:gettext("Name"),sortable:!0,renderer:(e,t,i)=>Ext.String.htmlEncode(`${e||""} ${i.data.lastname||""}`),dataIndex:"firstname",flex:1},{header:gettext("Comment"),sortable:!1,dataIndex:"comment",renderer:Ext.String.htmlEncode,flex:1}]}}),Ext.define("Proxmox.form.ThemeSelector",{extend:"Proxmox.form.KVComboBox",xtype:"proxmoxThemeSelector",comboItems:Proxmox.Utils.theme_array()}),Ext.define("Proxmox.form.field.FingerprintField",{extend:"Proxmox.form.field.Textfield",alias:["widget.pmxFingerprintField"],config:{fieldLabel:gettext("Fingerprint"),emptyText:gettext("Server certificate SHA-256 fingerprint, required for self-signed certificates"),regex:/[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){31}/,regexText:gettext("Example")+": AB:CD:EF:...",allowBlank:!0}}),Ext.define("Proxmox.button.Button",{extend:"Ext.button.Button",alias:"widget.proxmoxButton",selModel:void 0,enableFn:function(e){},confirmMsg:!1,dangerous:!1,parentXType:"grid",initComponent:function(){let e,t=this;if(t.handler){let e=t.handler;t.handler=function(i,a){let n,o;t.selModel&&(n=t.selModel.getSelection()[0],!n||!1===t.enableFn(n))||(t.confirmMsg?(o=t.confirmMsg,Ext.isFunction(t.confirmMsg)&&(o=t.confirmMsg(n)),Ext.MessageBox.defaultButton=t.dangerous?2:1,Ext.Msg.show({title:gettext("Confirm"),icon:t.dangerous?Ext.Msg.WARNING:Ext.Msg.QUESTION,message:o,buttons:Ext.Msg.YESNO,defaultFocus:t.dangerous?"no":"yes",callback:function(o){"yes"===o&&Ext.callback(e,t.scope,[i,a,n],0,t)}})):Ext.callback(e,t.scope,[i,a,n],0,t))}}if(t.callParent(),!t.selModel&&null!==t.selModel&&!1!==t.selModel){let e=t.up(t.parentXType);e&&e.selModel&&(t.selModel=e.selModel)}if(!0===t.waitMsgTarget){if(e=t.up("grid"),!e)throw"unable to find waitMsgTarget";t.waitMsgTarget=e}t.selModel&&t.mon(t.selModel,"selectionchange",function(){let e=t.selModel.getSelection()[0];e&&!1!==t.enableFn(e)?t.setDisabled(!1):t.setDisabled(!0)})}}),Ext.define("Proxmox.button.StdRemoveButton",{extend:"Proxmox.button.Button",alias:"widget.proxmoxStdRemoveButton",text:gettext("Remove"),disabled:!0,delay:void 0,config:{baseurl:void 0,customConfirmationMessage:void 0},getUrl:function(e){let t=this;return t.selModel?t.baseurl+"/"+e.getId():t.baseurl},callback:function(e,t,i){},getRecordName:e=>e.getId(),confirmMsg:function(e){let t,i=this,a=i.getRecordName(e);return t=i.customConfirmationMessage?i.customConfirmationMessage:gettext("Are you sure you want to remove entry {0}"),Ext.String.format(t,Ext.htmlEncode(`'${a}'`))},handler:function(e,t,i){let a=this,n=a.getUrl(i);void 0!==a.delay&&a.delay>=0&&(n+="?delay="+a.delay),Proxmox.Utils.API2Request({url:n,method:"DELETE",waitMsgTarget:a.waitMsgTarget,callback:function(e,t,i){Ext.callback(a.callback,a.scope,[e,t,i],0,a)},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})},initComponent:function(){let e=this;void 0!==e.initialConfig.disabled||null!==e.selModel&&!1!==e.selModel||(e.disabled=!1),e.callParent()}}),Ext.define("Proxmox.button.AltText",{extend:"Proxmox.button.Button",xtype:"proxmoxAltTextButton",defaultText:"",altText:"",listeners:{render:function(e){e.setText(this.altText);let t=e.getSize().width;e.setText(this.defaultText);let i=e.getSize().width;e.setWidth(i>t?i:t)}}}),Ext.define("Proxmox.button.Help",{extend:"Ext.button.Button",xtype:"proxmoxHelpButton",text:gettext("Help"),iconCls:" x-btn-icon-el-default-toolbar-small fa fa-question-circle",cls:"x-btn-default-toolbar-small proxmox-inline-button",hidden:!0,listenToGlobalEvent:!0,controller:{xclass:"Ext.app.ViewController",listen:{global:{proxmoxShowHelp:"onProxmoxShowHelp",proxmoxHideHelp:"onProxmoxHideHelp"}},onProxmoxShowHelp:function(e){let t=this.getView();!0===t.listenToGlobalEvent&&(t.setOnlineHelp(e),t.show())},onProxmoxHideHelp:function(){let e=this.getView();!0===e.listenToGlobalEvent&&e.hide()}},setOnlineHelp:function(e){let t=this,i=Proxmox.Utils.get_help_info(e);if(i){t.onlineHelp=e;let a=i.title;i.subtitle&&(a+=" - "+i.subtitle),t.setTooltip(a)}},setHelpConfig:function(e){this.setOnlineHelp(e.onlineHelp)},handler:function(){let e,t=this;t.onlineHelp&&(e=Proxmox.Utils.get_help_link(t.onlineHelp)),e?window.open(e):Ext.Msg.alert(gettext("Help"),gettext("No Help available"))},initComponent:function(){let e=this;e.callParent(),e.onlineHelp&&e.setOnlineHelp(e.onlineHelp)}}),Ext.define("Proxmox.grid.ObjectGrid",{extend:"Ext.grid.GridPanel",alias:["widget.proxmoxObjectGrid"],gridRows:[],disabled:!1,hideHeaders:!0,monStoreErrors:!1,add_combobox_row:function(e,t,i){let a=this;i=i||{},a.rows=a.rows||{},a.rows[e]={required:!0,defaultValue:i.defaultValue,header:t,renderer:i.renderer,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:i.labelWidth||100},items:{xtype:"proxmoxKVComboBox",name:e,comboItems:i.comboItems,value:i.defaultValue,deleteEmpty:!!i.deleteEmpty,emptyText:i.defaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,i.labelWidth),fieldLabel:t}}},i.onlineHelp&&(a.rows[e].editor.onlineHelp=i.onlineHelp)},add_text_row:function(e,t,i){let a=this;i=i||{},a.rows=a.rows||{},a.rows[e]={required:!0,defaultValue:i.defaultValue,header:t,renderer:i.renderer,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:i.labelWidth||100},items:{xtype:"proxmoxtextfield",name:e,deleteEmpty:!!i.deleteEmpty,emptyText:i.defaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,i.labelWidth),vtype:i.vtype,fieldLabel:t}}},i.onlineHelp&&(a.rows[e].editor.onlineHelp=i.onlineHelp)},add_boolean_row:function(e,t,i){let a=this;i=i||{},a.rows=a.rows||{},a.rows[e]={required:!0,defaultValue:i.defaultValue||0,header:t,renderer:i.renderer||Proxmox.Utils.format_boolean,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:i.labelWidth||100},items:{xtype:"proxmoxcheckbox",name:e,uncheckedValue:0,defaultValue:i.defaultValue||0,checked:!!i.defaultValue,deleteDefaultValue:!!i.deleteDefaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,i.labelWidth),fieldLabel:t}}},i.onlineHelp&&(a.rows[e].editor.onlineHelp=i.onlineHelp)},add_integer_row:function(e,t,i){let a=this;i=i||{},a.rows=a.rows||{},a.rows[e]={required:!0,defaultValue:i.defaultValue,header:t,renderer:i.renderer,editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:i.labelWidth||100},items:{xtype:"proxmoxintegerfield",name:e,minValue:i.minValue,maxValue:i.maxValue,emptyText:gettext("Default"),deleteEmpty:!!i.deleteEmpty,value:i.defaultValue,labelWidth:Proxmox.Utils.compute_min_label_width(t,i.labelWidth),fieldLabel:t}}},i.onlineHelp&&(a.rows[e].editor.onlineHelp=i.onlineHelp)},add_textareafield_row:function(e,t,i){let a=this;i=i||{},a.rows=a.rows||{};let n=i.fieldOpts||{};a.rows[e]={required:!0,defaultValue:"",header:t,renderer:e=>Ext.htmlEncode(Proxmox.Utils.base64ToUtf8(e)),editor:{xtype:"proxmoxWindowEdit",subject:t,fieldDefaults:{labelWidth:i.labelWidth||600},items:{xtype:"proxmoxBase64TextArea",...n,name:e}}},i.onlineHelp&&(a.rows[e].editor.onlineHelp=i.onlineHelp)},editorConfig:{},run_editor:function(){let e=this,t=e.getSelectionModel().getSelection()[0];if(!t)return;let i,a,n=e.rows[t.data.key];n.editor&&(Ext.isString(n.editor)?(a=Ext.apply({confid:t.data.key},e.editorConfig),i=Ext.create(n.editor,a)):(a=Ext.apply({confid:t.data.key},e.editorConfig),Ext.apply(a,n.editor),i=Ext.createWidget(n.editor.xtype,a),i.load()),i.show(),i.on("destroy",e.reload,e))},reload:function(){this.rstore.load()},getObjectValue:function(e,t){let i=this.store.getById(e);return i?i.data.value:t},renderKey:function(e,t,i,a,n,o){let r=this.rows;return(r&&r[e]?r[e]:{}).header||e},renderValue:function(e,t,i,a,n,o){let r=this,l=r.rows,s=i.data.key,d=(l&&l[s]?l[s]:{}).renderer;return d?d.call(r,e,t,i,a,n,o):e},listeners:{itemkeydown:function(e,t,i,a,n){n.getKey()===n.ENTER&&(this.pressedIndex=a)},itemkeyup:function(e,t,i,a,n){n.getKey()===n.ENTER&&a===this.pressedIndex&&this.run_editor(),this.pressedIndex=void 0}},initComponent:function(){let e=this;for(const t of e.gridRows||[]){let i=e[`add_${t.xtype}_row`];if("function"!=typeof i)throw`unknown object-grid row xtype '${t.xtype}'`;if("string"!=typeof t.name)throw"object-grid row need a valid name string-property!";i.call(e,t.name,t.text||t.name,t)}let t=e.rows;if(!e.rstore){if(!e.url)throw"no url specified";e.rstore=Ext.create("Proxmox.data.ObjectStore",{url:e.url,interval:e.interval,extraParams:e.extraParams,rows:e.rows})}let i=e.rstore,a=Ext.create("Proxmox.data.DiffStore",{rstore:i,sorters:[],filters:[]});if(t)for(const[e,i]of Object.entries(t))Ext.isDefined(i.defaultValue)?a.add({key:e,value:i.defaultValue}):i.required&&a.add({key:e,value:void 0});e.sorterFn&&a.sorters.add(Ext.create("Ext.util.Sorter",{sorterFn:e.sorterFn})),a.filters.add(Ext.create("Ext.util.Filter",{filterFn:function(e){if(t){let i=t[e.data.key];if(!i||!1===i.visible)return!1}return!0}})),Proxmox.Utils.monStoreErrors(e,i),Ext.applyIf(e,{store:a,stateful:!1,columns:[{header:gettext("Name"),width:e.cwidth1||200,dataIndex:"key",renderer:e.renderKey},{flex:1,header:gettext("Value"),dataIndex:"value",renderer:e.renderValue}]}),e.callParent(),e.monStoreErrors&&Proxmox.Utils.monStoreErrors(e,e.store)}}),Ext.define("Proxmox.grid.PendingObjectGrid",{extend:"Proxmox.grid.ObjectGrid",alias:["widget.proxmoxPendingObjectGrid"],getObjectValue:function(e,t,i){let a=this.store.getById(e);if(a){let e=a.data.value;return i&&(Ext.isDefined(a.data.pending)&&""!==a.data.pending?e=a.data.pending:1===a.data.delete&&(e=t)),Ext.isDefined(e)&&""!==e?e:t}return t},hasPendingChanges:function(e){let t=this,i=t.rows,a=(i&&i[e]?i[e]:{}).multiKey||[e],n=!1;return Ext.Array.each(a,function(e){let i=t.store.getById(e);return!(i&&i.data&&(Ext.isDefined(i.data.pending)&&""!==i.data.pending||1===i.data.delete))||(n=!0,!1)}),n},renderValue:function(e,t,i,a,n,o){let r=this,l=r.rows,s=i.data.key,d=l&&l[s]?l[s]:{},u=d.renderer,c="",x="";if(u?(c=u(e,t,i,a,n,o,!1),r.hasPendingChanges(s)&&(x=u(i.data.pending,t,i,a,n,o,!0)),x===c&&(x=void 0)):(c=e||"",x=i.data.pending),i.data.delete){let e=!0;d.multiKey&&Ext.Array.each(d.multiKey,function(t){let i=r.store.getById(t);return!i||!i.data||1===i.data.delete||(e=!1,!1)}),e&&(x='<div style="text-decoration: line-through;">'+c+"</div>")}return x?c+'<div style="color:darkorange">'+x+"</div>":c},initComponent:function(){let e=this;if(!e.rstore){if(!e.url)throw"no url specified";e.rstore=Ext.create("Proxmox.data.ObjectStore",{model:"KeyValuePendingDelete",readArray:!0,url:e.url,interval:e.interval,extraParams:e.extraParams,rows:e.rows})}e.callParent()}}),Ext.define("Proxmox.panel.AuthView",{extend:"Ext.grid.GridPanel",alias:"widget.pmxAuthView",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,stateful:!0,stateId:"grid-authrealms",viewConfig:{trackOver:!1},baseUrl:"/access/domains",storeBaseUrl:"/access/domains",columns:[{header:gettext("Realm"),width:100,sortable:!0,dataIndex:"realm"},{header:gettext("Type"),width:100,sortable:!0,dataIndex:"type"},{header:gettext("Default"),width:80,sortable:!0,dataIndex:"default",renderer:e=>e?Proxmox.Utils.renderEnabledIcon(!0):"",align:"center",cbind:{hidden:"{!showDefaultRealm}"}},{header:gettext("Comment"),sortable:!1,dataIndex:"comment",renderer:Ext.String.htmlEncode,flex:1}],openEditWindow:function(e,t){let i=this;const{useTypeInUrl:a,onlineHelp:n}=Proxmox.Schema.authDomains[e];Ext.create("Proxmox.window.AuthEditBase",{baseUrl:i.baseUrl,useTypeInUrl:a,onlineHelp:n,authType:e,realm:t,showDefaultRealm:i.showDefaultRealm,listeners:{destroy:()=>i.reload()}}).show()},reload:function(){this.getStore().load()},run_editor:function(){let e=this.getSelection()[0];e&&Proxmox.Schema.authDomains[e.data.type].edit&&this.openEditWindow(e.data.type,e.data.realm)},open_sync_window:function(){let e=this.getSelection()[0];e&&Proxmox.Schema.authDomains[e.data.type].sync&&Ext.create("Proxmox.window.SyncWindow",{type:e.data.type,realm:e.data.realm,listeners:{destroy:()=>this.reload()}}).show()},initComponent:function(){var e=this;e.store={model:"pmx-domains",sorters:{property:"realm",direction:"ASC"},proxy:{type:"proxmox",url:`/api2/json${e.storeBaseUrl}`}};let t=[];for(const[i,a]of Object.entries(Proxmox.Schema.authDomains).sort())a.add&&t.push({text:a.name,iconCls:"fa fa-fw "+(a.iconCls||"fa-address-book-o"),handler:()=>e.openEditWindow(i)});let i=[{text:gettext("Add"),menu:{items:t}},{xtype:"proxmoxButton",text:gettext("Edit"),disabled:!0,enableFn:e=>Proxmox.Schema.authDomains[e.data.type].edit,handler:()=>e.run_editor()},{xtype:"proxmoxStdRemoveButton",getUrl:t=>{let i=e.baseUrl;return Proxmox.Schema.authDomains[t.data.type].useTypeInUrl&&(i+=`/${t.get("type")}`),i+=`/${t.getId()}`,i},enableFn:e=>Proxmox.Schema.authDomains[e.data.type].add,callback:()=>e.reload()},{xtype:"proxmoxButton",text:gettext("Sync"),disabled:!0,enableFn:e=>Proxmox.Schema.authDomains[e.data.type].sync,handler:()=>e.open_sync_window()}];if(e.extraButtons){i.push("-");for(const t of e.extraButtons)i.push(t)}Ext.apply(e,{tbar:i,listeners:{activate:()=>e.reload(),itemdblclick:()=>e.run_editor()}}),e.callParent()}}),Ext.define("pmx-disk-list",{extend:"Ext.data.Model",fields:["devpath","used",{name:"size",type:"number"},{name:"osdid",type:"number",defaultValue:-1},{name:"status",convert:function(e,t){return e||(t.data.health?t.data.health:"partition"===t.data.type?"":Proxmox.Utils.unknownText)}},{name:"name",convert:function(e,t){return e||(t.data.devpath?t.data.devpath:void 0)}},{name:"disk-type",convert:function(e,t){return e||(t.data.type?t.data.type:void 0)}},"vendor","model","serial","rpm","type","wearout","health","mounted"],idProperty:"devpath"}),Ext.define("Proxmox.DiskList",{extend:"Ext.tree.Panel",alias:"widget.pmxDiskList",supportsWipeDisk:!1,rootVisible:!1,emptyText:gettext("No Disks found"),stateful:!0,stateId:"tree-node-disks",controller:{xclass:"Ext.app.ViewController",reload:function(){let e=this,t=e.getView(),i={};t.includePartitions&&(i["include-partitions"]=1);let a=`${t.baseurl}/list`;e.store.setProxy({type:"proxmox",extraParams:i,url:a}),e.store.load()},openSmartWindow:function(){let e=this.getView(),t=e.getSelection();if(!t||t.length<1)return;let i=t[0];Ext.create("Proxmox.window.DiskSmart",{baseurl:e.baseurl,dev:i.data.name}).show()},initGPT:function(){let e=this,t=e.getView(),i=t.getSelection();if(!i||i.length<1)return;let a=i[0];Proxmox.Utils.API2Request({url:`${t.exturl}/initgpt`,waitMsgTarget:t,method:"POST",params:{disk:a.data.name},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus),success:function(t,i){Ext.create("Proxmox.window.TaskProgress",{upid:t.result.data,taskDone:function(){e.reload()},autoShow:!0})}})},wipeDisk:function(){let e=this,t=e.getView(),i=t.getSelection();if(!i||i.length<1)return;let a=i[0];Proxmox.Utils.API2Request({url:`${t.exturl}/wipedisk`,waitMsgTarget:t,method:"PUT",params:{disk:a.data.name},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus),success:function(t,i){Ext.create("Proxmox.window.TaskProgress",{upid:t.result.data,taskDone:function(){e.reload()},autoShow:!0})}})},init:function(e){let t=e.nodename||"localhost";e.baseurl=`/api2/json/nodes/${t}/disks`,e.exturl=`/api2/extjs/nodes/${t}/disks`,this.store=Ext.create("Ext.data.Store",{model:"pmx-disk-list"}),this.store.on("load",this.onLoad,this),Proxmox.Utils.monStoreErrors(e,this.store),this.reload()},onLoad:function(e,t,i,a){let n=this.getView();if(!i)return void Proxmox.Utils.setErrorMask(n,Proxmox.Utils.getResponseErrorMessage(a.getError()));let o={};for(const e of t){let t=e.data;t.expanded=!0,t.children=t.partitions??[];for(let e of t.children)e["disk-type"]="partition",e.iconCls="fa fa-fw fa-hdd-o x-fa-tree",e.used="filesystem"===e.used?e.filesystem:e.used,e.parent=t.devpath,e.children=[],e.leaf=!0;t.iconCls="fa fa-fw fa-hdd-o x-fa-tree",t.leaf=0===t.children.length,t.parent||(o[t.devpath]=t)}for(const e of t){let t=e.data;t.parent&&(o[t.parent].leaf=!1,o[t.parent].children.push(t))}let r=[];for(const[e,t]of Object.entries(o))r.push(t);n.setRootNode({expanded:!0,children:r}),Proxmox.Utils.setErrorMask(n,!1)}},renderDiskType:function(e){if(void 0===e)return Proxmox.Utils.unknownText;switch(e){case"ssd":return"SSD";case"hdd":return"Hard Disk";case"usb":return"USB";default:return e}},renderDiskUsage:function(e,t,i){let a="";if(i){let e=[];if(i.data["osdid-list"]&&i.data["osdid-list"].length>0)for(const t of i.data["osdid-list"].sort())e.push(`OSD.${t.toString()}`);else void 0!==i.data.osdid&&i.data.osdid>=0&&e.push(`OSD.${i.data.osdid.toString()}`);i.data.journals>0&&e.push("Journal"),i.data.db>0&&e.push("DB"),i.data.wal>0&&e.push("WAL"),e.length>0&&(a=`, Ceph (${e.join(", ")})`)}return(e={bios:"BIOS boot",zfsreserved:"ZFS reserved",efi:"EFI",lvm:"LVM",zfs:"ZFS"}[e]||e)?`${e}${a}`:Proxmox.Utils.noText},columns:[{xtype:"treecolumn",header:gettext("Device"),width:150,sortable:!0,dataIndex:"devpath"},{header:gettext("Type"),width:80,sortable:!0,dataIndex:"disk-type",renderer:function(e){return this.renderDiskType(e)}},{header:gettext("Usage"),width:150,sortable:!1,renderer:function(e,t,i){return this.renderDiskUsage(e,t,i)},dataIndex:"used"},{header:gettext("Size"),width:100,align:"right",sortable:!0,renderer:Proxmox.Utils.format_size,dataIndex:"size"},{header:"GPT",width:60,align:"right",renderer:Proxmox.Utils.format_boolean,dataIndex:"gpt"},{header:gettext("Vendor"),width:100,sortable:!0,hidden:!0,renderer:Ext.String.htmlEncode,dataIndex:"vendor"},{header:gettext("Model"),width:200,sortable:!0,renderer:Ext.String.htmlEncode,dataIndex:"model"},{header:gettext("Serial"),width:200,sortable:!0,renderer:Ext.String.htmlEncode,dataIndex:"serial"},{header:"S.M.A.R.T.",width:100,sortable:!0,renderer:Ext.String.htmlEncode,dataIndex:"status"},{header:gettext("Mounted"),width:60,align:"right",renderer:Proxmox.Utils.format_boolean,dataIndex:"mounted"},{header:gettext("Wearout"),width:90,sortable:!0,align:"right",dataIndex:"wearout",renderer:function(e){return Ext.isNumeric(e)?(100-e).toString()+"%":gettext("N/A")}}],listeners:{itemdblclick:"openSmartWindow"},initComponent:function(){let e=this,t=[{text:gettext("Reload"),handler:"reload"},{xtype:"proxmoxButton",text:gettext("Show S.M.A.R.T. values"),parentXType:"treepanel",disabled:!0,enableFn:function(e){return!(!e||e.data.parent)},handler:"openSmartWindow"},{xtype:"proxmoxButton",text:gettext("Initialize Disk with GPT"),parentXType:"treepanel",disabled:!0,enableFn:function(e){return!(!e||e.data.parent||e.data.used&&"unused"!==e.data.used)},handler:"initGPT"}];e.supportsWipeDisk&&(t.push("-"),t.push({xtype:"proxmoxButton",text:gettext("Wipe Disk"),parentXType:"treepanel",dangerous:!0,confirmMsg:function(t){const i=t.data;let a=Ext.String.format(gettext("Are you sure you want to wipe {0}?"),i.devpath);a+=`<br> ${gettext("All data on the device will be lost!")}`;const n=e.renderDiskType(i["disk-type"]);let o;if(i.children.length>0){const t=i.children.map(t=>e.renderDiskUsage(t.used)).join(", ");o=`${gettext("Partitions")} (${t})`}else o=e.renderDiskUsage(i.used,void 0,t);const r=Proxmox.Utils.format_size(i.size),l=Ext.String.htmlEncode(i.serial);let s=`${gettext("Type")}: ${n}<br>`;return s+=`${gettext("Usage")}: ${o}<br>`,s+=`${gettext("Size")}: ${r}<br>`,s+=`${gettext("Serial")}: ${l}`,`${a}<br><br>${s}`},disabled:!0,handler:"wipeDisk"})),e.tbar=t,e.callParent()}}),Ext.define("Proxmox.EOLNotice",{extend:"Ext.Component",alias:"widget.proxmoxEOLNotice",userCls:"eol-notice",padding:"0 5",config:{product:"",version:"",eolDate:"",href:""},autoEl:{tag:"div","data-qtip":gettext("You won't get any security fixes after the End-Of-Life date. Please consider upgrading.")},getIconCls:function(){const e=new Date,t=new Date(this.eolDate);return e>new Date(t.getTime()-18144e5)?"critical fa-exclamation-triangle":"info-blue fa-info-circle"},initComponent:function(){let e=this,t=e.getIconCls(),i=e.href.startsWith("http")?e.href:`https://${e.href}`,a=Ext.String.format(gettext("Support for {0} {1} ends on {2}"),e.product,e.version,e.eolDate);e.html=`<i class="fa ${t}"></i>\n\t    <a href="${i}" target="_blank">${a} <i class="fa fa-external-link"></i></a>\n\t`,e.callParent()}}),Ext.define("Proxmox.panel.InputPanel",{extend:"Ext.panel.Panel",alias:["widget.inputpanel"],listeners:{activate:function(){this.onlineHelp&&Ext.GlobalEvents.fireEvent("proxmoxShowHelp",this.onlineHelp)},deactivate:function(){this.onlineHelp&&Ext.GlobalEvents.fireEvent("proxmoxHideHelp",this.onlineHelp)}},border:!1,onlineHelp:void 0,hasAdvanced:!1,showAdvanced:!1,onGetValues:function(e){return e},getValues:function(e){let t=this;Ext.isFunction(t.onGetValues)&&(e=!1);let i={};return Ext.Array.each(t.query("[isFormField]"),function(t){e&&!t.isDirty()||Proxmox.Utils.assemble_field_data(i,t.getSubmitData())}),t.onGetValues(i)},setAdvancedVisible:function(e){let t=this.getComponent("advancedContainer");t&&t.setVisible(e)},onSetValues:function(e){return e},setValues:function(e){let t=this,i=t.up("form");e=t.onSetValues(e),Ext.iterate(e,function(e,a){let n=t.query("[isFormField][name="+e+"]");for(const e of n)e&&(e.setValue(a),i.trackResetOnLoad&&e.resetOriginalValue())})},initComponent:function(){let e,t,i=this;if(i.items)e=[{layout:"anchor",items:i.items}],i.items=void 0;else if(i.column4)e=[],i.columnT&&e.push({padding:"0 0 0 0",layout:"anchor",items:i.columnT}),e.push({layout:"hbox",defaults:{border:!1,layout:"anchor",flex:1},items:[{padding:"0 10 0 0",items:i.column1},{padding:"0 10 0 0",items:i.column2},{padding:"0 10 0 0",items:i.column3},{padding:"0 0 0 10",items:i.column4}]}),i.columnB&&e.push({padding:"10 0 0 0",layout:"anchor",items:i.columnB});else{if(!i.column1)throw"unsupported config";e=[],i.columnT&&e.push({padding:"0 0 10 0",layout:"anchor",items:i.columnT}),e.push({layout:"hbox",defaults:{border:!1,layout:"anchor",flex:1},items:[{padding:"0 10 0 0",items:i.column1},{padding:"0 0 0 10",items:i.column2||[]}]}),i.columnB&&e.push({padding:"10 0 0 0",layout:"anchor",items:i.columnB})}i.advancedItems?(t=[{layout:"anchor",items:i.advancedItems}],i.advancedItems=void 0):(i.advancedColumn1||i.advancedColumn2||i.advancedColumnB)&&(t=[{layout:{type:"hbox",align:"begin"},defaults:{border:!1,layout:"anchor",flex:1},items:[{padding:"0 10 0 0",items:i.advancedColumn1||[]},{padding:"0 0 0 10",items:i.advancedColumn2||[]}]}],i.advancedColumn1=void 0,i.advancedColumn2=void 0,i.advancedColumnB&&(t.push({padding:"10 0 0 0",layout:"anchor",items:i.advancedColumnB}),i.advancedColumnB=void 0)),t&&(i.hasAdvanced=!0,t.unshift({xtype:"box",hidden:!1,border:!0,autoEl:{tag:"hr"}}),e.push({xtype:"container",itemId:"advancedContainer",hidden:!i.showAdvanced,defaults:{border:!1},items:t})),Ext.apply(i,{layout:{type:"vbox",align:"stretch"},defaultType:"container",items:e}),i.callParent()}}),Ext.define("Proxmox.widget.Info",{extend:"Ext.container.Container",alias:"widget.pmxInfoWidget",layout:{type:"vbox",align:"stretch"},value:0,maximum:1,printBar:!0,items:[{xtype:"component",itemId:"label",data:{title:"",usage:"",iconCls:void 0},tpl:['<div class="left-aligned">','<tpl if="iconCls">','<i class="{iconCls}"></i> ',"</tpl>",'{title}</div>&nbsp;<div class="right-aligned">{usage}</div>']},{height:2,border:0},{xtype:"progressbar",itemId:"progress",height:5,value:0,animate:!0}],warningThreshold:.75,criticalThreshold:.9,setPrintBar:function(e){this.printBar=e,this.getComponent("progress").setVisible(e)},setIconCls:function(e){this.getComponent("label").data.iconCls=e},setData:function(e){this.updateValue(e.text,e.usage)},updateValue:function(e,t){let i=this;if(i.lastText!==e||i.lastUsage!==t){i.lastText=e,i.lastUsage=t;var a=i.getComponent("label");if(a.update(Ext.apply(a.data,{title:i.title,usage:e})),void 0!==t&&i.printBar&&Ext.isNumeric(t)&&t>=0){let e=i.getComponent("progress");e.updateProgress(t,""),t>i.criticalThreshold?(e.removeCls("warning"),e.addCls("critical")):t>i.warningThreshold?(e.removeCls("critical"),e.addCls("warning")):(e.removeCls("warning"),e.removeCls("critical"))}}},initComponent:function(){var e=this;if(!e.title)throw"no title defined";e.callParent(),e.getComponent("progress").setVisible(e.printBar),e.updateValue(e.text,e.value),e.setIconCls(e.iconCls)}}),Ext.define("Proxmox.panel.LogView",{extend:"Ext.panel.Panel",xtype:"proxmoxLogView",pageSize:510,viewBuffer:50,lineHeight:16,scrollToEnd:!0,failCallback:void 0,controller:{xclass:"Ext.app.ViewController",updateParams:function(){let e=this.getViewModel();if(e.get("hide_timespan")||e.get("livemode"))return;let t=e.get("since"),i=e.get("until");if(t>i)return void Ext.Msg.alert("Error","Since date must be less equal than Until date.");let a=e.get("submitFormat");e.set("params.since",Ext.Date.format(t,a)),"Y-m-d"===a?e.set("params.until",Ext.Date.format(i,a)+" 23:59:59"):e.set("params.until",Ext.Date.format(i,a)),this.getView().loadTask.delay(200)},scrollPosBottom:function(){let e=this.getView(),t=e.getScrollY();return e.getScrollable().getMaxPosition().y-t},updateView:function(e,t,i){let a=this,n=a.getView(),o=a.getViewModel(),r=a.lookup("content"),l=o.get("data");if(t===l.first&&i===l.total&&e.length===l.lines&&1!==i)return;o.set("data",{first:t,total:i,lines:e.length});let s=a.scrollPosBottom(),d=n.scrollToEnd&&s<=5;if(d||(e.length=i),r.update(e.join("<br>")),d){let e=n.getScrollable();e.suspendEvent("scroll"),n.scrollTo(0,1/0),a.updateStart(!0),e.resumeEvent("scroll")}},doLoad:function(){let e=this;if(e.running)return void(e.requested=!0);e.running=!0;let t=e.getView(),i=e.getViewModel();Proxmox.Utils.API2Request({url:e.getView().url,params:i.get("params"),method:"GET",success:function(i){if(e.isDestroyed)return;Proxmox.Utils.setErrorMask(e,!1);let a=i.result.total,n=[],o=1/0;Ext.Array.each(i.result.data,function(e){o>e.n&&(o=e.n),n[e.n-1]=Ext.htmlEncode(e.t)}),e.updateView(n,o-1,a),e.running=!1,e.requested&&(e.requested=!1,t.loadTask.delay(200))},failure:function(i){if(t.failCallback)t.failCallback(i);else{let t=i.htmlStatus;Proxmox.Utils.setErrorMask(e,t)}e.running=!1,e.requested&&(e.requested=!1,t.loadTask.delay(200))}})},updateStart:function(e,t){let i=this.getView(),a=this.getViewModel(),n=a.get("params.limit"),o=a.get("data.total"),r=i.lastTargetLine&&i.lastTargetLine>t?2/3:1/3;i.lastTargetLine=t;let l=e?Math.trunc(o-n,10):Math.trunc(t-r*n+10);a.set("params.start",Math.max(l,0)),i.loadTask.delay(200)},onScroll:function(e,t){let i=this,a=i.getView(),n=i.getViewModel(),o=a.getScrollY()/a.lineHeight,r=a.getHeight()/a.lineHeight,l=Math.max(Math.trunc(o-1-a.viewBuffer),0),s=Math.trunc(o+r+1+a.viewBuffer),{start:d,limit:u}=n.get("params"),c=d<20?0:20;(l<d+c||s>d+u-c)&&i.updateStart(!1,o)},onLiveMode:function(){let e=this,t=e.getViewModel();t.set("livemode",!0),t.set("params",{start:0,limit:510});let i=e.getView();delete i.content,i.scrollToEnd=!0,e.updateView([],!0,!1)},onTimespan:function(){let e=this;e.getViewModel().set("livemode",!1),e.updateView([],!1),e.updateParams()},init:function(e){let t=this;if(!e.url)throw"no url specified";let i=this.getViewModel(),a=new Date;a.setDate(a.getDate()-3),i.set("until",new Date),i.set("since",a),i.set("params.limit",e.pageSize),i.set("hide_timespan",!e.log_select_timespan),i.set("submitFormat",e.submitFormat),t.lookup("content").setStyle("line-height",`${e.lineHeight}px`),e.loadTask=new Ext.util.DelayedTask(t.doLoad,t),t.updateParams(),e.task=Ext.TaskManager.start({run:()=>{e.isVisible()&&e.scrollToEnd&&t.scrollPosBottom()<=5&&e.loadTask.delay(200)},interval:1e3})}},onDestroy:function(){this.loadTask.cancel(),Ext.TaskManager.stop(this.task)},requestUpdate:function(){this.loadTask.delay(200)},viewModel:{data:{until:null,since:null,submitFormat:"Y-m-d",livemode:!0,hide_timespan:!1,data:{start:0,total:0,textlen:0},params:{start:0,limit:510}}},layout:"auto",bodyPadding:5,scrollable:{x:"auto",y:"auto",listeners:{scroll:{fn:function(e,t,i){let a=this.component.getController();a&&a.onScroll(t,i)},buffer:200}}},tbar:{bind:{hidden:"{hide_timespan}"},items:["->",{xtype:"segmentedbutton",items:[{text:gettext("Live Mode"),bind:{pressed:"{livemode}"},handler:"onLiveMode"},{text:gettext("Select Timespan"),bind:{pressed:"{!livemode}"},handler:"onTimespan"}]},{xtype:"box",autoEl:{cn:gettext("Since")+":"},bind:{disabled:"{livemode}"}},{xtype:"proxmoxDateTimeField",name:"since_date",reference:"since",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{since}",maxValue:"{until}",submitFormat:"{submitFormat}"}},{xtype:"box",autoEl:{cn:gettext("Until")+":"},bind:{disabled:"{livemode}"}},{xtype:"proxmoxDateTimeField",name:"until_date",reference:"until",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{until}",minValue:"{since}",submitFormat:"{submitFormat}"}},{xtype:"button",text:"Update",handler:"updateParams",bind:{disabled:"{livemode}"}}]},items:[{xtype:"box",reference:"content",style:{font:"normal 11px tahoma, arial, verdana, sans-serif","white-space":"pre"}}]}),Ext.define("Proxmox.widget.NodeInfoRepoStatus",{extend:"Proxmox.widget.Info",alias:"widget.pmxNodeInfoRepoStatus",title:gettext("Repository Status"),colspan:2,printBar:!1,product:void 0,repoLink:void 0,viewModel:{data:{subscriptionActive:"",noSubscriptionRepo:"",enterpriseRepo:"",testRepo:""},formulas:{repoStatus:function(e){return""===e("subscriptionActive")||""===e("enterpriseRepo")?"":e("noSubscriptionRepo")||e("testRepo")?"non-production":e("subscriptionActive")&&e("enterpriseRepo")?"ok":!e("subscriptionActive")&&e("enterpriseRepo")?"no-sub":e("enterpriseRepo")&&e("noSubscriptionRepo")&&e("testRepo")?"unknown":"no-repo"},repoStatusMessage:function(e){let t=this.getView();const i=e("repoStatus");let a=` <a data-qtip="${gettext("Open Repositories Panel")}"\n\t\t    href="${t.repoLink}">\n\t\t    <i class="fa black fa-chevron-right txt-shadow-hover"></i>\n\t\t    </a>`;return Proxmox.Utils.formatNodeRepoStatus(i,t.product)+a}}},setValue:function(e){this.updateValue(e)},bind:{value:"{repoStatusMessage}"},setRepositoryInfo:function(e){let t=this.getViewModel();for(const i of e){const e=i.handle,a=i.status||0;"enterprise"===e?t.set("enterpriseRepo",a):"no-subscription"===e?t.set("noSubscriptionRepo",a):"test"===e&&t.set("testRepo",a)}},setSubscriptionStatus:function(e){this.getViewModel().set("subscriptionActive",e)},initComponent:function(){let e=this;if(void 0===e.product)throw"no product name provided";if(void 0===e.repoLink)throw"no repo link href provided";e.callParent()}}),Ext.define("Proxmox.panel.NotificationConfigViewModel",{extend:"Ext.app.ViewModel",alias:"viewmodel.pmxNotificationConfigPanel",formulas:{builtinSelected:function(e){let t=e("selection")?.get("origin");return"modified-builtin"===t||"builtin"===t},removeButtonText:e=>e("builtinSelected")?gettext("Reset"):gettext("Remove"),removeButtonConfirmMessage:function(e){return e("builtinSelected")?gettext("Do you want to reset {0} to its default settings?"):void 0}}}),Ext.define("Proxmox.panel.NotificationConfigView",{extend:"Ext.panel.Panel",alias:"widget.pmxNotificationConfigView",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"chapter_notifications",layout:{type:"border"},items:[{region:"center",border:!1,xtype:"pmxNotificationEndpointView",cbind:{baseUrl:"{baseUrl}"}},{region:"south",height:"50%",border:!1,collapsible:!0,animCollapse:!1,xtype:"pmxNotificationMatcherView",cbind:{baseUrl:"{baseUrl}"}}]}),Ext.define("Proxmox.panel.NotificationEndpointView",{extend:"Ext.grid.Panel",alias:"widget.pmxNotificationEndpointView",title:gettext("Notification Targets"),viewModel:{type:"pmxNotificationConfigPanel"},bind:{selection:"{selection}"},controller:{xclass:"Ext.app.ViewController",openEditWindow:function(e,t){let i=this;Ext.create("Proxmox.window.EndpointEditBase",{baseUrl:i.getView().baseUrl,type:e,name:t,autoShow:!0,listeners:{destroy:()=>i.reload()}})},openEditForSelectedItem:function(){let e=this.getView().getSelection();e.length<1||this.openEditWindow(e[0].data.type,e[0].data.name)},reload:function(){this.getView().getStore().rstore.load(),this.getView().setSelection(null)},testEndpoint:function(){let e=this.getView(),t=e.getSelection();if(t.length<1)return;let i=t[0].data.name;Ext.Msg.confirm(gettext("Notification Target Test"),Ext.String.format(gettext("Do you want to send a test notification to '{0}'?"),i),function(t){"yes"===t&&Proxmox.Utils.API2Request({method:"POST",url:`${e.baseUrl}/targets/${i}/test`,success:function(e,t){Ext.Msg.show({title:gettext("Notification Target Test"),message:Ext.String.format(gettext("Sent test notification to '{0}'."),i),buttons:Ext.Msg.OK,icon:Ext.Msg.INFO})},autoErrorAlert:!0})})}},listeners:{itemdblclick:"openEditForSelectedItem",activate:"reload"},emptyText:gettext("No notification targets configured"),columns:[{dataIndex:"disable",text:gettext("Enable"),renderer:e=>Proxmox.Utils.renderEnabledIcon(!e),align:"center"},{dataIndex:"name",text:gettext("Target Name"),renderer:Ext.String.htmlEncode,flex:2},{dataIndex:"type",text:gettext("Type"),renderer:Ext.String.htmlEncode,flex:1},{dataIndex:"comment",text:gettext("Comment"),renderer:Ext.String.htmlEncode,flex:3},{dataIndex:"origin",text:gettext("Origin"),renderer:e=>{switch(e){case"user-created":return gettext("Custom");case"modified-builtin":return gettext("Built-In (modified)");case"builtin":return gettext("Built-In")}return"unknown"}}],store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-notification-endpoints",model:"proxmox-notification-endpoints",autoStart:!0},sorters:"name"},initComponent:function(){let e=this;if(!e.baseUrl)throw"baseUrl is not set!";let t=[];for(const[i,a]of Object.entries(Proxmox.Schema.notificationEndpointTypes).sort())t.push({text:a.name,iconCls:"fa fa-fw "+(a.iconCls||"fa-bell-o"),handler:()=>e.controller.openEditWindow(i)});Ext.apply(e,{tbar:[{text:gettext("Add"),menu:t},{xtype:"proxmoxButton",text:gettext("Modify"),handler:"openEditForSelectedItem",disabled:!0},{xtype:"proxmoxStdRemoveButton",callback:"reload",bind:{text:"{removeButtonText}",customConfirmationMessage:"{removeButtonConfirmMessage}"},getUrl:function(t){return`${e.baseUrl}/endpoints/${t.data.type}/${t.getId()}`},enableFn:e=>{let t=e.get("origin");return"user-created"===t||"modified-builtin"===t}},"-",{xtype:"proxmoxButton",text:gettext("Test"),handler:"testEndpoint",disabled:!0}]}),e.callParent(),e.store.rstore.proxy.setUrl(`/api2/json/${e.baseUrl}/targets`)}}),Ext.define("Proxmox.panel.NotificationMatcherView",{extend:"Ext.grid.Panel",alias:"widget.pmxNotificationMatcherView",title:gettext("Notification Matchers"),controller:{xclass:"Ext.app.ViewController",openEditWindow:function(e){let t=this;Ext.create("Proxmox.window.NotificationMatcherEdit",{baseUrl:t.getView().baseUrl,name:e,autoShow:!0,listeners:{destroy:()=>t.reload()}})},openEditForSelectedItem:function(){let e=this.getView().getSelection();e.length<1||this.openEditWindow(e[0].data.name)},reload:function(){this.getView().getStore().rstore.load(),this.getView().setSelection(null)}},viewModel:{type:"pmxNotificationConfigPanel"},bind:{selection:"{selection}"},listeners:{itemdblclick:"openEditForSelectedItem",activate:"reload"},emptyText:gettext("No notification matchers configured"),columns:[{dataIndex:"disable",text:gettext("Enable"),renderer:e=>Proxmox.Utils.renderEnabledIcon(!e),align:"center"},{dataIndex:"name",text:gettext("Matcher Name"),renderer:Ext.String.htmlEncode,flex:1},{dataIndex:"comment",text:gettext("Comment"),renderer:Ext.String.htmlEncode,flex:2},{dataIndex:"origin",text:gettext("Origin"),renderer:e=>{switch(e){case"user-created":return gettext("Custom");case"modified-builtin":return gettext("Built-In (modified)");case"builtin":return gettext("Built-In")}return"unknown"}}],store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-notification-matchers",model:"proxmox-notification-matchers",autoStart:!0},sorters:"name"},initComponent:function(){let e=this;if(!e.baseUrl)throw"baseUrl is not set!";Ext.apply(e,{tbar:[{xtype:"proxmoxButton",text:gettext("Add"),handler:()=>e.getController().openEditWindow(),selModel:!1},{xtype:"proxmoxButton",text:gettext("Modify"),handler:"openEditForSelectedItem",disabled:!0},{xtype:"proxmoxStdRemoveButton",callback:"reload",bind:{text:"{removeButtonText}",customConfirmationMessage:"{removeButtonConfirmMessage}"},baseurl:`${e.baseUrl}/matchers`,enableFn:e=>{let t=e.get("origin");return"user-created"===t||"modified-builtin"===t}}]}),e.callParent(),e.store.rstore.proxy.setUrl(`/api2/json/${e.baseUrl}/matchers`)}}),Ext.define("Proxmox.panel.JournalView",{extend:"Ext.panel.Panel",xtype:"proxmoxJournalView",numEntries:500,lineHeight:16,scrollToEnd:!0,controller:{xclass:"Ext.app.ViewController",updateParams:function(){let e=this.getViewModel(),t=e.get("since"),i=e.get("until");t.setHours(0,0,0,0),i.setHours(0,0,0,0),i.setDate(i.getDate()+1),this.getView().loadTask.delay(200,void 0,void 0,[!1,!1,Ext.Date.format(t,"U"),Ext.Date.format(i,"U")])},scrollPosBottom:function(){let e=this.getView(),t=e.getScrollY();return e.getScrollable().getMaxPosition().y-t},scrollPosTop:function(){return this.getView().getScrollY()},updateScroll:function(e,t,i,a){let n=this.getView();e?n.scrollToEnd&&i<=5?setTimeout(function(){n.scrollTo(0,1/0)},10):!n.scrollToEnd&&a<20*n.lineHeight&&setTimeout(function(){n.scrollTo(0,t*n.lineHeight+a)},10):setTimeout(function(){n.scrollTo(0,0)},10)},updateView:function(e,t,i){let a=this,n=a.getView(),o=a.getViewModel();if(!o||o.get("livemode")!==t)return;let r=a.lookup("content"),l=a.scrollPosBottom(),s=a.scrollPosTop(),d=e.shift(),u=e.pop(),c=e.length,x=e.map(Ext.htmlEncode).join("<br>"),m=!0;t?(i&&c?n.content=n.content?x+"<br>"+n.content:x:!i&&c?n.content=n.content?n.content+"<br>"+x:x:m=!1,i&&n.startcursor||(n.startcursor=u),!i&&n.endcursor||(n.endcursor=d)):n.content=c?x:"nothing logged or no timespan selected",m&&r.update(n.content),a.updateScroll(t,c,l,s)},doLoad:function(e,t,i,a){let n=this;if(n.running)return void(n.requested=!0);n.running=!0;let o=n.getView(),r={lastentries:o.numEntries||500};e?!t&&o.startcursor?r={startcursor:o.startcursor}:o.endcursor&&(r.endcursor=o.endcursor):r={since:i,until:a},Proxmox.Utils.API2Request({url:o.url,params:r,waitMsgTarget:e?void 0:o,method:"GET",success:function(i){if(n.isDestroyed)return;Proxmox.Utils.setErrorMask(n,!1);let a=i.result.data;n.updateView(a,e,t),n.running=!1,n.requested&&(n.requested=!1,o.loadTask.delay(200))},failure:function(e){let t=e.htmlStatus;Proxmox.Utils.setErrorMask(n,t),n.running=!1,n.requested&&(n.requested=!1,o.loadTask.delay(200))}})},onScroll:function(e,t){let i=this,a=i.getView();i.getViewModel().get("livemode")&&(i.scrollPosTop()<20*a.lineHeight?(a.scrollToEnd=!1,a.loadTask.delay(200,void 0,void 0,[!0,!0])):i.scrollPosBottom()<=5&&(a.scrollToEnd=!0))},init:function(e){let t=this;if(!e.url)throw"no url specified";let i=t.getViewModel(),a=this.getViewModel(),n=new Date;n.setDate(n.getDate()-3),a.set("until",new Date),a.set("since",n),t.lookup("content").setStyle("line-height",e.lineHeight+"px"),e.loadTask=new Ext.util.DelayedTask(t.doLoad,t,[!0,!1]),e.task=Ext.TaskManager.start({run:function(){e.isVisible()&&e.scrollToEnd&&i.get("livemode")&&t.scrollPosBottom()<=5&&e.loadTask.delay(200,void 0,void 0,[!0,!1])},interval:1e3})},onLiveMode:function(){let e=this,t=e.getView();delete t.startcursor,delete t.endcursor,delete t.content,e.getViewModel().set("livemode",!0),t.scrollToEnd=!0,e.updateView([],!0,!1)},onTimespan:function(){this.getViewModel().set("livemode",!1),this.updateView([],!1)}},onDestroy:function(){let e=this;e.loadTask.cancel(),Ext.TaskManager.stop(e.task),delete e.content},requestUpdate:function(){this.loadTask.delay(200)},viewModel:{data:{livemode:!0,until:null,since:null}},layout:"auto",bodyPadding:5,scrollable:{x:"auto",y:"auto",listeners:{scroll:{fn:function(e,t,i){let a=this.component.getController();a&&a.onScroll(t,i)},buffer:200}}},tbar:{items:["->",{xtype:"segmentedbutton",items:[{text:gettext("Live Mode"),bind:{pressed:"{livemode}"},handler:"onLiveMode"},{text:gettext("Select Timespan"),bind:{pressed:"{!livemode}"},handler:"onTimespan"}]},{xtype:"box",bind:{disabled:"{livemode}"},autoEl:{cn:gettext("Since")+":"}},{xtype:"datefield",name:"since_date",reference:"since",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{since}",maxValue:"{until}"}},{xtype:"box",bind:{disabled:"{livemode}"},autoEl:{cn:gettext("Until")+":"}},{xtype:"datefield",name:"until_date",reference:"until",format:"Y-m-d",bind:{disabled:"{livemode}",value:"{until}",minValue:"{since}"}},{xtype:"button",text:"Update",reference:"updateBtn",handler:"updateParams",bind:{disabled:"{livemode}"}}]},items:[{xtype:"box",reference:"content",style:{font:"normal 11px tahoma, arial, verdana, sans-serif","white-space":"pre"}}]}),Ext.define("pmx-permissions",{extend:"Ext.data.TreeModel",fields:["text","type",{type:"boolean",name:"propagate"}]}),Ext.define("Proxmox.panel.PermissionViewPanel",{extend:"Ext.tree.Panel",xtype:"proxmoxPermissionViewPanel",scrollable:!0,layout:"fit",rootVisible:!1,animate:!1,sortableColumns:!1,auth_id_name:"userid",auth_id:void 0,columns:[{xtype:"treecolumn",header:gettext("Path")+"/"+gettext("Permission"),dataIndex:"text",flex:6},{header:gettext("Propagate"),dataIndex:"propagate",flex:1,renderer:function(e){return Ext.isDefined(e)?Proxmox.Utils.format_boolean(e):""}}],initComponent:function(){let e=this;Proxmox.Utils.API2Request({url:"/access/permissions?"+encodeURIComponent(e.auth_id_name)+"="+encodeURIComponent(e.auth_id),method:"GET",failure:function(t,i){Proxmox.Utils.setErrorMask(e,t.htmlStatus)},success:function(t,i){Proxmox.Utils.setErrorMask(e,!1);let a=Ext.decode(t.responseText).data||{},n={name:"__root",expanded:!0,children:[]},o={"/":{children:[],text:"/",type:"path"}};Ext.Object.each(a,function(e,t){let i={text:e,type:"path",children:[]};Ext.Object.each(t,function(e,t){let a={text:e,type:"perm",propagate:1===t||!0===t,iconCls:"fa fa-fw fa-unlock",leaf:!0};i.children.push(a),i.expandable=!0}),o[e]=i}),Ext.Object.each(o,function(e,t){let i=o["/"];if("/"===e)i=n,t.expanded=!0;else{let t=e.split("/");for(;t.pop();){let e=t.join("/");if(o[e]){i=o[e];break}}}i.children.push(t)}),e.setRootNode(n)}}),e.callParent(),e.store.sorters.add(new Ext.util.Sorter({sorterFn:function(e,t){let i=e.data.text,a=t.data.text;return e.data.type!==t.data.type&&(a=e.data.type,i=t.data.type),i>a?1:i<a?-1:0}}))}}),Ext.define("Proxmox.PermissionView",{extend:"Ext.window.Window",alias:"widget.userShowPermissionWindow",mixins:["Proxmox.Mixin.CBind"],scrollable:!0,width:800,height:600,layout:"fit",cbind:{title:e=>Ext.String.htmlEncode(e("auth_id"))+` - ${gettext("Granted Permissions")}`},items:[{xtype:"proxmoxPermissionViewPanel",cbind:{auth_id:"{auth_id}",auth_id_name:"{auth_id_name}"}}]}),Ext.define("Proxmox.panel.PruneInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxPruneInputPanel",mixins:["Proxmox.Mixin.CBind"],keepLastEmptyText:"",cbindData:function(){return this.isCreate=!!this.isCreate,{}},column1:[{xtype:"pmxPruneKeepField",name:"keep-last",fieldLabel:gettext("Keep Last"),cbind:{deleteEmpty:"{!isCreate}",emptyText:"{keepLastEmptyText}"}},{xtype:"pmxPruneKeepField",name:"keep-daily",fieldLabel:gettext("Keep Daily"),cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxPruneKeepField",name:"keep-monthly",fieldLabel:gettext("Keep Monthly"),cbind:{deleteEmpty:"{!isCreate}"}}],column2:[{xtype:"pmxPruneKeepField",fieldLabel:gettext("Keep Hourly"),name:"keep-hourly",cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxPruneKeepField",name:"keep-weekly",fieldLabel:gettext("Keep Weekly"),cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxPruneKeepField",name:"keep-yearly",fieldLabel:gettext("Keep Yearly"),cbind:{deleteEmpty:"{!isCreate}"}}]}),Ext.draw.Container.prototype.defaultDownloadServerUrl="-",Ext.define("Proxmox.chart.axis.segmenter.NumericBase2",{extend:"Ext.chart.axis.segmenter.Numeric",alias:"segmenter.numericBase2",preferredStep:function(e,t){let i=Math.floor(Math.log2(t)),a=Math.pow(2,i);return(t/=a)<=1?t=1:t<2&&(t=2),{unit:{fixes:-i,scale:a},step:t}},exactStep:function(e,t){let i=Math.floor(Math.log2(t));return{unit:{fixes:(t%Math.pow(2,i)===0?0:1)-i,scale:1},step:t}}}),Ext.define("Proxmox.widget.RRDChart",{extend:"Ext.chart.CartesianChart",alias:"widget.proxmoxRRDChart",unit:void 0,powerOfTwo:!1,downloadServerUrl:"-",controller:{xclass:"Ext.app.ViewController",init:function(e){this.powerOfTwo=e.powerOfTwo},convertToUnits:function(e){let t=["","k","M","G","T","P"],i=0,a="0.##";e<.1&&(a+="#");const n=this.powerOfTwo?1024:1e3;for(;e>=n&&i<t.length-1;)e/=n,i++;e=Ext.Number.correctFloat(e),e=Ext.util.Format.number(e,a);let o=t[i];return o&&this.powerOfTwo&&(o+="i"),`${e.toString()} ${o}`},leftAxisRenderer:function(e,t,i){return this.convertToUnits(t)},onSeriesTooltipRender:function(e,t,i){let a=this.getView(),n="";"percent"===a.unit?n="%":"bytes"===a.unit?n="B":"bytespersecond"===a.unit&&(n="B/s");let o=i.field;a.fieldTitles&&a.fieldTitles[a.fields.indexOf(i.field)]&&(o=a.fieldTitles[a.fields.indexOf(i.field)]);let r=this.convertToUnits(t.get(i.field)),l=new Date(t.get("time"));e.setHtml(`${o}: ${r}${n}<br>${l}`)},onAfterAnimation:function(e,t){if(!e.header||!e.header.tools)return;let i=e.header.tools[0],a=e.interactions[0].getUndoButton();i.setDisabled(a.isDisabled())}},width:770,height:300,animation:!1,interactions:[{type:"crosszoom"}],legend:{type:"dom",padding:0},listeners:{redraw:{fn:"onAfterAnimation",options:{buffer:500}}},touchAction:{panX:!0,panY:!0},constructor:function(e){let t=e.powerOfTwo?"numericBase2":"numeric";e.axes=[{type:"numeric",position:"left",grid:!0,renderer:"leftAxisRenderer",minimum:0,segmenter:t},{type:"time",position:"bottom",grid:!0,fields:["time"]}],this.callParent([e])},checkThemeColors:function(){let e=this,t=getComputedStyle(document.documentElement),i=t.getPropertyValue("--pwt-panel-background").trim()||"#ffffff",a=t.getPropertyValue("--pwt-text-color").trim()||"#000000",n=t.getPropertyValue("--pwt-chart-primary").trim()||"#000000",o=t.getPropertyValue("--pwt-chart-grid-stroke").trim()||"#dddddd";e.setBackground(i),e.axes.forEach(e=>{e.setLabel({color:a}),e.setTitle({color:a}),e.setStyle({strokeStyle:n}),e.setGrid({stroke:o})}),e.redraw()},initComponent:function(){let e=this;if(!e.store)throw"cannot work without store";if(!e.fields)throw"cannot work without fields";e.callParent();let t="";"percent"===e.unit?t="%":"bytes"===e.unit?t="Bytes":"bytespersecond"===e.unit?t="Bytes/s":e.fieldTitles&&1===e.fieldTitles.length?t=e.fieldTitles[0]:1===e.fields.length&&(t=e.fields[0]),e.axes[0].setTitle(t),e.updateHeader(),e.header&&e.legend&&(e.header.padding="4 9 4",e.header.add(e.legend),e.legend=void 0),e.noTool||e.addTool({type:"minus",disabled:!0,tooltip:gettext("Undo Zoom"),handler:function(){let t=e.interactions[0].getUndoButton();t.handler&&t.handler()}}),e.fields.forEach(function(t,i){let a=t;e.fieldTitles&&e.fieldTitles[i]&&(a=e.fieldTitles[i]),e.addSeries(Ext.apply({type:"line",xField:"time",yField:t,title:a,fill:!0,style:{lineWidth:1.5,opacity:.6},marker:{opacity:0,scaling:.01},highlightCfg:{opacity:1,scaling:1.5},tooltip:{trackMouse:!0,renderer:"onSeriesTooltipRender"}},e.seriesConfig))}),e.store.onAfter("load",function(){e.setAnimation({duration:200,easing:"easeIn"})},this,{single:!0}),e.checkThemeColors(),e.mediaQueryList=window.matchMedia("(prefers-color-scheme: dark)"),e.themeListener=t=>{e.checkThemeColors()},e.mediaQueryList.addEventListener("change",e.themeListener)},doDestroy:function(){let e=this;e.mediaQueryList.removeEventListener("change",e.themeListener),e.callParent()}}),Ext.define("Proxmox.panel.GaugeWidget",{extend:"Ext.panel.Panel",alias:"widget.proxmoxGauge",defaults:{style:{"text-align":"center"}},items:[{xtype:"box",itemId:"title",data:{title:""},tpl:"<h3>{title}</h3>"},{xtype:"polar",height:120,border:!1,downloadServerUrl:"-",itemId:"chart",series:[{type:"gauge",value:0,colors:["#f5f5f5"],sectors:[0],donut:90,needleLength:100,totalAngle:Math.PI}],sprites:[{id:"valueSprite",type:"text",text:"",textAlign:"center",textBaseline:"bottom",x:125,y:110,fontSize:30}]},{xtype:"box",itemId:"text"}],header:!1,border:!1,warningThreshold:.6,criticalThreshold:.9,warningColor:"#fc0",criticalColor:"#FF6C59",defaultColor:"#c2ddf2",backgroundColor:"#f5f5f5",initialValue:0,checkThemeColors:function(){let e=this,t=getComputedStyle(document.documentElement),i=t.getPropertyValue("--pwt-panel-background").trim()||"#ffffff",a=t.getPropertyValue("--pwt-text-color").trim()||"#000000";e.defaultColor=t.getPropertyValue("--pwt-gauge-default").trim()||"#c2ddf2",e.criticalColor=t.getPropertyValue("--pwt-gauge-crit").trim()||"#ff6c59",e.warningColor=t.getPropertyValue("--pwt-gauge-warn").trim()||"#fc0",e.backgroundColor=t.getPropertyValue("--pwt-gauge-back").trim()||"#f5f5f5";let n=e.chart.series[0].getValue()/100,o=e.defaultColor;n>=e.criticalThreshold?o=e.criticalColor:n>=e.warningThreshold&&(o=e.warningColor),e.chart.series[0].setColors([o,e.backgroundColor]),e.chart.setBackground(i),e.valueSprite.setAttributes({fillStyle:a},!0),e.chart.redraw()},updateValue:function(e,t){let i=this,a=i.defaultColor,n={};e>=i.criticalThreshold?a=i.criticalColor:e>=i.warningThreshold&&(a=i.warningColor),i.chart.series[0].setColors([a,i.backgroundColor]),i.chart.series[0].setValue(100*e),i.valueSprite.setText(" "+(100*e).toFixed(0)+"%"),n.x=i.chart.getWidth()/2,n.y=i.chart.getHeight()-20,i.spriteFontSize&&(n.fontSize=i.spriteFontSize),i.valueSprite.setAttributes(n,!0),void 0!==t&&i.text.setHtml(t)},initComponent:function(){let e=this;e.callParent(),e.title&&e.getComponent("title").update({title:e.title}),e.text=e.getComponent("text"),e.chart=e.getComponent("chart"),e.valueSprite=e.chart.getSurface("chart").get("valueSprite"),e.checkThemeColors(),e.mediaQueryList=window.matchMedia("(prefers-color-scheme: dark)"),e.themeListener=t=>{e.checkThemeColors()},e.mediaQueryList.addEventListener("change",e.themeListener)},doDestroy:function(){let e=this;e.mediaQueryList.removeEventListener("change",e.themeListener),e.callParent()}}),Ext.define("Proxmox.panel.GotifyEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxGotifyEditPanel",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"notification_targets_gotify",type:"gotify",items:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0},{xtype:"proxmoxtextfield",fieldLabel:gettext("Server URL"),name:"server",allowBlank:!1},{xtype:"proxmoxtextfield",inputType:"password",fieldLabel:gettext("API Token"),name:"token",cbind:{emptyText:e=>e("isCreate")?"":gettext("Unchanged"),allowBlank:"{!isCreate}"}},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:e=>(e.enable=!e.disable,delete e.disable,e),onGetValues:function(e){let t=this;return e.enable?t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,e}}),Ext.define("Proxmox.panel.Certificates",{extend:"Ext.grid.Panel",xtype:"pmxCertificates",uploadButtons:void 0,infoUrl:void 0,columns:[{header:gettext("File"),width:150,dataIndex:"filename"},{header:gettext("Issuer"),flex:1,dataIndex:"issuer"},{header:gettext("Subject"),flex:1,dataIndex:"subject"},{header:gettext("Public Key Alogrithm"),flex:1,dataIndex:"public-key-type",hidden:!0},{header:gettext("Public Key Size"),flex:1,dataIndex:"public-key-bits",hidden:!0},{header:gettext("Valid Since"),width:150,dataIndex:"notbefore",renderer:Proxmox.Utils.render_timestamp},{header:gettext("Expires"),width:150,dataIndex:"notafter",renderer:Proxmox.Utils.render_timestamp},{header:gettext("Subject Alternative Names"),flex:1,dataIndex:"san",renderer:Proxmox.Utils.render_san},{header:gettext("Fingerprint"),dataIndex:"fingerprint",hidden:!0},{header:gettext("PEM"),dataIndex:"pem",hidden:!0}],reload:function(){this.rstore.load()},delete_certificate:function(){let e=this.selModel.getSelection()[0];if(!e)return;let t=this.certById[e.id],i=t.url;Proxmox.Utils.API2Request({url:`/api2/extjs/${i}?restart=1`,method:"DELETE",success:function(e,i){t.reloadUi&&(Ext.getBody().mask(gettext("API server will be restarted to use new certificates, please reload web-interface!"),["pve-static-mask"]),Ext.defer(()=>window.location.reload(!0),1e4))},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})},controller:{xclass:"Ext.app.ViewController",view_certificate:function(){let e=this.getView(),t=e.getSelection();!t||t.length<1||Ext.create("Proxmox.window.CertificateViewer",{cert:t[0].data.filename,url:`/api2/extjs/${e.infoUrl}`}).show()}},listeners:{itemdblclick:"view_certificate"},initComponent:function(){let e=this;if(e.nodename||(e.nodename="_all"),!e.uploadButtons)throw"no upload buttons defined";if(!e.infoUrl)throw"no certificate store url given";e.rstore=Ext.create("Proxmox.data.UpdateStore",{storeid:"certs-"+e.nodename,model:"proxmox-certificate",proxy:{type:"proxmox",url:`/api2/extjs/${e.infoUrl}`}}),e.store={type:"diff",rstore:e.rstore};let t=[];if(e.deletableCertIds={},e.certById={},1===e.uploadButtons.length){let i=e.uploadButtons[0];if(!i.url)throw"missing certificate url";e.certById[i.id]=i,i.deletable&&(e.deletableCertIds[i.id]=!0),t.push({xtype:"button",text:gettext("Upload Custom Certificate"),handler:function(){let e=this.up("grid"),t=Ext.create("Proxmox.window.CertificateUpload",{url:`/api2/extjs/${i.url}`,reloadUi:i.reloadUi});t.show(),t.on("destroy",e.reload,e)}})}else{let i=[];e.selModel=Ext.create("Ext.selection.RowModel",{});for(const t of e.uploadButtons){if(!t.id)throw"missing id in certificate entry";if(!t.url)throw"missing url in certificate entry";if(!t.name)throw"missing name in certificate entry";e.certById[t.id]=t,t.deletable&&(e.deletableCertIds[t.id]=!0),i.push({text:Ext.String.format("Upload {0} Certificate",t.name),handler:function(){let e=this.up("grid"),i=Ext.create("Proxmox.window.CertificateUpload",{url:`/api2/extjs/${t.url}`,reloadUi:t.reloadUi});i.show(),i.on("destroy",e.reload,e)}})}t.push({text:gettext("Upload Custom Certificate"),menu:{xtype:"menu",items:i}})}t.push({xtype:"proxmoxButton",text:gettext("Delete Custom Certificate"),confirmMsg:t=>{let i=e.certById[t.id];return i.name?Ext.String.format(gettext("Are you sure you want to remove the certificate used for {0}"),i.name):gettext("Are you sure you want to remove the certificate")},callback:()=>e.reload(),selModel:e.selModel,disabled:!0,enableFn:t=>!!e.deletableCertIds[t.id],handler:function(){e.delete_certificate()}},"-",{xtype:"proxmoxButton",itemId:"viewbtn",disabled:!0,text:gettext("View Certificate"),handler:"view_certificate"}),Ext.apply(e,{tbar:t}),e.callParent(),e.rstore.startUpdate(),e.on("destroy",e.rstore.stopUpdate,e.rstore)}}),Ext.define("Proxmox.panel.ACMEAccounts",{extend:"Ext.grid.Panel",xtype:"pmxACMEAccounts",title:gettext("Accounts"),acmeUrl:void 0,controller:{xclass:"Ext.app.ViewController",addAccount:function(){let e=this,t=e.getView(),i=-1!==t.getStore().findExact("name","default");Ext.create("Proxmox.window.ACMEAccountCreate",{defaultExists:i,acmeUrl:t.acmeUrl,taskDone:function(){e.reload()}}).show()},viewAccount:function(){let e=this.getView(),t=e.getSelection();t.length<1||Ext.create("Proxmox.window.ACMEAccountView",{url:`${e.acmeUrl}/account/${t[0].data.name}`}).show()},reload:function(){this.getView().getStore().rstore.load()},showTaskAndReload:function(e,t,i){let a=this;if(!t)return;let n=i.result.data;Ext.create("Proxmox.window.TaskProgress",{upid:n,taskDone:function(){a.reload()}}).show()}},minHeight:150,emptyText:gettext("No Accounts configured"),columns:[{dataIndex:"name",text:gettext("Name"),renderer:Ext.String.htmlEncode,flex:1}],listeners:{itemdblclick:"viewAccount"},store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-acme-accounts",model:"proxmox-acme-accounts",autoStart:!0},sorters:"name"},initComponent:function(){let e=this;if(!e.acmeUrl)throw"no acmeUrl given";Ext.apply(e,{tbar:[{xtype:"proxmoxButton",text:gettext("Add"),selModel:!1,handler:"addAccount"},{xtype:"proxmoxButton",text:gettext("View"),handler:"viewAccount",disabled:!0},{xtype:"proxmoxStdRemoveButton",baseurl:`${e.acmeUrl}/account`,callback:"showTaskAndReload"}]}),e.callParent(),e.store.rstore.proxy.setUrl(`/api2/json/${e.acmeUrl}/account`)}}),Ext.define("Proxmox.panel.ACMEPluginView",{extend:"Ext.grid.Panel",alias:"widget.pmxACMEPluginView",title:gettext("Challenge Plugins"),acmeUrl:void 0,controller:{xclass:"Ext.app.ViewController",addPlugin:function(){let e=this,t=e.getView();Ext.create("Proxmox.window.ACMEPluginEdit",{acmeUrl:t.acmeUrl,url:`${t.acmeUrl}/plugins`,isCreate:!0,apiCallDone:function(){e.reload()}}).show()},editPlugin:function(){let e=this,t=e.getView(),i=t.getSelection();if(i.length<1)return;let a=i[0].data.plugin;Ext.create("Proxmox.window.ACMEPluginEdit",{acmeUrl:t.acmeUrl,url:`${t.acmeUrl}/plugins/${a}`,apiCallDone:function(){e.reload()}}).show()},reload:function(){this.getView().getStore().rstore.load()}},minHeight:150,emptyText:gettext("No Plugins configured"),columns:[{dataIndex:"plugin",text:gettext("Plugin"),renderer:Ext.String.htmlEncode,flex:1},{dataIndex:"api",text:"API",renderer:Ext.String.htmlEncode,flex:1}],listeners:{itemdblclick:"editPlugin"},store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,rstore:{type:"update",storeid:"proxmox-acme-plugins",model:"proxmox-acme-plugins",autoStart:!0,filters:e=>!!e.data.api},sorters:"plugin"},initComponent:function(){let e=this;if(!e.acmeUrl)throw"no acmeUrl given";e.url=`${e.acmeUrl}/plugins`,Ext.apply(e,{tbar:[{xtype:"proxmoxButton",text:gettext("Add"),handler:"addPlugin",selModel:!1},{xtype:"proxmoxButton",text:gettext("Edit"),handler:"editPlugin",disabled:!0},{xtype:"proxmoxStdRemoveButton",callback:"reload",baseurl:`${e.acmeUrl}/plugins`}]}),e.callParent(),e.store.rstore.proxy.setUrl(`/api2/json/${e.acmeUrl}/plugins`)}}),Ext.define("proxmox-acme-domains",{extend:"Ext.data.Model",fields:["domain","type","alias","plugin","configkey"],idProperty:"domain"}),Ext.define("Proxmox.panel.ACMEDomains",{extend:"Ext.grid.Panel",xtype:"pmxACMEDomains",mixins:["Proxmox.Mixin.CBind"],margin:"10 0 0 0",title:"ACME",emptyText:gettext("No Domains configured"),url:void 0,domainUsages:void 0,orderUrl:void 0,separateDomainEntries:void 0,acmeUrl:void 0,cbindData:function(e){return{acmeUrl:this.acmeUrl,accountUrl:`/api2/json/${this.acmeUrl}/account`}},viewModel:{data:{domaincount:0,account:void 0,configaccount:void 0,accountEditable:!1,accountsAvailable:!1,hasUsage:!1},formulas:{canOrder:e=>!!e("account")&&e("domaincount")>0,editBtnIcon:e=>"fa black fa-"+(e("accountEditable")?"check":"pencil"),accountTextHidden:e=>e("accountEditable")||!e("accountsAvailable"),accountValueHidden:e=>!e("accountEditable")||!e("accountsAvailable")}},controller:{xclass:"Ext.app.ViewController",init:function(e){this.lookup("accountselector").store.on("load",this.onAccountsLoad,this)},onAccountsLoad:function(e,t,i){let a=this,n=a.getViewModel(),o=n.get("configaccount");n.set("accountsAvailable",t.length>0),a.autoChangeAccount&&t.length>0?(a.changeAccount(t[0].data.name,()=>{n.set("accountEditable",!1),a.reload()}),a.autoChangeAccount=!1):o&&(-1!==e.findExact("name",o)?n.set("account",o):n.set("account",null))},addDomain:function(){let e=this,t=e.getView();Ext.create("Proxmox.window.ACMEDomainEdit",{url:t.url,acmeUrl:t.acmeUrl,nodeconfig:t.nodeconfig,domainUsages:t.domainUsages,separateDomainEntries:t.separateDomainEntries,apiCallDone:function(){e.reload()}}).show()},editDomain:function(){let e=this,t=e.getView(),i=t.getSelection();i.length<1||Ext.create("Proxmox.window.ACMEDomainEdit",{url:t.url,acmeUrl:t.acmeUrl,nodeconfig:t.nodeconfig,domainUsages:t.domainUsages,separateDomainEntries:t.separateDomainEntries,domain:i[0].data,apiCallDone:function(){e.reload()}}).show()},removeDomain:function(){let e=this,t=e.getView(),i=t.getSelection();if(i.length<1)return;let a=i[0].data,n={};if("acme"!==a.configkey)n.delete=a.configkey;else{let e=Proxmox.Utils.parseACME(t.nodeconfig.acme);Proxmox.Utils.remove_domain_from_acme(e,a.domain),n.acme=Proxmox.Utils.printACME(e)}Proxmox.Utils.API2Request({method:"PUT",url:t.url,params:n,success:function(t,i){e.reload()},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})},toggleEditAccount:function(){let e=this,t=e.getViewModel();t.get("accountEditable")?e.changeAccount(t.get("account"),function(){t.set("accountEditable",!1),e.reload()}):t.set("accountEditable",!0)},changeAccount:function(e,t){let i=this.getView(),a={},n=Proxmox.Utils.parseACME(i.nodeconfig.acme);n.account=e,a.acme=Proxmox.Utils.printACME(n),Proxmox.Utils.API2Request({method:"PUT",waitMsgTarget:i,url:i.url,params:a,success:function(e,i){Ext.isFunction(t)&&t()},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})},order:function(e){let t=this,i=t.getView();Proxmox.Utils.API2Request({method:"POST",params:{force:1},url:e?e.url:i.orderUrl,success:function(i,a){Ext.create("Proxmox.window.TaskViewer",{upid:i.result.data,taskDone:function(i){t.orderFinished(i,e)}}).show()},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})},orderFinished:function(e,t){e&&t.reloadUi&&(Ext.getBody().mask(gettext("API server will be restarted to use new certificates, please reload web-interface!"),["pve-static-mask"]),Ext.defer(()=>window.location.reload(!0),1e4))},reload:function(){this.getView().rstore.load()},addAccount:function(){let e=this,t=e.getView();Ext.create("Proxmox.window.ACMEAccountCreate",{autoShow:!0,acmeUrl:t.acmeUrl,taskDone:function(){e.reload();let t=e.lookup("accountselector");e.autoChangeAccount=!0,t.store.load()}})}},tbar:[{xtype:"proxmoxButton",text:gettext("Add"),handler:"addDomain",selModel:!1},{xtype:"proxmoxButton",text:gettext("Edit"),disabled:!0,handler:"editDomain"},{xtype:"proxmoxStdRemoveButton",handler:"removeDomain"},"-","order-menu","-",{xtype:"displayfield",value:gettext("Using Account")+":",bind:{hidden:"{!accountsAvailable}"}},{xtype:"displayfield",reference:"accounttext",renderer:e=>e||Proxmox.Utils.NoneText,bind:{value:"{account}",hidden:"{accountTextHidden}"}},{xtype:"pmxACMEAccountSelector",hidden:!0,reference:"accountselector",cbind:{url:"{accountUrl}"},bind:{value:"{account}",hidden:"{accountValueHidden}"}},{xtype:"button",iconCls:"fa black fa-pencil",baseCls:"x-plain",userCls:"pointer",bind:{iconCls:"{editBtnIcon}",hidden:"{!accountsAvailable}"},handler:"toggleEditAccount"},{xtype:"displayfield",value:gettext("No Account available."),bind:{hidden:"{accountsAvailable}"}},{xtype:"button",hidden:!0,reference:"accountlink",text:gettext("Add ACME Account"),bind:{hidden:"{accountsAvailable}"},handler:"addAccount"}],updateStore:function(e,t,i){let a,n=this,o=[];a=i&&t.length>0?t[0]:{data:{}},n.nodeconfig=a.data;let r="default";if(a.data.acme){let e=Proxmox.Utils.parseACME(a.data.acme);(e.domains||[]).forEach(e=>{if(""===e)return;let t={domain:e,type:"standalone",configkey:"acme"};o.push(t)}),e.account&&(r=e.account)}let l=n.getViewModel();l.get("account")===r||l.get("accountEditable")||(l.set("configaccount",r),n.lookup("accountselector").store.load());for(let e=0;e<Proxmox.Utils.acmedomain_count;e++){let t=a.data[`acmedomain${e}`];if(!t)continue;let i=Proxmox.Utils.parsePropertyString(t,"domain");i.type=i.plugin?"dns":"standalone",i.configkey=`acmedomain${e}`,o.push(i)}l.set("domaincount",o.length),n.store.loadData(o,!1)},listeners:{itemdblclick:"editDomain"},columns:[{dataIndex:"domain",flex:5,text:gettext("Domain")},{dataIndex:"usage",flex:1,text:gettext("Usage"),bind:{hidden:"{!hasUsage}"}},{dataIndex:"type",flex:1,text:gettext("Type")},{dataIndex:"plugin",flex:1,text:gettext("Plugin")}],initComponent:function(){let e=this;if(!e.acmeUrl)throw"no acmeUrl given";if(!e.url)throw"no url given";if(!e.nodename)throw"no nodename given";if(!e.domainUsages&&!e.orderUrl)throw"neither domainUsages nor orderUrl given";if(e.rstore=Ext.create("Proxmox.data.UpdateStore",{interval:1e4,autoStart:!0,storeid:`proxmox-node-domains-${e.nodename}`,proxy:{type:"proxmox",url:`/api2/json/${e.url}`}}),e.store=Ext.create("Ext.data.Store",{model:"proxmox-acme-domains",sorters:"domain"}),e.domainUsages){let t=[];for(const i of e.domainUsages){if(!i.name)throw"missing certificate url";if(!i.url)throw"missing certificate url";t.push({text:Ext.String.format("Order {0} Certificate Now",i.name),handler:function(){return e.getController().order(i)}})}e.tbar.splice(e.tbar.indexOf("order-menu"),1,{text:gettext("Order Certificates Now"),menu:{xtype:"menu",items:t}})}else e.tbar.splice(e.tbar.indexOf("order-menu"),1,{xtype:"button",reference:"order",text:gettext("Order Certificates Now"),bind:{disabled:"{!canOrder}"},handler:function(){return e.getController().order()}});e.callParent(),e.getViewModel().set("hasUsage",!!e.domainUsages),e.mon(e.rstore,"load","updateStore",e),Proxmox.Utils.monStoreErrors(e,e.rstore),e.on("destroy",e.rstore.stopUpdate,e.rstore)}}),Ext.define("Proxmox.panel.EmailRecipientPanel",{extend:"Ext.panel.Panel",xtype:"pmxEmailRecipientPanel",mixins:["Proxmox.Mixin.CBind"],border:!1,mailValidator:function(){let e=this.down("[name=mailto-user]"),t=this.down("[name=mailto]");return!(!e.getValue()?.length&&!t.getValue())||gettext("Either mailto or mailto-user must be set")},items:[{layout:"anchor",border:!1,cbind:{isCreate:"{isCreate}"},items:[{xtype:"pmxUserSelector",name:"mailto-user",multiSelect:!0,allowBlank:!0,editable:!1,skipEmptyText:!0,fieldLabel:gettext("Recipient(s)"),cbind:{deleteEmpty:"{!isCreate}"},validator:function(){return this.up("pmxEmailRecipientPanel").mailValidator()},autoEl:{tag:"div","data-qtip":gettext("The notification will be sent to the user's configured mail address")},listConfig:{width:600,columns:[{header:gettext("User"),sortable:!0,dataIndex:"userid",renderer:Ext.String.htmlEncode,flex:1},{header:gettext("E-Mail"),sortable:!0,dataIndex:"email",renderer:Ext.String.htmlEncode,flex:1},{header:gettext("Comment"),sortable:!1,dataIndex:"comment",renderer:Ext.String.htmlEncode,flex:1}]}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Additional Recipient(s)"),name:"mailto",allowBlank:!0,emptyText:"<EMAIL>, ...",cbind:{deleteEmpty:"{!isCreate}"},autoEl:{tag:"div","data-qtip":gettext("Multiple recipients must be separated by spaces, commas or semicolons")},validator:function(){return this.up("pmxEmailRecipientPanel").mailValidator()}}]}]}),Ext.define("Proxmox.panel.SendmailEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxSendmailEditPanel",mixins:["Proxmox.Mixin.CBind"],type:"sendmail",onlineHelp:"notification_targets_sendmail",mailValidator:function(){let e=this.down("[name=mailto-user]"),t=this.down("[name=mailto]");return!(!e.getValue()?.length&&!t.getValue())||gettext("Either mailto or mailto-user must be set")},items:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0},{xtype:"pmxEmailRecipientPanel",cbind:{isCreate:"{isCreate}"}},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],advancedItems:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Author"),name:"author",allowBlank:!0,cbind:{emptyText:"{defaultMailAuthor}",deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("From Address"),name:"from-address",allowBlank:!0,emptyText:gettext("Defaults to datacenter configuration, or root@$hostname"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:e=>(e.enable=!e.disable,delete e.disable,e),onGetValues:function(e){let t=this;return e.enable?t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,e.mailto&&(e.mailto=e.mailto.split(/[\s,;]+/)),e}}),Ext.define("Proxmox.panel.SmtpEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxSmtpEditPanel",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"notification_targets_smtp",type:"smtp",viewModel:{xtype:"viewmodel",cbind:{isCreate:"{isCreate}"},data:{mode:"tls",authentication:!0},formulas:{portEmptyText:function(e){let t;switch(e("mode")){case"insecure":t=25;break;case"starttls":t=587;break;case"tls":t=465}return`${Proxmox.Utils.defaultText} (${t})`},passwordEmptyText:function(e){let t=this.isCreate;return e("authentication")&&!t?gettext("Unchanged"):""}}},columnT:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0}],column1:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Server"),name:"server",allowBlank:!1,emptyText:gettext("mail.example.com")},{xtype:"proxmoxKVComboBox",name:"mode",fieldLabel:gettext("Encryption"),editable:!1,comboItems:[["insecure",Proxmox.Utils.noneText+" ("+gettext("insecure")+")"],["starttls","STARTTLS"],["tls","TLS"]],bind:"{mode}",cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxintegerfield",name:"port",fieldLabel:gettext("Port"),minValue:1,maxValue:65535,bind:{emptyText:"{portEmptyText}"},submitEmptyText:!1,cbind:{deleteEmpty:"{!isCreate}"}}],column2:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("Authenticate"),name:"authentication",bind:{value:"{authentication}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Username"),name:"username",allowBlank:!1,cbind:{deleteEmpty:"{!isCreate}"},bind:{disabled:"{!authentication}"}},{xtype:"proxmoxtextfield",inputType:"password",fieldLabel:gettext("Password"),name:"password",allowBlank:!0,bind:{disabled:"{!authentication}",emptyText:"{passwordEmptyText}"}}],columnB:[{xtype:"proxmoxtextfield",fieldLabel:gettext("From Address"),name:"from-address",allowBlank:!1,emptyText:gettext("<EMAIL>")},{xtype:"pmxEmailRecipientPanel",cbind:{isCreate:"{isCreate}"}},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],advancedColumnB:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Author"),name:"author",allowBlank:!0,cbind:{emptyText:"{defaultMailAuthor}",deleteEmpty:"{!isCreate}"}}],onGetValues:function(e){let t=this;return e.mailto&&(e.mailto=e.mailto.split(/[\s,;]+/)),e.authentication||t.isCreate||(Proxmox.Utils.assemble_field_data(e,{delete:"username"}),Proxmox.Utils.assemble_field_data(e,{delete:"password"})),e.enable?t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,delete e.authentication,e},onSetValues:function(e){return e.authentication=!!e.username,e.enable=!e.disable,delete e.disable,e}}),Ext.define("Proxmox.panel.StatusView",{extend:"Ext.panel.Panel",alias:"widget.pmxStatusView",layout:{type:"column"},title:gettext("Status"),getRecordValue:function(e,t){if(!e)throw"no key given";void 0===t&&(t=this.getStore());let i=t.getById(e);return i?i.data.value:""},fieldRenderer:function(e,t){return void 0===t?e:Ext.isNumeric(t)&&1!==t?Proxmox.Utils.render_size_usage(e,t):Proxmox.Utils.render_usage(e)},fieldCalculator:function(e,t){return!Ext.isNumeric(t)&&Ext.isNumeric(e)?e:Ext.isNumeric(e)||void 0===e.used||void 0===e.total?e/t:e.total>0?e.used/e.total:0},updateField:function(e){let t=this,i=t.fieldRenderer;if(Ext.isFunction(e.renderer)&&(i=e.renderer),!0===e.multiField)e.updateValue(i.call(e,t.getStore().getRecord()));else if(void 0!==e.textField)e.updateValue(i.call(e,t.getRecordValue(e.textField)));else if(void 0!==e.valueField){let a=t.getRecordValue(e.valueField),n=void 0!==e.maxField?t.getRecordValue(e.maxField):1,o=t.fieldCalculator;Ext.isFunction(e.calculate)&&(o=e.calculate),e.updateValue(i.call(e,a,n),o(a,n))}},getStore:function(){if(!this.rstore)throw"there is no rstore";return this.rstore},updateTitle:function(){this.setTitle(this.getRecordValue("name"))},updateValues:function(e,t,i){let a=this;i&&(a.query("pmxInfoWidget").forEach(a.updateField,a),a.query("pveInfoWidget").forEach(a.updateField,a),a.updateTitle(e))},initComponent:function(){let e=this;if(!e.rstore)throw"no rstore given";if(!e.title)throw"no title given";Proxmox.Utils.monStoreErrors(e,e.rstore),e.callParent(),e.mon(e.rstore,"load",e.updateValues,e)}}),Ext.define("pmx-tfa-users",{extend:"Ext.data.Model",fields:["userid"],idProperty:"userid",proxy:{type:"proxmox",url:"/api2/json/access/tfa"}}),Ext.define("pmx-tfa-entry",{extend:"Ext.data.Model",fields:["fullid","userid","type","description","created","enable"],idProperty:"fullid"}),Ext.define("Proxmox.panel.TfaView",{extend:"Ext.grid.GridPanel",alias:"widget.pmxTfaView",mixins:["Proxmox.Mixin.CBind"],title:gettext("Second Factors"),reference:"tfaview",issuerName:"Proxmox",yubicoEnabled:!1,cbindData:function(e){return{yubicoEnabled:this.yubicoEnabled}},store:{type:"diff",autoDestroy:!0,autoDestroyRstore:!0,model:"pmx-tfa-entry",rstore:{type:"store",proxy:"memory",storeid:"pmx-tfa-entry",model:"pmx-tfa-entry"}},controller:{xclass:"Ext.app.ViewController",init:function(e){e.tfaStore=Ext.create("Proxmox.data.UpdateStore",{autoStart:!0,interval:5e3,storeid:"pmx-tfa-users",model:"pmx-tfa-users"}),e.tfaStore.on("load",this.onLoad,this),e.on("destroy",e.tfaStore.stopUpdate),Proxmox.Utils.monStoreErrors(e,e.tfaStore)},reload:function(){this.getView().tfaStore.load()},onLoad:function(e,t,i){if(!i)return;let a=(new Date).getTime()/1e3,n=[];Ext.Array.each(t,e=>{let t=(e.data["tfa-locked-until"]||0)>a,i=e.data["totp-locked"];Ext.Array.each(e.data.entries,a=>{n.push({fullid:`${e.id}/${a.id}`,userid:e.id,type:a.type,description:a.description,created:a.created,enable:a.enable,locked:t||"totp"===a.type&&i})})});let o=this.getView().store.rstore;o.loadData(n),o.fireEvent("load",o,n,!0)},addTotp:function(){let e=this;Ext.create("Proxmox.window.AddTotp",{isCreate:!0,issuerName:e.getView().issuerName,listeners:{destroy:function(){e.reload()}}}).show()},addWebauthn:function(){let e=this;Ext.create("Proxmox.window.AddWebauthn",{isCreate:!0,autoShow:!0,listeners:{destroy:()=>e.reload()}})},addRecovery:async function(){let e=this;Ext.create("Proxmox.window.AddTfaRecovery",{autoShow:!0,listeners:{destroy:()=>e.reload()}})},addYubico:function(){let e=this;Ext.create("Proxmox.window.AddYubico",{isCreate:!0,autoShow:!0,listeners:{destroy:()=>e.reload()}})},editItem:function(){let e=this,t=e.getView().getSelection();1!==t.length||t[0].id.endsWith("/recovery")||Ext.create("Proxmox.window.TfaEdit",{"tfa-id":t[0].data.fullid,autoShow:!0,listeners:{destroy:()=>e.reload()}})},renderUser:e=>e.split("/")[0],renderEnabled:function(e,t,i){return i.data.locked?gettext("Locked"):void 0===e?Proxmox.Utils.yesText:Proxmox.Utils.format_boolean(e)},onRemoveButton:function(e,t,i){let a=this;Ext.create("Proxmox.tfa.confirmRemove",{...i.data,callback:e=>a.removeItem(e,i),autoShow:!0})},removeItem:async function(e,t){let i=this;e=null!==e?"?password="+encodeURIComponent(e):"";try{i.getView().mask(gettext("Please wait..."),"x-mask-loading"),await Proxmox.Async.api2({url:`/api2/extjs/access/tfa/${t.id}${e}`,method:"DELETE"}),i.reload()}catch(e){Ext.Msg.alert(gettext("Error"),e.result.message)}finally{i.getView().unmask()}}},viewConfig:{trackOver:!1},listeners:{itemdblclick:"editItem"},columns:[{header:gettext("User"),width:200,sortable:!0,dataIndex:"fullid",renderer:"renderUser"},{header:gettext("Enabled"),width:80,sortable:!0,dataIndex:"enable",renderer:"renderEnabled"},{header:gettext("TFA Type"),width:80,sortable:!0,dataIndex:"type"},{header:gettext("Created"),width:150,sortable:!0,dataIndex:"created",renderer:e=>e?Proxmox.Utils.render_timestamp(e):"N/A"},{header:gettext("Description"),width:300,sortable:!0,dataIndex:"description",renderer:Ext.String.htmlEncode,flex:1}],tbar:[{text:gettext("Add"),cbind:{},menu:{xtype:"menu",items:[{text:gettext("TOTP"),itemId:"totp",iconCls:"fa fa-fw fa-clock-o",handler:"addTotp"},{text:gettext("WebAuthn"),itemId:"webauthn",iconCls:"fa fa-fw fa-shield",handler:"addWebauthn"},{text:gettext("Recovery Keys"),itemId:"recovery",iconCls:"fa fa-fw fa-file-text-o",handler:"addRecovery"},{text:gettext("Yubico OTP"),itemId:"yubico",iconCls:"fa fa-fw fa-yahoo",handler:"addYubico",cbind:{hidden:"{!yubicoEnabled}"}}]}},"-",{xtype:"proxmoxButton",text:gettext("Edit"),handler:"editItem",enableFn:e=>!e.id.endsWith("/recovery"),disabled:!0},{xtype:"proxmoxButton",disabled:!0,text:gettext("Remove"),getRecordName:e=>e.data.description,handler:"onRemoveButton"}]}),Ext.define("Proxmox.panel.NotesView",{extend:"Ext.panel.Panel",xtype:"pmxNotesView",mixins:["Proxmox.Mixin.CBind"],title:gettext("Notes"),bodyPadding:10,scrollable:!0,animCollapse:!1,collapseFirst:!1,maxLength:65536,enableTBar:!1,onlineHelp:"markdown_basics",tbar:{itemId:"tbar",hidden:!0,items:[{text:gettext("Edit"),iconCls:"fa fa-pencil-square-o",handler:function(){this.up("panel").run_editor()}}]},cbindData:function(e){let t=this,i="";if(t.node)t.url=`/api2/extjs/nodes/${t.node}/config`;else if("root"===t.pveSelNode?.data?.id)t.url="/api2/extjs/cluster/options",i=t.pveSelNode?.data?.type;else{const e=t.pveSelNode?.data?.node;if(i=t.pveSelNode?.data?.type,!e)throw"no node name specified";if(!Ext.Array.contains(["node","qemu","lxc"],i))throw"invalid type specified";const a=t.pveSelNode?.data?.vmid;if(!a&&"node"!==i)throw"no VM ID specified";t.url=`/api2/extjs/nodes/${e}/`,"qemu"!==i&&"lxc"!==i||(t.url+=`${i}/${a}/`,t.maxLength=8192),t.url+="config"}return t.pveType=i,t.load(),{}},run_editor:function(){let e=this;Ext.create("Proxmox.window.NotesEdit",{url:e.url,onlineHelp:e.onlineHelp,listeners:{destroy:()=>e.load()},autoShow:!0}).setMaxLength(e.maxLength)},setNotes:function(e=""){let t=this,i=Proxmox.Markdown.parse(e);t.update(i),t.collapsible&&"auto"===t.collapseMode&&t.setCollapsed(!e)},load:function(){let e=this;Proxmox.Utils.API2Request({url:e.url,waitMsgTarget:e,failure:(t,i)=>{e.update(gettext("Error")+" "+t.htmlStatus),e.setCollapsed(!1)},success:({result:t})=>e.setNotes(t.data.description)})},listeners:{render:function(e){let t=this,i=Ext.state.Manager.getProvider();t.mon(i,"statechange",function(e,i,a){null!==a&&"edit-notes-on-double-click"===i&&(a?t.getEl().on("dblclick",t.run_editor,t):t.getEl().clearListeners())}),i.get("edit-notes-on-double-click",!1)&&t.getEl().on("dblclick",t.run_editor,t)},afterlayout:function(){let e=this;e.collapsible&&!e.getCollapsed()&&"always"===e.collapseMode&&(e.setCollapsed(!0),e.collapseMode="")}},tools:[{glyph:"xf044@FontAwesome",tooltip:gettext("Edit notes"),callback:e=>e.run_editor(),style:{paddingRight:"5px"}}],initComponent:function(){let e=this;if(e.callParent(),!0===e.enableTBar||"node"===e.pveType||""===e.pveType)e.down("#tbar").setVisible(!0);else if(1!==e.pveSelNode?.data?.template){e.setCollapsible(!0),e.collapseDirection="right";let t=Ext.state.Manager.getProvider();e.collapseMode=t.get("guest-notes-collapse","never"),"auto"===e.collapseMode&&e.setCollapsed(!0)}}}),Ext.define("Proxmox.panel.WebhookEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxWebhookEditPanel",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"notification_targets_webhook",type:"webhook",columnT:[],column1:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Endpoint Name"),regex:Proxmox.Utils.safeIdRegex,allowBlank:!1}],column2:[{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0}],columnB:[{xtype:"fieldcontainer",fieldLabel:gettext("Method/URL"),layout:"hbox",border:!1,margin:"0 0 5 0",items:[{xtype:"proxmoxKVComboBox",name:"method",editable:!1,value:"post",comboItems:[["post","POST"],["put","PUT"],["get","GET"]],width:80,margin:"0 5 0 0"},{xtype:"proxmoxtextfield",name:"url",allowBlank:!1,emptyText:"https://example.com/hook",regex:Proxmox.Utils.httpUrlRegex,regexText:gettext("Must be a valid URL"),flex:4}]},{xtype:"pmxWebhookKeyValueList",name:"header",fieldLabel:gettext("Headers"),addLabel:gettext("Add Header"),maskValues:!1,cbind:{isCreate:"{isCreate}"},margin:"0 0 10 0"},{xtype:"textarea",fieldLabel:gettext("Body"),name:"body",allowBlank:!0,minHeight:"150",fieldStyle:{"font-family":"monospace"},margin:"0 0 5 0"},{xtype:"pmxWebhookKeyValueList",name:"secret",fieldLabel:gettext("Secrets"),addLabel:gettext("Add Secret"),maskValues:!0,cbind:{isCreate:"{isCreate}"},margin:"0 0 10 0"},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:e=>(e.enable=!e.disable,e.body&&(e.body=Proxmox.Utils.base64ToUtf8(e.body)),delete e.disable,e),onGetValues:function(e){let t=this;return e.enable?t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,e.body?e.body=Proxmox.Utils.utf8ToBase64(e.body):(delete e.body,t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"body"})),Ext.isArray(e.header)&&!e.header.length&&(delete e.header,t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"header"})),Ext.isArray(e.secret)&&!e.secret.length&&(delete e.secret,t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"secret"})),delete e.enable,e}}),Ext.define("Proxmox.form.WebhookKeyValueList",{extend:"Ext.form.FieldContainer",alias:"widget.pmxWebhookKeyValueList",mixins:["Ext.form.field.Field"],fieldTitle:gettext("Item"),addLabel:void 0,maskRe:void 0,allowBlank:!0,selectAll:!1,isFormField:!0,deleteEmpty:!1,config:{deleteEmpty:!1,maskValues:!1},setValue:function(e){let t=this;e=Ext.isArray(e)?e:(e??"").split(";").filter(e=>""!==e);let i=t.lookup("grid").getStore();return e.length>0?i.setData(e.map(e=>{let i=Proxmox.Utils.parsePropertyString(e),a=t.maskValues?"":Proxmox.Utils.base64ToUtf8(i.value),n={headerName:i.name,headerValue:a};return!t.isCreate&&t.maskValues&&(n.emptyText=gettext("Unchanged")),n})):i.removeAll(),t.checkChange(),t},getValue:function(){let e=[];return this.lookup("grid").getStore().each(t=>{if(t.data.headerName){let i={name:t.data.headerName,value:Proxmox.Utils.utf8ToBase64(t.data.headerValue)};e.push(Proxmox.Utils.printPropertyString(i))}}),e},getErrors:function(e){let t=this,i=!1;return t.lookup("grid").getStore().each(e=>{e.data.headerName||(i=!0),!e.data.headerValue&&e.data.newValue&&(i=!0),e.data.headerValue||t.maskValues||(i=!0)}),i?[gettext("Name/value must not be empty.")]:[]},getSubmitData:function(){let e,t=this,i=null;return!t.disabled&&t.submitValue&&(e=t.getValue(),null!==e&&""!==e?(i={},i[t.getName()]=e):t.getDeleteEmpty()&&(i={},i.delete=t.getName())),i},controller:{xclass:"Ext.app.ViewController",addLine:function(){this.lookup("grid").getStore().add({headerName:"",headerValue:"",emptyText:gettext("Value"),newValue:!0})},removeSelection:function(e){let t=this.getView(),i=this.lookup("grid"),a=e.getWidgetRecord();void 0!==a&&(i.getStore().remove(a),t.checkChange(),t.validate())},itemChange:function(e,t){let i=e.getWidgetRecord();if(!i)return;let a=e.getWidgetColumn();i.set(a.dataIndex,t);let n=e.up("pmxWebhookKeyValueList");n.checkChange(),n.validate()},control:{"grid button":{click:"removeSelection"}}},initComponent:function(){let e=this,t=[{xtype:"grid",reference:"grid",minHeight:100,maxHeight:100,scrollable:"vertical",viewConfig:{deferEmptyText:!1},store:{listeners:{update:function(){this.commitChanges()}}},margin:"5 0 5 0",columns:[{header:e.fieldTtitle,dataIndex:"headerName",xtype:"widgetcolumn",widget:{xtype:"textfield",isFormField:!1,maskRe:e.maskRe,allowBlank:!1,queryMode:"local",emptyText:gettext("Key"),listeners:{change:"itemChange"}},onWidgetAttach:function(e,t){t.isValid()},flex:1},{header:e.fieldTtitle,dataIndex:"headerValue",xtype:"widgetcolumn",widget:{xtype:"proxmoxtextfield",inputType:e.maskValues?"password":"text",isFormField:!1,maskRe:e.maskRe,queryMode:"local",listeners:{change:"itemChange"},allowBlank:!e.isCreate&&e.maskValues,bind:{emptyText:"{record.emptyText}"}},onWidgetAttach:function(e,t){t.isValid()},flex:1},{xtype:"widgetcolumn",width:40,widget:{xtype:"button",iconCls:"fa fa-trash-o"}}]},{xtype:"button",text:e.addLabel?e.addLabel:gettext("Add"),iconCls:"fa fa-plus-circle",handler:"addLine"}];for(const[i,a]of Object.entries(e.gridConfig??{}))t[0][i]=a;Ext.apply(e,{items:t}),e.callParent(),e.initField()}}),Ext.define("Proxmox.window.Edit",{extend:"Ext.window.Window",alias:"widget.proxmoxWindowEdit",autoLoad:!1,autoLoadOptions:void 0,extraRequestParams:{},resizable:!1,subject:void 0,isCreate:!1,isAdd:!1,isRemove:!1,showReset:!0,submitText:void 0,submitOptions:{},backgroundDelay:0,submitUrl:Ext.identityFn,loadUrl:Ext.identityFn,referenceHolder:!0,defaultButton:"submitbutton",defaultFocus:"field:focusable[disabled=false][hidden=false]",showProgress:!1,showTaskViewer:!1,taskDone:Ext.emptyFn,apiCallDone:Ext.emptyFn,onlineHelp:void 0,constructor:function(e){let t=this;t.extraRequestParams=Object.assign({},t.extraRequestParams),t.submitOptions=Object.assign({},t.submitOptions),t.callParent(arguments)},isValid:function(){return this.formPanel.getForm().isValid()},getValues:function(e){let t=this,i={};return Ext.apply(i,t.extraRequestParams),t.formPanel.getForm().getFields().each(function(t){t.up("inputpanel")||e&&!t.isDirty()||Proxmox.Utils.assemble_field_data(i,t.getSubmitData())}),Ext.Array.each(t.query("inputpanel"),function(t){Proxmox.Utils.assemble_field_data(i,t.getValues(e))}),i},setValues:function(e){let t=this.formPanel.getForm(),i=t.getFields();Ext.iterate(e,function(e,a){i.filterBy(t=>(t.id===e||t.name===e||t.dataIndex===e)&&!t.up("inputpanel")).each(e=>{e.setValue(a),t.trackResetOnLoad&&e.resetOriginalValue()})}),Ext.Array.each(this.query("inputpanel"),function(t){t.setValues(e)})},setSubmitText:function(e){this.lookup("submitbutton").setText(e)},submit:function(){let e=this,t=e.formPanel.getForm(),i=e.getValues();Ext.Object.each(i,function(e,t){Object.prototype.hasOwnProperty.call(i,e)&&Ext.isArray(t)&&!t.length&&(i[e]="")}),e.digest&&(i.digest=e.digest),e.backgroundDelay&&(i.background_delay=e.backgroundDelay);let a=Ext.isFunction(e.submitUrl)?e.submitUrl(e.url,i):e.submitUrl||e.url;"DELETE"===e.method&&(a=a+"?"+Ext.Object.toQueryString(i),i=void 0);let n=Ext.apply({url:a,waitMsgTarget:e,method:e.method||(e.backgroundDelay?"POST":"PUT"),params:i,failure:function(i,a){e.apiCallDone(!1,i,a),i.result&&i.result.errors&&t.markInvalid(i.result.errors),Ext.Msg.alert(gettext("Error"),i.htmlStatus)},success:function(t,i){let a=(e.backgroundDelay||e.showProgress||e.showTaskViewer)&&t.result.data;if(e.apiCallDone(!0,t,i),a){e.hide();let i=t.result.data,a=e.showTaskViewer?"Viewer":"Progress";Ext.create("Proxmox.window.Task"+a,{autoShow:!0,upid:i,taskDone:e.taskDone,listeners:{destroy:function(){e.close()}}})}else e.close()}},e.submitOptions??{});Proxmox.Utils.API2Request(n)},load:function(e){let t=this,i=t.formPanel.getForm();e=e||{};let a=Ext.apply({waitMsgTarget:t},e);if(Object.keys(t.extraRequestParams).length>0){let e=a.params||{};Ext.applyIf(e,t.extraRequestParams),a.params=e}let n=Ext.isFunction(t.loadUrl)?t.loadUrl(t.url,t.initialConfig):t.loadUrl||t.url;var o;o=e.success,Ext.apply(a,{url:n,method:"GET",success:function(e,a){i.clearInvalid(),t.digest=e.result?.digest||e.result?.data?.digest,o?o(e,a):t.setValues(e.result.data),Ext.Array.each(t.query("radiofield"),e=>e.resetOriginalValue())},failure:function(e,i){Ext.Msg.alert(gettext("Error"),e.htmlStatus,function(){t.close()})}}),Proxmox.Utils.API2Request(a)},initComponent:function(){let e=this;if(!(e.url||e.submitUrl&&e.loadUrl&&e.submitUrl!==Ext.identityFn&&e.loadUrl!==Ext.identityFn))throw"neither 'url' nor both, submitUrl and loadUrl specified";if(e.create)throw"deprecated parameter, use isCreate";let t=Ext.isArray(e.items)?e.items:[e.items];e.items=void 0,e.formPanel=Ext.create("Ext.form.Panel",{url:e.url,method:e.method||"PUT",trackResetOnLoad:!0,bodyPadding:void 0!==e.bodyPadding?e.bodyPadding:10,border:!1,defaults:Ext.apply({},e.defaults,{border:!1}),fieldDefaults:Ext.apply({},e.fieldDefaults,{labelWidth:100,anchor:"100%"}),items:t});let i,a=e.formPanel.down("inputpanel"),n=e.formPanel.getForm();i=e.isCreate?e.submitText?e.submitText:e.isAdd?gettext("Add"):e.isRemove?gettext("Remove"):gettext("Create"):e.submitText||gettext("OK");let o=Ext.create("Ext.Button",{reference:"submitbutton",text:i,disabled:!e.isCreate,handler:function(){e.submit()}}),r=Ext.create("Ext.panel.Tool",{glyph:"xf0e2@FontAwesome",tooltip:gettext("Reset form data"),callback:()=>n.reset(),style:{paddingRight:"2px"},disabled:!0}),l=function(){let t=n.isValid(),i=n.isDirty();o.setDisabled(!t||!(i||e.isCreate)),r.setDisabled(!i)};n.on("dirtychange",l),n.on("validitychange",l);let s=300;e.fieldDefaults&&e.fieldDefaults.labelWidth&&(s+=e.fieldDefaults.labelWidth-100);let d=a&&(a.column1||a.column2);if(e.subject&&!e.title&&(e.title=Proxmox.Utils.dialog_title(e.subject,e.isCreate,e.isAdd)),e.buttons=[o],!e.isCreate&&e.showReset&&(e.tools=[r]),a&&a.hasAdvanced){let t=Ext.state.Manager.getProvider(),i=t.get("proxmox-advanced-cb");a.setAdvancedVisible(i),e.buttons.unshift({xtype:"proxmoxcheckbox",itemId:"advancedcb",boxLabelAlign:"before",boxLabel:gettext("Advanced"),stateId:"proxmox-advanced-cb",value:i,listeners:{change:function(e,i){a.setAdvancedVisible(i),t.set("proxmox-advanced-cb",i)}}})}let u=e.onlineHelp;if(!u&&a&&a.onlineHelp&&(u=a.onlineHelp),u){let t=Ext.create("Proxmox.button.Help");e.buttons.unshift(t,"->"),Ext.GlobalEvents.fireEvent("proxmoxShowHelp",u)}if(Ext.applyIf(e,{modal:!0,width:d?2*s:s,border:!1,items:[e.formPanel]}),e.callParent(),a?.hasAdvanced){a.down("#advancedContainer").query("field").forEach(function(t){e.mon(t,"validitychange",(e,t)=>{t||e.up("inputpanel").setAdvancedVisible(!0)})})}e.on("afterlayout",function(){e.suspendLayout=!0,e.isValid(),e.suspendLayout=!1}),e.autoLoad&&e.load(e.autoLoadOptions)}}),Ext.define("Proxmox.window.PasswordEdit",{extend:"Proxmox.window.Edit",alias:"proxmoxWindowPasswordEdit",mixins:["Proxmox.Mixin.CBind"],subject:gettext("Password"),url:"/api2/extjs/access/password",width:380,fieldDefaults:{labelWidth:150},minLength:5,confirmCurrentPassword:!1,hintHtml:void 0,items:[{xtype:"textfield",inputType:"password",fieldLabel:gettext("Your Current Password"),reference:"confirmation-password",name:"confirmation-password",allowBlank:!1,vtype:"password",cbind:{hidden:"{!confirmCurrentPassword}",disabled:"{!confirmCurrentPassword}"}},{xtype:"textfield",inputType:"password",fieldLabel:gettext("New Password"),allowBlank:!1,name:"password",listeners:{change:e=>e.next().validate(),blur:e=>e.next().validate()},cbind:{minLength:"{minLength}"}},{xtype:"textfield",inputType:"password",fieldLabel:gettext("Confirm New Password"),name:"verifypassword",allowBlank:!1,vtype:"password",initialPassField:"password",submitValue:!1},{xtype:"component",userCls:"pmx-hint",name:"password-hint",hidden:!0,cbind:{html:"{hintHtml}",hidden:"{!hintHtml}"}},{xtype:"hiddenfield",name:"userid",cbind:{value:"{userid}"}}]}),Ext.define("Proxmox.window.SafeDestroy",{extend:"Ext.window.Window",alias:"widget.proxmoxSafeDestroy",title:gettext("Confirm"),modal:!0,buttonAlign:"center",bodyPadding:10,width:450,layout:{type:"hbox"},defaultFocus:"confirmField",showProgress:!1,additionalItems:[],taskDone:Ext.emptyFn,apiCallDone:Ext.emptyFn,config:{item:{id:void 0,formattedIdentifier:void 0},url:void 0,note:void 0,taskName:void 0,params:{}},getParams:function(){return Ext.Object.isEmpty(this.params)?"":"?"+Ext.Object.toQueryString(this.params)},controller:{xclass:"Ext.app.ViewController",control:{"field[name=confirm]":{change:function(e,t){const i=this.getView(),a=this.lookupReference("removeButton");t===i.getItem().id.toString()?a.enable():a.disable()},specialkey:function(e,t){const i=this.lookupReference("removeButton");i.isDisabled()||t.getKey()!==t.ENTER||i.fireEvent("click",i,t)}},"button[reference=removeButton]":{click:function(){const e=this.getView();Proxmox.Utils.API2Request({url:e.getUrl()+e.getParams(),method:"DELETE",waitMsgTarget:e,failure:function(t,i){e.apiCallDone(!1,t,i),e.close(),Ext.Msg.alert("Error",t.htmlStatus)},success:function(t,i){const a=!(!e.showProgress||!t.result.data);if(e.apiCallDone(!0,t,i),a){e.hide();const i=t.result.data;Ext.create("Proxmox.window.TaskProgress",{upid:i,taskDone:e.taskDone,listeners:{destroy:function(){e.close()}}}).show()}else e.close()}})}}}},buttons:[{reference:"removeButton",text:gettext("Remove"),disabled:!0}],initComponent:function(){let e=this;e.items=[{xtype:"component",cls:[Ext.baseCSSPrefix+"message-box-icon",Ext.baseCSSPrefix+"message-box-warning",Ext.baseCSSPrefix+"dlg-icon"]},{xtype:"container",flex:1,layout:{type:"vbox",align:"stretch"},items:[{xtype:"component",reference:"messageCmp"},{itemId:"confirmField",reference:"confirmField",xtype:"textfield",name:"confirm",labelWidth:300,hideTrigger:!0,allowBlank:!1}].concat(e.additionalItems).concat([{xtype:"container",reference:"noteContainer",flex:1,hidden:!0,layout:{type:"vbox"},items:[{xtype:"component",reference:"noteCmp",userCls:"pmx-hint"}]}])}],e.callParent();const t=e.getItem().id;if(!Ext.isDefined(t))throw"no ID specified";if(Ext.isDefined(e.getNote())){e.lookupReference("noteCmp").setHtml(`<span title="${e.getNote()}">${e.getNote()}</span>`);const t=e.lookupReference("noteContainer");t.setHidden(!1),t.setDisabled(!1)}let i=e.getTaskName();if(!Ext.isDefined(i))throw"no task name specified";e.lookupReference("messageCmp").setHtml(Ext.htmlEncode(Proxmox.Utils.format_task_description(i,e.getItem().formattedIdentifier??t)));let a=`${gettext("Please enter the ID to confirm")} (${t})`;e.lookupReference("confirmField").setFieldLabel(Ext.htmlEncode(a))}}),Ext.define("Proxmox.window.PackageVersions",{extend:"Ext.window.Window",alias:"widget.proxmoxPackageVersions",title:gettext("Package versions"),width:600,height:650,layout:"fit",modal:!0,url:"/nodes/localhost/apt/versions",viewModel:{parent:null,data:{packageList:""}},buttons:[{xtype:"button",text:gettext("Copy"),iconCls:"fa fa-clipboard",handler:function(e){window.getSelection().selectAllChildren(document.getElementById("pkgversions")),document.execCommand("copy")}},{text:gettext("Ok"),handler:function(){this.up("window").close()}}],items:[{xtype:"component",autoScroll:!0,id:"pkgversions",padding:5,bind:{html:"{packageList}"},style:{"white-space":"pre","font-family":"monospace"}}],listeners:{afterrender:function(){this.loadPackageVersions()}},loadPackageVersions:async function(){let e=this,{result:t}=await Proxmox.Async.api2({waitMsgTarget:e.down('component[id="pkgversions"]'),method:"GET",url:e.url}).catch(Proxmox.Utils.alertResponseFailure),i="";for(const e of t.data){let t="not correctly installed";e.OldVersion&&"unknown"!==e.OldVersion?t=e.OldVersion:"ConfigFiles"===e.CurrentState&&(t="residual config");const a=e.Package;e.ExtraInfo?i+=`${a}: ${t} (${e.ExtraInfo})\n`:i+=`${a}: ${t}\n`}e.getViewModel().set("packageList",Ext.htmlEncode(i))}}),Ext.define("Proxmox.window.TaskProgress",{extend:"Ext.window.Window",alias:"widget.proxmoxTaskProgress",taskDone:Ext.emptyFn,width:300,layout:"auto",modal:!0,bodyPadding:5,initComponent:function(){let e=this;if(!e.upid)throw"no task specified";let t=Proxmox.Utils.parse_task_upid(e.upid),i=Ext.create("Proxmox.data.ObjectStore",{url:`/api2/json/nodes/${t.node}/tasks/${encodeURIComponent(e.upid)}/status`,interval:1e3,rows:{status:{defaultValue:"unknown"},exitstatus:{defaultValue:"unknown"}}});e.on("destroy",i.stopUpdate);let a=function(e,t){let a=i.getById(e);return a?a.data.value:t},n=Ext.create("Ext.ProgressBar");e.mon(i,"load",function(){if("stopped"===a("status")){let t=a("exitstatus");"OK"===t?(n.reset(),n.updateText("Done!"),Ext.Function.defer(e.close,1e3,e)):(e.close(),Ext.Msg.alert("Task failed",Ext.htmlEncode(t))),e.taskDone("OK"===t)}});let o=Ext.htmlEncode(Proxmox.Utils.format_task_description(t.type,t.id));Ext.apply(e,{title:gettext("Task")+": "+o,items:n,buttons:[{text:gettext("Details"),handler:function(){Ext.create("Proxmox.window.TaskViewer",{autoShow:!0,taskDone:e.taskDone,upid:e.upid}),e.close()}}]}),e.callParent(),i.startUpdate(),n.wait({text:gettext("running...")})}}),Ext.define("Proxmox.window.TaskViewer",{extend:"Ext.window.Window",alias:"widget.proxmoxTaskViewer",extraTitle:"",taskDone:Ext.emptyFn,initComponent:function(){let e=this;if(!e.upid)throw"no task specified";let t,i=Proxmox.Utils.parse_task_upid(e.upid),a={status:{header:gettext("Status"),defaultValue:"unknown",renderer:function(e){if("stopped"!==e)return Ext.htmlEncode(e);let i=t.getObjectValue("exitstatus");return i?Ext.htmlEncode(`${e}: ${i}`):"unknown"}},exitstatus:{visible:!1,renderer:Ext.String.htmlEncode},type:{header:gettext("Task type"),required:!0,renderer:Ext.String.htmlEncode},user:{header:gettext("User name"),renderer:function(e){let i=e,a=t.getObjectValue("tokenid");return a&&(i+=`!${a} (API Token)`),Ext.String.htmlEncode(i)},required:!0},tokenid:{header:gettext("API Token"),renderer:Ext.String.htmlEncode,visible:!1},node:{header:gettext("Node"),required:!0,renderer:Ext.String.htmlEncode},pid:{header:gettext("Process ID"),required:!0,renderer:Ext.String.htmlEncode},task_id:{header:gettext("Task ID"),renderer:Ext.String.htmlEncode},starttime:{header:gettext("Start Time"),required:!0,renderer:Proxmox.Utils.render_timestamp},upid:{header:gettext("Unique task ID"),renderer:Ext.String.htmlEncode}};e.endtime&&("object"==typeof e.endtime&&(e.endtime=parseInt(e.endtime.getTime()/1e3,10)),a.endtime={header:gettext("End Time"),required:!0,renderer:function(){return Proxmox.Utils.render_timestamp(e.endtime)}}),a.duration={header:gettext("Duration"),required:!0,renderer:function(){let i=t.getObjectValue("starttime"),a=(e.endtime||Date.now()/1e3)-i;return Proxmox.Utils.format_duration_human(a)}};let n=Ext.create("Proxmox.data.ObjectStore",{url:`/api2/json/nodes/${i.node}/tasks/${encodeURIComponent(e.upid)}/status`,interval:1e3,rows:a});e.on("destroy",n.stopUpdate);let o=function(){Proxmox.Utils.API2Request({url:`/nodes/${i.node}/tasks/${encodeURIComponent(e.upid)}`,waitMsgTarget:e,method:"DELETE",failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})},r=new Ext.Button({text:gettext("Stop"),disabled:!0,handler:o}),l=new Ext.Button({text:gettext("Stop"),disabled:!0,handler:o});t=Ext.create("Proxmox.grid.ObjectGrid",{title:gettext("Status"),layout:"fit",tbar:[r],rstore:n,rows:a,border:!1});let s=new Ext.Button({text:gettext("Download"),iconCls:"fa fa-download",handler:()=>Proxmox.Utils.downloadAsFile(`/api2/json/nodes/${i.node}/tasks/${encodeURIComponent(e.upid)}/log?download=1`)}),d=Ext.create("Proxmox.panel.LogView",{title:gettext("Output"),tbar:[l,"->",s],border:!1,url:`/api2/extjs/nodes/${i.node}/tasks/${encodeURIComponent(e.upid)}/log`});e.mon(n,"load",function(){let i=t.getObjectValue("status");"stopped"===i&&(d.scrollToEnd=!1,d.requestUpdate(),n.stopUpdate(),e.taskDone("OK"===t.getObjectValue("exitstatus"))),r.setDisabled("running"!==i),l.setDisabled("running"!==i),s.setDisabled("running"===i)}),n.startUpdate(),Ext.apply(e,{title:Ext.htmlEncode("Task viewer: "+i.desc+e.extraTitle),width:800,height:500,layout:"fit",modal:!0,items:[{xtype:"tabpanel",region:"center",items:[d,t]}]}),e.callParent(),d.fireEvent("show",d)}}),Ext.define("Proxmox.window.LanguageEditWindow",{extend:"Ext.window.Window",alias:"widget.pmxLanguageEditWindow",viewModel:{parent:null,data:{language:"__default__"}},controller:{xclass:"Ext.app.ViewController",init:function(e){let t=Ext.util.Cookies.get(e.cookieName)||"__default__";if("kr"===t){t="ko";let i=Ext.Date.add(new Date,Ext.Date.YEAR,10);Ext.util.Cookies.set(e.cookieName,t,i)}this.getViewModel().set("language",t)},applyLanguage:function(e){let t=this.getView(),i=this.getViewModel(),a=Ext.Date.add(new Date,Ext.Date.YEAR,10);Ext.util.Cookies.set(t.cookieName,i.get("language"),a),t.mask(gettext("Please wait..."),"x-mask-loading"),window.location.reload()}},cookieName:"PVELangCookie",title:gettext("Language"),modal:!0,bodyPadding:10,resizable:!1,items:[{xtype:"proxmoxLanguageSelector",fieldLabel:gettext("Language"),labelWidth:75,bind:{value:"{language}"}}],buttons:[{text:gettext("Apply"),handler:"applyLanguage"}]}),Ext.define("Proxmox.window.DiskSmart",{extend:"Ext.window.Window",alias:"widget.pmxSmartWindow",modal:!0,layout:{type:"fit"},width:800,height:500,minWidth:400,minHeight:300,bodyPadding:5,items:[{xtype:"gridpanel",layout:{type:"fit"},emptyText:gettext("No S.M.A.R.T. Values"),scrollable:!0,flex:1,itemId:"smartGrid",reserveScrollbar:!0,columns:[{text:"ID",dataIndex:"id",width:50,align:"right"},{text:gettext("Attribute"),dataIndex:"name",flex:1,renderer:Ext.String.htmlEncode},{text:gettext("Value"),dataIndex:"real-value",renderer:Ext.String.htmlEncode},{text:gettext("Normalized"),dataIndex:"real-normalized",width:60,align:"right"},{text:gettext("Threshold"),dataIndex:"threshold",width:60,align:"right"},{text:gettext("Worst"),dataIndex:"worst",width:60,align:"right"},{text:gettext("Flags"),dataIndex:"flags"},{text:gettext("Failing"),dataIndex:"fail",renderer:Ext.String.htmlEncode}]},{xtype:"component",itemId:"smartPlainText",hidden:!0,autoScroll:!0,padding:5,style:{"white-space":"pre","font-family":"monospace"}}],buttons:[{text:gettext("Reload"),name:"reload",handler:function(){this.up("window").store.reload()}},{text:gettext("Close"),name:"close",handler:function(){this.up("window").close()}}],initComponent:function(){let e=this;if(!e.baseurl)throw"no baseurl specified";if(!e.dev)throw"no device specified";e.title=`${gettext("S.M.A.R.T. Values")} (${e.dev})`,e.store=Ext.create("Ext.data.Store",{model:"pmx-disk-smart",proxy:{type:"proxmox",url:`${e.baseurl}/smart?disk=${e.dev}`}}),e.callParent();let t=e.down("#smartGrid"),i=e.down("#smartPlainText");Proxmox.Utils.monStoreErrors(t,e.store),e.mon(e.store,"load",function(e,a,n){if(!n||a.length<=0)return;let o="text"===a[0].data.type;o?i.setHtml(Ext.String.htmlEncode(a[0].data.text)):t.setStore(a[0].attributes()),t.setVisible(!o),i.setVisible(o)}),e.store.load()}},function(){Ext.define("pmx-disk-smart",{extend:"Ext.data.Model",fields:[{name:"health"},{name:"type"},{name:"text"}],hasMany:{model:"pmx-smart-attribute",name:"attributes"}}),Ext.define("pmx-smart-attribute",{extend:"Ext.data.Model",fields:[{name:"id",type:"number"},"name","value","worst","threshold","flags","fail","raw","normalized",{name:"real-value",calculate:e=>e.raw??e.value},{name:"real-normalized",calculate:e=>e.normalized??e.value}],idProperty:"name"})}),Ext.define("Proxmox.window.ZFSDetail",{extend:"Ext.window.Window",alias:"widget.pmxZFSDetail",mixins:["Proxmox.Mixin.CBind"],cbindData:function(e){let t=this;return t.url=`/nodes/${t.nodename}/disks/zfs/${encodeURIComponent(t.zpool)}`,{zpoolUri:`/api2/json/${t.url}`,title:`${gettext("Status")}: ${t.zpool}`}},controller:{xclass:"Ext.app.ViewController",reload:function(){let e=this,t=e.getView();e.lookup("status").reload(),Proxmox.Utils.API2Request({url:`/api2/extjs/${t.url}`,waitMsgTarget:t,method:"GET",failure:function(e,i){Proxmox.Utils.setErrorMask(t,e.htmlStatus)},success:function(t,i){let a=e.lookup("devices");a.getSelectionModel().deselectAll(),a.setRootNode(t.result.data),a.expandAll()}})},init:function(e){let t=this;Proxmox.Utils.monStoreErrors(t,t.lookup("status").getStore().rstore),t.reload()}},modal:!0,width:800,height:600,resizable:!0,cbind:{title:"{title}"},layout:{type:"vbox",align:"stretch"},defaults:{layout:"fit",border:!1},tbar:[{text:gettext("Reload"),iconCls:"fa fa-refresh",handler:"reload"}],items:[{xtype:"proxmoxObjectGrid",reference:"status",flex:0,cbind:{url:"{zpoolUri}",nodename:"{nodename}"},rows:{state:{header:gettext("Health"),renderer:Proxmox.Utils.render_zfs_health},scan:{header:gettext("Scan")},status:{header:gettext("Status")},action:{header:gettext("Action")},errors:{header:gettext("Errors")}}},{xtype:"treepanel",reference:"devices",title:gettext("Devices"),stateful:!0,stateId:"grid-node-zfsstatus",rootVisible:!1,fields:["name","status",{type:"string",name:"iconCls",calculate:function(e){if(e.leaf)return"fa x-fa-tree fa-hdd-o"}}],sorters:"name",flex:1,cbind:{zpool:"{zpoolUri}",nodename:"{nodename}"},columns:[{xtype:"treecolumn",text:gettext("Name"),dataIndex:"name",flex:1},{text:gettext("Health"),renderer:Proxmox.Utils.render_zfs_health,dataIndex:"state"},{text:"READ",dataIndex:"read"},{text:"WRITE",dataIndex:"write"},{text:"CKSUM",dataIndex:"cksum"},{text:gettext("Message"),dataIndex:"msg"}]}]}),Ext.define("Proxmox.window.CertificateViewer",{extend:"Proxmox.window.Edit",xtype:"pmxCertViewer",title:gettext("Certificate"),fieldDefaults:{labelWidth:120},width:800,resizable:!0,items:[{xtype:"displayfield",fieldLabel:gettext("Name"),name:"filename"},{xtype:"displayfield",fieldLabel:gettext("Fingerprint"),name:"fingerprint"},{xtype:"displayfield",fieldLabel:gettext("Issuer"),name:"issuer"},{xtype:"displayfield",fieldLabel:gettext("Subject"),name:"subject"},{xtype:"displayfield",fieldLabel:gettext("Public Key Type"),name:"public-key-type"},{xtype:"displayfield",fieldLabel:gettext("Public Key Size"),name:"public-key-bits"},{xtype:"displayfield",fieldLabel:gettext("Valid Since"),renderer:Proxmox.Utils.render_timestamp,name:"notbefore"},{xtype:"displayfield",fieldLabel:gettext("Expires"),renderer:Proxmox.Utils.render_timestamp,name:"notafter"},{xtype:"displayfield",fieldLabel:gettext("Subject Alternative Names"),name:"san",renderer:Proxmox.Utils.render_san},{xtype:"textarea",editable:!1,grow:!0,growMax:200,fieldLabel:gettext("Certificate"),name:"pem"}],initComponent:function(){var e=this;if(!e.cert)throw"no cert given";if(!e.url)throw"no url given";e.callParent(),e.down("toolbar[dock=bottom]").setVisible(!1),e.load({success:function(t){Ext.isArray(t.result.data)&&Ext.Array.each(t.result.data,function(t){return t.filename!==e.cert||(e.setValues(t),!1)})}})}}),Ext.define("Proxmox.window.CertificateUpload",{extend:"Proxmox.window.Edit",xtype:"pmxCertUpload",title:gettext("Upload Custom Certificate"),resizable:!1,isCreate:!0,submitText:gettext("Upload"),method:"POST",width:600,reloadUi:void 0,apiCallDone:function(e,t,i){e&&this.reloadUi&&(Ext.getBody().mask(gettext("API server will be restarted to use new certificates, please reload web-interface!"),["pve-static-mask"]),Ext.defer(()=>window.location.reload(!0),1e4))},items:[{fieldLabel:gettext("Private Key (Optional)"),labelAlign:"top",emptyText:gettext("No change"),name:"key",xtype:"textarea"},{xtype:"filebutton",text:gettext("From File"),listeners:{change:function(e,t,i){let a=this.up("form");t=t.event,Ext.Array.each(t.target.files,function(e){Proxmox.Utils.loadTextFromFile(e,function(e){a.down("field[name=key]").setValue(e)},16384)}),e.reset()}}},{xtype:"box",autoEl:"hr"},{fieldLabel:gettext("Certificate Chain"),labelAlign:"top",allowBlank:!1,name:"certificates",xtype:"textarea"},{xtype:"filebutton",text:gettext("From File"),listeners:{change:function(e,t,i){let a=this.up("form");t=t.event,Ext.Array.each(t.target.files,function(e){Proxmox.Utils.loadTextFromFile(e,function(e){a.down("field[name=certificates]").setValue(e)},16384)}),e.reset()}}},{xtype:"hidden",name:"restart",value:"1"},{xtype:"hidden",name:"force",value:"1"}],initComponent:function(){if(!this.url)throw"neither url given";this.callParent()}}),Ext.define("Proxmox.window.ConsentModal",{extend:"Ext.window.Window",alias:["widget.pmxConsentModal"],mixins:["Proxmox.Mixin.CBind"],maxWidth:1e3,maxHeight:1e3,minWidth:600,minHeight:400,scrollable:!0,modal:!0,closable:!1,resizable:!1,alwaysOnTop:!0,title:gettext("Consent"),items:[{xtype:"displayfield",padding:10,scrollable:!0,cbind:{value:"{consent}"}}],buttons:[{handler:function(){this.up("window").close()},text:gettext("OK")}]}),Ext.define("Proxmox.window.ACMEAccountCreate",{extend:"Proxmox.window.Edit",mixins:["Proxmox.Mixin.CBind"],xtype:"pmxACMEAccountCreate",acmeUrl:void 0,width:450,title:gettext("Register Account"),isCreate:!0,method:"POST",submitText:gettext("Register"),showTaskViewer:!0,defaultExists:!1,items:[{xtype:"proxmoxtextfield",fieldLabel:gettext("Account Name"),name:"name",cbind:{emptyText:e=>e("defaultExists")?"":"default",allowBlank:e=>!e("defaultExists")}},{xtype:"textfield",name:"contact",vtype:"email",allowBlank:!1,fieldLabel:gettext("E-Mail")},{xtype:"proxmoxComboGrid",name:"directory",reference:"directory",allowBlank:!1,valueField:"url",displayField:"name",fieldLabel:gettext("ACME Directory"),store:{autoLoad:!0,fields:["name","url"],idProperty:["name"],proxy:{type:"proxmox"},sorters:{property:"name",direction:"ASC"}},listConfig:{columns:[{header:gettext("Name"),dataIndex:"name",flex:1},{header:gettext("URL"),dataIndex:"url",flex:1}]},listeners:{change:function(e,t){let i=this;if(!t)return;let a=i.up("window").acmeUrl,n=i.up("window").down("#tos_url_display"),o=i.up("window").down("#tos_url"),r=i.up("window").down("#tos_checkbox");n.setValue(gettext("Loading")),o.setValue(void 0),r.setValue(void 0),r.setHidden(!0),Proxmox.Utils.API2Request({url:`${a}/tos`,method:"GET",params:{directory:t},success:function(e,t){o.setValue(e.result.data),n.setValue(e.result.data),r.setHidden(!1)},failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus)}})}}},{xtype:"displayfield",itemId:"tos_url_display",renderer:Proxmox.Utils.render_optional_url,name:"tos_url_display"},{xtype:"hidden",itemId:"tos_url",name:"tos_url"},{xtype:"proxmoxcheckbox",itemId:"tos_checkbox",boxLabel:gettext("Accept TOS"),submitValue:!1,validateValue:function(e){return!(!e||!this.checked)}}],initComponent:function(){let e=this;if(!e.acmeUrl)throw"no acmeUrl given";e.url=`${e.acmeUrl}/account`,e.callParent(),e.lookup("directory").store.proxy.setUrl(`/api2/json/${e.acmeUrl}/directories`)}}),Ext.define("Proxmox.window.ACMEAccountView",{extend:"Proxmox.window.Edit",xtype:"pmxACMEAccountView",width:600,fieldDefaults:{labelWidth:140},title:gettext("Account"),items:[{xtype:"displayfield",fieldLabel:gettext("E-Mail"),name:"email"},{xtype:"displayfield",fieldLabel:gettext("Created"),name:"createdAt"},{xtype:"displayfield",fieldLabel:gettext("Status"),name:"status"},{xtype:"displayfield",fieldLabel:gettext("Directory"),renderer:Proxmox.Utils.render_optional_url,name:"directory"},{xtype:"displayfield",fieldLabel:gettext("Terms of Services"),renderer:Proxmox.Utils.render_optional_url,name:"tos"}],initComponent:function(){var e=this;e.callParent(),e.down("toolbar[dock=bottom]").setVisible(!1),e.load({success:function(t){var i=t.result.data;i.email=i.account.contact[0],i.createdAt=i.account.createdAt,i.status=i.account.status,e.setValues(i)}})}}),Ext.define("Proxmox.window.ACMEPluginEdit",{extend:"Proxmox.window.Edit",xtype:"pmxACMEPluginEdit",mixins:["Proxmox.Mixin.CBind"],isAdd:!0,isCreate:!1,width:550,acmeUrl:void 0,subject:"ACME DNS Plugin",cbindData:function(e){return{challengeSchemaUrl:`/api2/json/${this.acmeUrl}/challenge-schema`}},items:[{xtype:"inputpanel",createdFields:{},createdInitially:!1,originalValues:{},createSchemaFields:function(e){let t=this,i=t.down("container"),a=t.down("field[name=data]"),n=t.down("field[name=hint]");t.createdInitially||([t.originalValues]=Proxmox.Utils.parseACMEPluginData(a.getValue()));let o=[];for(const[e,a]of Object.entries(t.createdFields)){let t=a.getValue();null!=t&&""!==t&&o.push(`${e}=${t}`),i.remove(a)}let r=a.getValue();null!=r&&""!==r&&o.push(r),a.setValue(o.join("\n")),t.createdFields={},"object"!=typeof e.fields&&(e.fields={});let l=!1;for(const[a,n]of Object.entries(e.fields).sort((e,t)=>e[0].localeCompare(t[0]))){let e;switch(n.type){case"string":e="proxmoxtextfield";break;case"integer":e="proxmoxintegerfield";break;case"number":e="numberfield";break;default:console.warn(`unknown type '${n.type}'`),e="proxmoxtextfield"}let o=a;"string"==typeof n.name&&(o=n.name);let r=Ext.create({xtype:e,name:`custom_${a}`,fieldLabel:Ext.htmlEncode(o),width:"100%",labelWidth:150,labelSeparator:"=",emptyText:n.default||"",autoEl:n.description?{tag:"div","data-qtip":Ext.htmlEncode(Ext.htmlEncode(n.description))}:void 0});t.createdFields[a]=r,i.add(r),l=!0}a.setHidden(l),e.description?(n.setValue(e.description),n.setHidden(!1)):(n.setValue(""),n.setHidden(!0));let s=[];[o,s]=Proxmox.Utils.parseACMEPluginData(a.getValue());for(const[e,i]of Object.entries(o))t.createdFields[e]?(t.createdFields[e].setValue(i),t.createdFields[e].originalValue=t.originalValues[e],t.createdFields[e].checkDirty()):s.push(`${e}=${i}`);a.setValue(s.join("\n")),t.createdInitially||(a.resetOriginalValue(),t.createdInitially=!0)},onGetValues:function(e){let t=this,i=t.up("pmxACMEPluginEdit");i.isCreate&&(e.id=e.plugin,e.type="dns"),delete e.plugin,Proxmox.Utils.delete_if_default(e,"validation-delay","30",i.isCreate);let a="";for(const[i,n]of Object.entries(t.createdFields)){let t=n.getValue();null!=t&&""!==t&&(a+=`${i}=${t}\n`),delete e[`custom_${i}`]}return e.data=Ext.util.Base64.encode(a+e.data),e},items:[{xtype:"pmxDisplayEditField",cbind:{editable:e=>e("isCreate"),submitValue:e=>e("isCreate")},editConfig:{flex:1,xtype:"proxmoxtextfield",allowBlank:!1},name:"plugin",labelWidth:150,fieldLabel:gettext("Plugin ID")},{xtype:"proxmoxintegerfield",name:"validation-delay",labelWidth:150,fieldLabel:gettext("Validation Delay"),emptyText:30,cbind:{deleteEmpty:"{!isCreate}"},minValue:0,maxValue:172800},{xtype:"pmxACMEApiSelector",name:"api",labelWidth:150,cbind:{url:"{challengeSchemaUrl}"},listeners:{change:function(e){let t=e.getSchema();e.up("inputpanel").createSchemaFields(t)}}},{xtype:"textarea",fieldLabel:gettext("API Data"),labelWidth:150,name:"data"},{xtype:"displayfield",fieldLabel:gettext("Hint"),labelWidth:150,name:"hint",hidden:!0}]}],initComponent:function(){var e=this;if(!e.acmeUrl)throw"no acmeUrl given";e.callParent(),e.isCreate?e.method="POST":e.load({success:function(t,i){e.setValues(t.result.data)}})}}),Ext.define("Proxmox.window.ACMEDomainEdit",{extend:"Proxmox.window.Edit",xtype:"pmxACMEDomainEdit",mixins:["Proxmox.Mixin.CBind"],subject:gettext("Domain"),isCreate:!1,width:450,acmeUrl:void 0,url:void 0,domainUsages:void 0,separateDomainEntries:void 0,cbindData:function(e){return{pluginsUrl:`/api2/json/${this.acmeUrl}/plugins`,hasUsage:!!this.domainUsages}},items:[{xtype:"inputpanel",onGetValues:function(e){let t=this.up("pmxACMEDomainEdit"),i=t.nodeconfig,a=t.domain||{},n={digest:i.digest},o=a.configkey,r=Proxmox.Utils.parseACME(i.acme),l=()=>{for(let e=0;e<Proxmox.Utils.acmedomain_count;e++)if(void 0===i[`acmedomain${e}`])return`acmedomain${e}`;throw"too many domains configured"};return t.separateDomainEntries||t.domainUsages?(o&&"acme"!==o||(o=l()),delete e.type,n[o]=Proxmox.Utils.printPropertyString(e,"domain"),n):("dns"===e.type?(a.configkey&&"acme"!==a.configkey||(o=l(),a.domain&&(Proxmox.Utils.remove_domain_from_acme(r,a.domain),n.acme=Proxmox.Utils.printACME(r))),delete e.type,n[o]=Proxmox.Utils.printPropertyString(e,"domain")):(a.configkey&&"acme"!==a.configkey&&(n.delete=[a.configkey]),Proxmox.Utils.add_domain_to_acme(r,e.domain),a.domain!==e.domain&&Proxmox.Utils.remove_domain_from_acme(r,a.domain),n.acme=Proxmox.Utils.printACME(r)),n)},items:[{xtype:"proxmoxKVComboBox",name:"type",fieldLabel:gettext("Challenge Type"),allowBlank:!1,value:"standalone",comboItems:[["standalone","HTTP"],["dns","DNS"]],validator:function(e){let t=this.up("pmxACMEDomainEdit"),i=t.domain?t.domain.configkey:void 0;if("dns"===this.getValue()&&(!i||"acme"===i)){let e=!1;for(let i=0;i<Proxmox.Utils.acmedomain_count;i++)t.nodeconfig[`acmedomain${i}`]||(e=!0);if(!e)return gettext("Only 5 Domains with type DNS can be configured")}return!0},listeners:{change:function(e,t){let i=this.up("pmxACMEDomainEdit").down("field[name=plugin]");i.setDisabled("dns"!==t),i.setHidden("dns"!==t)}}},{xtype:"hidden",name:"alias"},{xtype:"pmxACMEPluginSelector",name:"plugin",disabled:!0,hidden:!0,allowBlank:!1,cbind:{url:"{pluginsUrl}"}},{xtype:"proxmoxtextfield",name:"domain",allowBlank:!1,vtype:"DnsNameOrWildcard",value:"",fieldLabel:gettext("Domain")},{xtype:"combobox",name:"usage",multiSelect:!0,editable:!1,fieldLabel:gettext("Usage"),cbind:{hidden:"{!hasUsage}",allowBlank:"{!hasUsage}"},fields:["usage","name"],displayField:"name",valueField:"usage",store:{data:[{usage:"api",name:"API"},{usage:"smtp",name:"SMTP"}]}}]}],initComponent:function(){let e=this;if(!e.url)throw"no url given";if(!e.acmeUrl)throw"no acmeUrl given";if(!e.nodeconfig)throw"no nodeconfig given";if(e.isCreate=!e.domain,e.isCreate&&(e.domain=`${Proxmox.NodeName}.`),e.callParent(),e.isCreate)e.setValues({domain:e.domain});else{let t={...e.domain};Ext.isDefined(t.usage)&&(t.usage=t.usage.split(";")),e.setValues(t)}}}),Ext.define("Proxmox.window.EndpointEditBase",{extend:"Proxmox.window.Edit",isAdd:!0,fieldDefaults:{labelWidth:120},width:700,initComponent:function(){let e=this;if(e.isCreate=!e.name,!e.baseUrl)throw"baseUrl not set";"group"===e.type?e.url=`/api2/extjs${e.baseUrl}/groups`:e.url=`/api2/extjs${e.baseUrl}/endpoints/${e.type}`,e.isCreate?e.method="POST":(e.url+=`/${e.name}`,e.method="PUT");let t=Proxmox.Schema.notificationEndpointTypes[e.type];if(!t)throw"unknown endpoint type";e.subject=t.name,Ext.apply(e,{items:[{name:e.name,xtype:t.ipanel,isCreate:e.isCreate,baseUrl:e.baseUrl,type:e.type,defaultMailAuthor:t.defaultMailAuthor}]}),e.callParent(),e.isCreate||e.load()}}),Ext.define("Proxmox.panel.NotificationMatcherGeneralPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxNotificationMatcherGeneralPanel",mixins:["Proxmox.Mixin.CBind"],items:[{xtype:"pmxDisplayEditField",name:"name",cbind:{value:"{name}",editable:"{isCreate}"},fieldLabel:gettext("Matcher Name"),allowBlank:!1},{xtype:"proxmoxcheckbox",name:"enable",fieldLabel:gettext("Enable"),allowBlank:!1,checked:!0},{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],onSetValues:function(e){return e.enable=!e.disable,delete e.disable,e},onGetValues:function(e){let t=this;return e.enable?t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:"disable"}):e.disable=1,delete e.enable,e}}),Ext.define("Proxmox.panel.NotificationMatcherTargetPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxNotificationMatcherTargetPanel",mixins:["Proxmox.Mixin.CBind"],items:[{xtype:"pmxNotificationTargetSelector",name:"target",allowBlank:!1}]}),Ext.define("Proxmox.window.NotificationMatcherEdit",{extend:"Proxmox.window.Edit",isAdd:!0,onlineHelp:"notification_matchers",fieldDefaults:{labelWidth:120},width:800,initComponent:function(){let e=this;if(e.isCreate=!e.name,!e.baseUrl)throw"baseUrl not set";e.url=`/api2/extjs${e.baseUrl}/matchers`,e.isCreate?e.method="POST":(e.url+=`/${e.name}`,e.method="PUT"),e.subject=gettext("Notification Matcher"),Ext.apply(e,{bodyPadding:0,items:[{xtype:"tabpanel",region:"center",layout:"fit",bodyPadding:10,items:[{name:e.name,title:gettext("General"),xtype:"pmxNotificationMatcherGeneralPanel",isCreate:e.isCreate,baseUrl:e.baseUrl},{name:e.name,title:gettext("Match Rules"),xtype:"pmxNotificationMatchRulesEditPanel",isCreate:e.isCreate,baseUrl:e.baseUrl},{name:e.name,title:gettext("Targets to notify"),xtype:"pmxNotificationMatcherTargetPanel",isCreate:e.isCreate,baseUrl:e.baseUrl}]}]}),e.callParent(),e.isCreate||e.load()}}),Ext.define("Proxmox.form.NotificationTargetSelector",{extend:"Ext.grid.Panel",alias:"widget.pmxNotificationTargetSelector",mixins:{field:"Ext.form.field.Field"},padding:"0 0 10 0",allowBlank:!0,selectAll:!1,isFormField:!0,store:{autoLoad:!0,model:"proxmox-notification-endpoints",sorters:"name"},columns:[{header:gettext("Target Name"),dataIndex:"name",flex:1},{header:gettext("Type"),dataIndex:"type",flex:1},{header:gettext("Comment"),dataIndex:"comment",flex:3}],selModel:{selType:"checkboxmodel",mode:"SIMPLE"},checkChangeEvents:["selectionchange","change"],listeners:{selectionchange:function(){this.checkChange()}},getSubmitData:function(){let e={};return e[this.name]=this.getValue(),e},getValue:function(){let e=this;return void 0!==e.savedValue?e.savedValue:(e.getSelectionModel().getSelection()??[]).map(e=>e.data.name)},setValueSelection:function(e){let t=this,i=t.getStore(),a=[],n=e.map(e=>{let t=i.findRecord("name",e,0,!1,!0,!0);return t||a.push(e),t}).filter(e=>e);for(const e of a){let t=i.add({name:e,type:"-",comment:gettext("Included target does not exist!")});n.push(t[0])}let o=t.getSelectionModel();n.length?o.select(n):o.deselectAll(),t.getErrors()},setValue:function(e){let t=this,i=t.getStore();return i.isLoaded()?t.setValueSelection(e):(t.savedValue=e,i.on("load",function(){t.setValueSelection(e),delete t.savedValue},{single:!0})),t.mixins.field.setValue.call(t,e)},getErrors:function(e){let t=this;return t.isDisabled()||!1!==t.allowBlank||0!==t.getSelectionModel().getCount()?(t.removeBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[]):(t.addBodyCls(["x-form-trigger-wrap-default","x-form-trigger-wrap-invalid"]),[gettext("No target selected")])},initComponent:function(){this.callParent(),this.initField()}}),Ext.define("Proxmox.panel.NotificationRulesEditPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxNotificationMatchRulesEditPanel",mixins:["Proxmox.Mixin.CBind"],controller:{xclass:"Ext.app.ViewController",control:{field:{change:function(e){let t=this.getViewModel();if(e.field){let i=t.get("selectedRecord");if(!i)return;let a=Ext.apply({},i.get("data")),n=e.getValue();n&&n.length||(a[e.field]=n,i.set({data:a}))}}}}},viewModel:{data:{selectedRecord:null,matchFieldType:"exact",matchFieldField:"",matchFieldValue:"",rootMode:"all"},formulas:{nodeType:{get:function(e){let t=e("selectedRecord");return t?.get("type")},set:function(e){let t,i=this.get("selectedRecord");switch(e){case"match-severity":t={value:["info","notice","warning","error","unknown"]};break;case"match-field":t={type:"exact",field:"",value:""};break;case"match-calendar":t={value:""}}let a={type:e,data:t};i.set(a)}},showMatchingMode:function(e){let t=e("selectedRecord");return!!t&&t.isRoot()},showMatcherType:function(e){let t=e("selectedRecord");return!!t&&!t.isRoot()},rootMode:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){let t=this.get("selectedRecord"),i=t.get("data"),a=!1;e.startsWith("not")&&(e=e.substring(3),a=!0),t.set({data:{...i,value:e,invert:a}})},get:function(e){return(e?.get("data").invert?"not":"")+e?.get("data")?.value}}}},column1:[{xtype:"pmxNotificationMatchRuleTree",cbind:{isCreate:"{isCreate}"}}],column2:[{xtype:"pmxNotificationMatchRuleSettings",cbind:{baseUrl:"{baseUrl}"}}],onGetValues:function(e){let t=this,i=i=>{Ext.isArray(e[i])&&0===e[i].length&&(delete e[i],t.isCreate||Proxmox.Utils.assemble_field_data(e,{delete:i}))};return i("match-field"),i("match-severity"),i("match-calendar"),e}}),Ext.define("Proxmox.panel.NotificationMatchRuleTree",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchRuleTree",mixins:["Proxmox.Mixin.CBind"],border:!1,getNodeTextAndIcon:function(e,t){let i,a;switch(e){case"match-severity":{let e=t.value;Ext.isArray(t.value)&&(e=t.value.join(", ")),i=Ext.String.format(gettext("Match severity: {0}"),e),a="fa fa-exclamation",e||(a+=" internal-error")}break;case"match-field":{let e=t.field,n=t.value;i=Ext.String.format(gettext("Match field: {0}={1}"),e,n),a="fa fa-square-o",e&&n&&(!Ext.isArray(n)||n.length)||(a+=" internal-error")}break;case"match-calendar":{let e=t.value;i=Ext.String.format(gettext("Match calendar: {0}"),e),a="fa fa-calendar-o",e&&e.length||(a+=" internal-error")}break;case"mode":"all"===t.value?i=t.invert?gettext("At least one rule does not match"):gettext("All rules match"):"any"===t.value&&(i=t.invert?gettext("No rule matches"):gettext("Any rule matches")),a="fa fa-filter"}return[i,a]},initComponent:function(){let e=this,t=Ext.create("Ext.data.TreeStore",{root:{expanded:!0,expandable:!1,text:"",type:"mode",data:{value:"all",invert:!1},children:[],iconCls:"fa fa-filter"}}),i=Ext.create({xtype:"hiddenfield",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getErrors:function(){for(const e of this.value??[]){if(!e.match(/^([^:]+):([^=]+)=(.+)$/))return[""]}return[]},getSubmitValue:function(){let e=this.value;return e||(e=[]),e},name:"match-field"}),a=Ext.create({xtype:"hiddenfield",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getErrors:function(){for(const e of this.value??[])if(!e)return[""];return[]},getSubmitValue:function(){let e=this.value;return e||(e=[]),e},name:"match-severity"}),n=Ext.create({xtype:"hiddenfield",name:"mode",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getSubmitValue:function(){return this.value}}),o=Ext.create({xtype:"hiddenfield",name:"match-calendar",setValue:function(e){this.value=e,this.checkChange()},getValue:function(){return this.value},getErrors:function(){for(const e of this.value??[])if(!e)return[""];return[]},getSubmitValue:function(){return this.value}}),r=Ext.create({xtype:"proxmoxcheckbox",name:"invert-match",hidden:!0,deleteEmpty:!e.isCreate});i.addListener("change",function(e,i){for(let e of t.queryBy(e=>"match-field"===e.get("type")).getRange())e.remove(!0);if(!i)return;let a=i.map(function(e){let[,t,i,a]=e.match(/^(?:(regex|exact):)?([A-Za-z0-9_][A-Za-z0-9._-]*)=(.+)$/);return void 0===t&&(t="exact"),"exact"===t&&(a=a.split(",")),{type:"match-field",data:{type:t,field:i,value:a},leaf:!0}}),n=t.getRootNode();for(let e of a)n.appendChild(e)}),a.addListener("change",function(e,i){for(let e of t.queryBy(e=>"match-severity"===e.get("type")).getRange())e.remove(!0);let a=i.map(function(e){return{type:"match-severity",data:{value:e.split(",")},leaf:!0}}),n=t.getRootNode();for(let e of a)n.appendChild(e)}),o.addListener("change",function(e,i){for(let e of t.queryBy(e=>"match-calendar"===e.get("type")).getRange())e.remove(!0);let a=i.map(function(e){return{type:"match-calendar",data:{value:e},leaf:!0}}),n=t.getRootNode();for(let e of a)n.appendChild(e)}),n.addListener("change",function(e,i){let a=t.getRootNode().get("data");t.getRootNode().set("data",{...a,value:i})}),r.addListener("change",function(e,i){let a=t.getRootNode().get("data");t.getRootNode().set("data",{...a,invert:i})}),t.addListener("datachanged",function(t){t.suspendEvent("datachanged");let l=[],s=[],d=[],u="all",c=!1;t.each(function(t){let i=t.get("type"),a=t.get("data");switch(i){case"match-field":l.push(`${a.type}:${a.field??""}=${a.value??""}`);break;case"match-severity":Ext.isArray(a.value)?s.push(a.value.join(",")):s.push(a.value);break;case"match-calendar":d.push(a.value);break;case"mode":u=a.value,c=a.invert}let[n,o]=e.getNodeTextAndIcon(i,a);t.set({text:n,iconCls:o})}),i.suspendEvent("change"),i.setValue(l),i.resumeEvent("change"),o.suspendEvent("change"),o.setValue(d),o.resumeEvent("change"),n.suspendEvent("change"),n.setValue(u),n.resumeEvent("change"),r.suspendEvent("change"),r.setValue(c),r.resumeEvent("change"),a.suspendEvent("change"),a.setValue(s),a.resumeEvent("change"),t.resumeEvent("datachanged")});let l=Ext.create({xtype:"treepanel",store:t,minHeight:300,maxHeight:300,scrollable:!0,bind:{selection:"{selectedRecord}"}});Ext.apply(e,{items:[i,n,a,r,o,l,{xtype:"button",margin:"5 5 5 0",text:gettext("Add"),iconCls:"fa fa-plus-circle",handler:function(){t.getRootNode().appendChild({type:"match-field",data:{type:"exact",field:"",value:""},leaf:!0}),l.setSelection(t.getRootNode().lastChild)}},{xtype:"button",margin:"5 5 5 0",text:gettext("Remove"),iconCls:"fa fa-minus-circle",handler:function(){let e=l.getSelection();for(let t of e)t.isRoot()||t.remove(!0)}}]}),e.callParent()}}),Ext.define("Proxmox.panel.NotificationMatchRuleSettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchRuleSettings",mixins:["Proxmox.Mixin.CBind"],border:!1,layout:"anchor",items:[{xtype:"proxmoxKVComboBox",name:"mode",fieldLabel:gettext("Match if"),allowBlank:!1,isFormField:!1,matchFieldWidth:!1,comboItems:[["all",gettext("All rules match")],["any",gettext("Any rule matches")],["notall",gettext("At least one rule does not match")],["notany",gettext("No rule matches")]],hidden:!0,bind:{hidden:"{!showMatchingMode}",disabled:"{!showMatchingMode}",value:"{rootMode}"}},{xtype:"proxmoxKVComboBox",fieldLabel:gettext("Node type"),isFormField:!1,allowBlank:!1,hidden:!0,bind:{value:"{nodeType}",hidden:"{!showMatcherType}",disabled:"{!showMatcherType}"},comboItems:[["match-field",gettext("Match Field")],["match-severity",gettext("Match Severity")],["match-calendar",gettext("Match Calendar")]]},{xtype:"pmxNotificationMatchFieldSettings",cbind:{baseUrl:"{baseUrl}"}},{xtype:"pmxNotificationMatchSeveritySettings"},{xtype:"pmxNotificationMatchCalendarSettings"}]}),Ext.define("Proxmox.panel.MatchCalendarSettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchCalendarSettings",border:!1,layout:"anchor",hidden:!0,bind:{hidden:"{!typeIsMatchCalendar}"},viewModel:{formulas:{typeIsMatchCalendar:{bind:{bindTo:"{selectedRecord}",deep:!0},get:function(e){return"match-calendar"===e?.get("type")}},matchCalendarValue:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){let t=this.get("selectedRecord"),i=t.get("data");t.set({data:{...i,value:e}})},get:function(e){return e?.get("data")?.value}}}},items:[{xtype:"proxmoxKVComboBox",fieldLabel:gettext("Timespan to match"),isFormField:!1,allowBlank:!1,editable:!0,displayField:"key",field:"value",bind:{value:"{matchCalendarValue}",disabled:"{!typeIsMatchCalender}"},comboItems:[["mon 8-12",""],["tue..fri,sun 0:00-23:59",""]]}],initComponent:function(){let e=this;Ext.apply(e.viewModel,{parent:e.up("pmxNotificationMatchRulesEditPanel").getViewModel()}),e.callParent()}}),Ext.define("Proxmox.panel.MatchSeveritySettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchSeveritySettings",border:!1,layout:"anchor",hidden:!0,bind:{hidden:"{!typeIsMatchSeverity}"},viewModel:{formulas:{typeIsMatchSeverity:{bind:{bindTo:"{selectedRecord}",deep:!0},get:function(e){return"match-severity"===e?.get("type")}},matchSeverityValue:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){let t=this.get("selectedRecord"),i=t.get("data");t.set({data:{...i,value:e}})},get:function(e){return e?.get("data")?.value}}}},items:[{xtype:"proxmoxKVComboBox",fieldLabel:gettext("Severities to match"),isFormField:!1,allowBlank:!0,multiSelect:!0,field:"value",hidden:!0,bind:{value:"{matchSeverityValue}",hidden:"{!typeIsMatchSeverity}",disabled:"{!typeIsMatchSeverity}"},comboItems:[["info",gettext("Info")],["notice",gettext("Notice")],["warning",gettext("Warning")],["error",gettext("Error")],["unknown",gettext("Unknown")]]}],initComponent:function(){let e=this;Ext.apply(e.viewModel,{parent:e.up("pmxNotificationMatchRulesEditPanel").getViewModel()}),e.callParent()}}),Ext.define("Proxmox.panel.MatchFieldSettings",{extend:"Ext.panel.Panel",xtype:"pmxNotificationMatchFieldSettings",border:!1,layout:"anchor",hidden:!0,bind:{hidden:"{!typeIsMatchField}"},controller:{xclass:"Ext.app.ViewController",control:{"field[reference=fieldSelector]":{change:function(e){let t=this.getView().down("field[reference=valueSelector]").getStore(),i=e.getValue();i&&t.setFilters([{property:"field",value:i}])}}}},viewModel:{formulas:{typeIsMatchField:{bind:{bindTo:"{selectedRecord}",deep:!0},get:function(e){return"match-field"===e?.get("type")}},isRegex:function(e){return"regex"===e("matchFieldType")},matchFieldType:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){let t=this.get("selectedRecord"),i=t.get("data"),a=[];if("regex"===e){let e="^";i.value&&i.value.length&&(e+=`(${i.value.join("|")})`),e+="$",a.push(e)}t.set({data:{...i,type:e,value:a}})},get:function(e){return e?.get("data")?.type}},matchFieldField:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){let t=this.get("selectedRecord"),i=t.get("data");t.set({data:{...i,field:e,value:[]}})},get:function(e){return e?.get("data")?.field}},matchFieldValue:{bind:{bindTo:"{selectedRecord}",deep:!0},set:function(e){let t=this.get("selectedRecord"),i=t.get("data");t.set({data:{...i,value:e}})},get:function(e){return e?.get("data")?.value}}}},initComponent:function(){let e=this,t=Ext.create("Ext.data.Store",{model:"proxmox-notification-fields",autoLoad:!0,proxy:{type:"proxmox",url:`/api2/json/${e.baseUrl}/matcher-fields`},listeners:{load:function(){this.each(function(e){e.set({description:Proxmox.Utils.formatNotificationFieldName(e.get("name"))})}),this.commitChanges()}}}),i=Ext.create("Ext.data.Store",{model:"proxmox-notification-field-values",autoLoad:!0,proxy:{type:"proxmox",url:`/api2/json/${e.baseUrl}/matcher-field-values`},listeners:{load:function(){this.each(function(e){"type"===e.get("field")&&e.set({comment:Proxmox.Utils.formatNotificationFieldValue(e.get("value"))})},this,!0),this.commitChanges()}}});Ext.apply(e.viewModel,{parent:e.up("pmxNotificationMatchRulesEditPanel").getViewModel()}),Ext.apply(e,{items:[{fieldLabel:gettext("Match Type"),xtype:"proxmoxKVComboBox",reference:"type",isFormField:!1,allowBlank:!1,submitValue:!1,field:"type",bind:{value:"{matchFieldType}"},comboItems:[["exact",gettext("Exact")],["regex",gettext("Regex")]]},{fieldLabel:gettext("Field"),reference:"fieldSelector",xtype:"proxmoxComboGrid",isFormField:!1,submitValue:!1,allowBlank:!1,editable:!1,store:t,queryMode:"local",valueField:"name",displayField:"description",field:"field",bind:{value:"{matchFieldField}"},listConfig:{columns:[{header:gettext("Description"),dataIndex:"description",flex:2},{header:gettext("Field Name"),dataIndex:"name",flex:1}]}},{fieldLabel:gettext("Value"),reference:"valueSelector",xtype:"proxmoxComboGrid",autoSelect:!1,editable:!1,isFormField:!1,submitValue:!1,allowBlank:!1,showClearTrigger:!0,field:"value",store:i,valueField:"value",displayField:"value",notFoundIsValid:!1,multiSelect:!0,bind:{value:"{matchFieldValue}",hidden:"{isRegex}"},listConfig:{columns:[{header:gettext("Value"),dataIndex:"value",flex:1},{header:gettext("Comment"),dataIndex:"comment",flex:2}]}},{fieldLabel:gettext("Regex"),xtype:"proxmoxtextfield",editable:!0,isFormField:!1,submitValue:!1,allowBlank:!1,field:"value",bind:{value:"{matchFieldValue}",hidden:"{!isRegex}"}}]}),e.callParent()}}),Ext.define("proxmox-file-tree",{extend:"Ext.data.Model",fields:["filepath","text","type","size",{name:"sizedisplay",calculate:e=>void 0===e.size?"":Proxmox.Utils.format_size(e.size)},{name:"mtime",type:"date",dateFormat:"timestamp"},{name:"iconCls",calculate:function(e){let t=Proxmox.Schema.pxarFileTypes[e.type]?.icon??"file-o";return e.expanded&&"d"===e.type&&(t="folder-open-o"),`fa fa-${t}`}}],idProperty:"filepath"}),Ext.define("Proxmox.window.FileBrowser",{extend:"Ext.window.Window",width:800,height:600,modal:!0,config:{listURL:"",downloadURL:"",extraParams:{},downloadableFileTypes:{h:!0,f:!0,d:!0},downloadPrefix:""},controller:{xclass:"Ext.app.ViewController",buildUrl:function(e,t){let i=new URL(e,window.location.origin);for(const[e,a]of Object.entries(t))i.searchParams.append(e,a);return i.href},downloadTar:function(){this.downloadFile(!0)},downloadZip:function(){this.downloadFile(!1)},downloadFile:function(e){let t=this,i=t.getView(),a=t.lookup("tree").getSelection();if(!a||a.length<1)return;let n=a[0].data,o={...i.extraParams};o.filepath=n.filepath;let r=i.downloadPrefix+n.text;"d"===n.type&&(e?(o.tar=1,r+=".tar.zst"):r+=".zip"),Proxmox.Utils.downloadAsFile(t.buildUrl(i.downloadURL,o),r)},fileChanged:function(){let e=this.getView(),t=this.lookup("tree").getSelection();if(!t||t.length<1)return;let i=t[0].data,a=Ext.String.format(gettext('Selected "{0}"'),atob(i.filepath));e.lookup("selectText").setText(a);let n=e.downloadURL&&e.downloadableFileTypes[i.type],o="d"===i.type,r=e.lookup("downloadBtn");r.setDisabled(!n||o),r.setHidden(n&&o);let l=Proxmox.Schema.pxarFileTypes[i.type]?.label??Proxmox.Utils.unknownText,s=Ext.String.format(gettext("File of type {0} cannot be downloaded directly, download a parent directory instead."),l);n||r.setStyle({pointerEvents:"all"}),r.setTooltip(n?null:s);let d=e.lookup("menuBtn");d.setDisabled(!n||!o),d.setHidden(!n||!o)},errorHandler:function(e,t){let i=this;return 503!==e?.status&&(i.lookup("downloadBtn").setDisabled(!0),i.lookup("menuBtn").setDisabled(!0),!!i.initialLoadDone&&(Ext.Msg.alert(gettext("Error"),t),!0))},init:function(e){let t=this,i=t.lookup("tree");if(!e.listURL)throw"no list URL given";let a=i.getStore(),n=a.getProxy();n.setUrl(e.listURL),n.setTimeout(6e4),n.setExtraParams(e.extraParams),i.mon(a,"beforeload",()=>{Proxmox.Utils.setErrorMask(i,!0)}),i.mon(a,"load",(e,a,n,o,r)=>{if(n)return void Proxmox.Utils.setErrorMask(i,!1);if(r.loadCount||(r.loadCount=0),503===o?.error?.status&&r.loadCount<10)return r.collapse(),r.expand(),void r.loadCount++;let l=o.getError(),s=Proxmox.Utils.getResponseErrorMessage(l);((e,i)=>t.errorHandler(e,i))(l,s)?Proxmox.Utils.setErrorMask(i,!1):Proxmox.Utils.setErrorMask(i,s)}),a.load((n,o,r)=>{let l=a.getRoot();if(l.expand(),"all"===e.archive)l.expandChildren(!1);else if(e.archive){let t=l.findChild("text",e.archive);t&&(t.expand(),setTimeout(function(){i.setSelection(t),i.getView().focusRow(t)},10))}else 1===l.childNodes.length&&l.firstChild.expand();t.initialLoadDone=r})},control:{treepanel:{selectionchange:"fileChanged"}}},layout:"fit",items:[{xtype:"treepanel",scrollable:!0,rootVisible:!1,reference:"tree",store:{autoLoad:!1,model:"proxmox-file-tree",defaultRootId:"/",nodeParam:"filepath",sorters:"text",proxy:{appendId:!1,type:"proxmox"}},viewConfig:{loadMask:!1},columns:[{text:gettext("Name"),xtype:"treecolumn",flex:1,dataIndex:"text",renderer:Ext.String.htmlEncode},{text:gettext("Size"),dataIndex:"sizedisplay",align:"end",sorter:{sorterFn:function(e,t){if("d"===e.data.type&&"d"!==t.data.type)return-1;if("d"!==e.data.type&&"d"===t.data.type)return 1;return(e.data.size||0)-(t.data.size||0)}}},{text:gettext("Modified"),dataIndex:"mtime",minWidth:200},{text:gettext("Type"),dataIndex:"type",renderer:e=>Proxmox.Schema.pxarFileTypes[e]?.label??Proxmox.Utils.unknownText}]}],fbar:[{text:"",xtype:"label",reference:"selectText"},{text:gettext("Download"),xtype:"button",handler:"downloadZip",reference:"downloadBtn",disabled:!0,hidden:!0},{text:gettext("Download as"),xtype:"button",reference:"menuBtn",menu:{items:[{iconCls:"fa fa-fw fa-file-zip-o",text:gettext(".zip"),handler:"downloadZip",reference:"downloadZip"},{iconCls:"fa fa-fw fa-archive",text:gettext(".tar.zst"),handler:"downloadTar",reference:"downloadTar"}]}}]}),Ext.define("Proxmox.window.AuthEditBase",{extend:"Proxmox.window.Edit",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,isAdd:!0,fieldDefaults:{labelWidth:120},baseurl:"/access/domains",useTypeInUrl:!1,initComponent:function(){var e=this;e.isCreate=!e.realm,e.url=`/api2/extjs${e.baseUrl}`,e.useTypeInUrl&&(e.url+=`/${e.authType}`),e.isCreate?e.method="POST":(e.url+=`/${e.realm}`,e.method="PUT");let t,i,a=Proxmox.Schema.authDomains[e.authType];if(!a)throw`unknown auth type ${e.authType}`;if(!a.add&&e.isCreate)throw`trying to add non addable realm of type ${e.authType}`;e.subject=a.name,a.syncipanel?(i=0,t={xtype:"tabpanel",region:"center",layout:"fit",bodyPadding:10,items:[{title:gettext("General"),realm:e.realm,xtype:a.ipanel,isCreate:e.isCreate,useTypeInUrl:e.useTypeInUrl,type:e.authType,showDefaultRealm:e.showDefaultRealm},{title:gettext("Sync Options"),realm:e.realm,xtype:a.syncipanel,isCreate:e.isCreate,type:e.authType}]}):t=[{realm:e.realm,xtype:a.ipanel,isCreate:e.isCreate,useTypeInUrl:e.useTypeInUrl,type:e.authType,showDefaultRealm:e.showDefaultRealm}],Ext.apply(e,{items:t,bodyPadding:i}),e.callParent(),e.isCreate||e.load({success:function(t,i){var a=t.result.data||{};if(!e.useTypeInUrl&&a.type!==e.authType)throw e.close(),`got wrong auth type '${e.authType}' for realm '${a.type}'`;e.setValues(a)}})}}),Ext.define("Proxmox.panel.OpenIDInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthOpenIDPanel",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,type:"openid",onGetValues:function(e){let t=this;return t.isCreate&&!t.useTypeInUrl&&(e.type=t.type),e},columnT:[{xtype:"textfield",name:"issuer-url",fieldLabel:gettext("Issuer URL"),allowBlank:!1}],column1:[{xtype:"pmxDisplayEditField",name:"realm",cbind:{value:"{realm}",editable:"{isCreate}"},fieldLabel:gettext("Realm"),allowBlank:!1},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Default Realm"),name:"default",value:0,cbind:{deleteEmpty:"{!isCreate}",hidden:"{!showDefaultRealm}",disabled:"{!showDefaultRealm}"},autoEl:{tag:"div","data-qtip":gettext("Set realm as default for login")}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Client ID"),name:"client-id",allowBlank:!1},{xtype:"proxmoxtextfield",fieldLabel:gettext("Client Key"),cbind:{deleteEmpty:"{!isCreate}"},name:"client-key"}],column2:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("Autocreate Users"),name:"autocreate",value:0,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"pmxDisplayEditField",name:"username-claim",fieldLabel:gettext("Username Claim"),editConfig:{xtype:"proxmoxKVComboBox",editable:!0,comboItems:[["__default__",Proxmox.Utils.defaultText],["subject","subject"],["username","username"],["email","email"]]},cbind:{value:e=>e("isCreate")?"__default__":Proxmox.Utils.defaultText,deleteEmpty:"{!isCreate}",editable:"{isCreate}"}},{xtype:"proxmoxtextfield",name:"scopes",fieldLabel:gettext("Scopes"),emptyText:`${Proxmox.Utils.defaultText} (email profile)`,submitEmpty:!1,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxKVComboBox",name:"prompt",fieldLabel:gettext("Prompt"),editable:!0,emptyText:gettext("Auth-Provider Default"),comboItems:[["__default__",gettext("Auth-Provider Default")],["none","none"],["login","login"],["consent","consent"],["select_account","select_account"]],cbind:{deleteEmpty:"{!isCreate}"}}],columnB:[{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}],advancedColumnB:[{xtype:"proxmoxtextfield",name:"acr-values",fieldLabel:gettext("ACR Values"),submitEmpty:!1,cbind:{deleteEmpty:"{!isCreate}"}}]}),Ext.define("Proxmox.panel.LDAPInputPanelViewModel",{extend:"Ext.app.ViewModel",alias:"viewmodel.pmxAuthLDAPPanel",data:{mode:"ldap",anonymous_search:1},formulas:{tls_enabled:function(e){return"ldap"!==e("mode")}}}),Ext.define("Proxmox.panel.LDAPInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthLDAPPanel",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,viewModel:{type:"pmxAuthLDAPPanel"},type:"ldap",onlineHelp:"user-realms-ldap",onGetValues:function(e){if(this.isCreate&&!this.useTypeInUrl&&(e.type=this.type),e.anonymous_search&&!this.isCreate){if(e.delete||(e.delete=[]),!Array.isArray(e.delete)){let t=e.delete;e.delete=[],e.delete.push(t)}e.delete.push("bind-dn"),e.delete.push("password")}return delete e.anonymous_search,e},onSetValues:function(e){return e.anonymous_search=e["bind-dn"]?0:1,this.getViewModel().set("anonymous_search",e.anonymous_search),e},cbindData:function(e){return{isLdap:"ldap"===this.type,isAd:"ad"===this.type}},column1:[{xtype:"pmxDisplayEditField",name:"realm",cbind:{value:"{realm}",editable:"{isCreate}"},fieldLabel:gettext("Realm"),allowBlank:!1},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Default Realm"),name:"default",value:0,cbind:{deleteEmpty:"{!isCreate}",hidden:"{!showDefaultRealm}",disabled:"{!showDefaultRealm}"},autoEl:{tag:"div","data-qtip":gettext("Set realm as default for login")}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Base Domain Name"),name:"base-dn",emptyText:"cn=Users,dc=company,dc=net",cbind:{hidden:"{!isLdap}",allowBlank:"{!isLdap}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("User Attribute Name"),name:"user-attr",emptyText:"uid / sAMAccountName",cbind:{hidden:"{!isLdap}",allowBlank:"{!isLdap}"}},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Anonymous Search"),name:"anonymous_search",bind:{value:"{anonymous_search}"}},{xtype:"proxmoxtextfield",fieldLabel:gettext("Bind Domain Name"),name:"bind-dn",allowBlank:!1,cbind:{emptyText:e=>e("isAd")?"<EMAIL>":"cn=user,dc=company,dc=net",autoEl:e=>e("isAd")?{tag:"div","data-qtip":gettext("LDAP DN syntax can be used as well, e.g. cn=user,dc=company,dc=net")}:{}},bind:{disabled:"{anonymous_search}"}},{xtype:"proxmoxtextfield",inputType:"password",fieldLabel:gettext("Bind Password"),name:"password",cbind:{emptyText:e=>e("isCreate")?"":gettext("Unchanged"),allowBlank:"{!isCreate}"},bind:{disabled:"{anonymous_search}"}}],column2:[{xtype:"proxmoxtextfield",name:"server1",fieldLabel:gettext("Server"),allowBlank:!1},{xtype:"proxmoxtextfield",name:"server2",fieldLabel:gettext("Fallback Server"),submitEmpty:!1,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxintegerfield",name:"port",fieldLabel:gettext("Port"),minValue:1,maxValue:65535,emptyText:gettext("Default"),submitEmptyText:!1,cbind:{deleteEmpty:"{!isCreate}"}},{xtype:"proxmoxKVComboBox",name:"mode",fieldLabel:gettext("Mode"),editable:!1,comboItems:[["ldap","LDAP"],["ldap+starttls","STARTTLS"],["ldaps","LDAPS"]],bind:"{mode}",cbind:{deleteEmpty:"{!isCreate}",value:e=>e("isCreate")?"ldap":"LDAP"}},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Verify Certificate"),name:"verify",value:0,cbind:{deleteEmpty:"{!isCreate}"},bind:{disabled:"{!tls_enabled}"},autoEl:{tag:"div","data-qtip":gettext("Verify TLS certificate of the server")}}],columnB:[{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),cbind:{deleteEmpty:"{!isCreate}"}}]}),Ext.define("Proxmox.panel.LDAPSyncInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthLDAPSyncPanel",mixins:["Proxmox.Mixin.CBind"],editableAttributes:["firstname","lastname","email"],editableDefaults:["scope","enable-new"],default_opts:{},sync_attributes:{},type:"ldap",onGetValues:function(e){let t=this;t.editableDefaults.forEach(i=>{e[i]?(t.default_opts[i]=e[i],delete e[i]):delete t.default_opts[i]});let i=[];return["acl","entry","properties"].forEach(t=>{e[`remove-vanished-${t}`]&&i.push(t),delete e[`remove-vanished-${t}`]}),t.default_opts["remove-vanished"]=i.join(";"),e["sync-defaults-options"]=Proxmox.Utils.printPropertyString(t.default_opts),t.editableAttributes.forEach(i=>{e[i]?(t.sync_attributes[i]=e[i],delete e[i]):delete t.sync_attributes[i]}),e["sync-attributes"]=Proxmox.Utils.printPropertyString(t.sync_attributes),Proxmox.Utils.delete_if_default(e,"sync-defaults-options"),Proxmox.Utils.delete_if_default(e,"sync-attributes"),"string"==typeof e.delete&&(e.delete=e.delete.split(",")),t.isCreate&&delete e.delete,e},setValues:function(e){let t=this;if(e["sync-attributes"]&&(t.sync_attributes=Proxmox.Utils.parsePropertyString(e["sync-attributes"]),delete e["sync-attributes"],t.editableAttributes.forEach(i=>{t.sync_attributes[i]&&(e[i]=t.sync_attributes[i])})),e["sync-defaults-options"]&&(t.default_opts=Proxmox.Utils.parsePropertyString(e["sync-defaults-options"]),delete e.default_opts,t.editableDefaults.forEach(i=>{t.default_opts[i]&&(e[i]=t.default_opts[i])}),t.default_opts["remove-vanished"])){let i=t.default_opts["remove-vanished"].split(";");for(const t of i)e[`remove-vanished-${t}`]=1}return t.callParent([e])},column1:[{xtype:"proxmoxtextfield",name:"firstname",fieldLabel:gettext("First Name attribute"),autoEl:{tag:"div","data-qtip":Ext.String.format(gettext("Often called {0}"),"`givenName`")}},{xtype:"proxmoxtextfield",name:"lastname",fieldLabel:gettext("Last Name attribute"),autoEl:{tag:"div","data-qtip":Ext.String.format(gettext("Often called {0}"),"`sn`")}},{xtype:"proxmoxtextfield",name:"email",fieldLabel:gettext("E-Mail attribute"),autoEl:{tag:"div","data-qtip":e=>e("isAd")?Ext.String.format(gettext("Often called {0} or {1}"),"`userPrincipalName`","`mail`"):Ext.String.format(gettext("Often called {0}"),"`mail`")}},{xtype:"displayfield",value:gettext("Default Sync Options")},{xtype:"proxmoxKVComboBox",value:"__default__",deleteEmpty:!1,comboItems:[["__default__",Ext.String.format(gettext("{0} ({1})"),Proxmox.Utils.yesText,Proxmox.Utils.defaultText)],["true",Proxmox.Utils.yesText],["false",Proxmox.Utils.noText]],name:"enable-new",fieldLabel:gettext("Enable new users")}],column2:[{xtype:"proxmoxtextfield",name:"user-classes",fieldLabel:gettext("User classes"),cbind:{deleteEmpty:"{!isCreate}"},emptyText:"inetorgperson, posixaccount, person, user",autoEl:{tag:"div","data-qtip":gettext("Default user classes: inetorgperson, posixaccount, person, user")}},{xtype:"proxmoxtextfield",name:"filter",fieldLabel:gettext("User Filter"),cbind:{deleteEmpty:"{!isCreate}"}}],columnB:[{xtype:"fieldset",title:gettext("Remove Vanished Options"),items:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("ACL"),name:"remove-vanished-acl",boxLabel:gettext("Remove ACLs of vanished users")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Entry"),name:"remove-vanished-entry",boxLabel:gettext("Remove vanished user")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Properties"),name:"remove-vanished-properties",boxLabel:gettext("Remove vanished properties from synced users.")}]}]}),Ext.define("Proxmox.panel.ADInputPanel",{extend:"Proxmox.panel.LDAPInputPanel",xtype:"pmxAuthADPanel",type:"ad",onlineHelp:"user-realms-ad"}),Ext.define("Proxmox.panel.ADSyncInputPanel",{extend:"Proxmox.panel.LDAPSyncInputPanel",xtype:"pmxAuthADSyncPanel",type:"ad"}),Ext.define("Proxmox.panel.SimpleRealmInputPanel",{extend:"Proxmox.panel.InputPanel",xtype:"pmxAuthSimplePanel",mixins:["Proxmox.Mixin.CBind"],showDefaultRealm:!1,column1:[{xtype:"pmxDisplayEditField",name:"realm",cbind:{value:"{realm}"},fieldLabel:gettext("Realm")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Default Realm"),name:"default",value:0,deleteEmpty:!0,autoEl:{tag:"div","data-qtip":gettext("Set realm as default for login")},cbind:{hidden:"{!showDefaultRealm}",disabled:"{!showDefaultRealm}"}}],column2:[],columnB:[{xtype:"proxmoxtextfield",name:"comment",fieldLabel:gettext("Comment"),allowBlank:!0,deleteEmpty:!0}]}),Ext.define("Proxmox.window.TfaLoginWindow",{extend:"Ext.window.Window",mixins:["Proxmox.Mixin.CBind"],title:gettext("Second login factor required"),modal:!0,resizable:!1,width:512,layout:{type:"vbox",align:"stretch"},defaultButton:"tfaButton",viewModel:{data:{confirmText:gettext("Confirm Second Factor"),canConfirm:!1,availableChallenge:{}}},cancelled:!0,controller:{xclass:"Ext.app.ViewController",init:function(e){let t=this,i=t.getViewModel();if(!e.userid)throw"no userid given";if(!e.ticket)throw"no ticket given";const a=e.challenge;if(!a)throw"no challenge given";let n=t.getLastTabUsed(),o=-1,r=0,l=0,s=!1;for(const e of["webauthn","totp","recovery","u2f","yubico"]){const t=!!a[e];i.set(`availableChallenge.${e}`,t),t&&(l++,"recovery"===e&&(s=!0),(r===n||o<0)&&(o=r)),r++}if(!l||1===l&&s&&!a.recovery.length)return t.lookup("cannotLogin").setVisible(!0),t.lookup("recoveryKey").setVisible(!1),void e.down("tabpanel").setActiveTab(2);if(e.down("tabpanel").setActiveTab(o),a.recovery)if(e.challenge.recovery.length){let i=e.challenge.recovery.map(e=>Ext.String.format(gettext("ID {0}"),e)).join(", ");t.lookup("availableRecovery").update(Ext.String.htmlEncode(Ext.String.format(gettext("Available recovery keys: {0}"),i))),t.lookup("availableRecovery").setVisible(!0),e.challenge.recovery.length<=3&&t.lookup("recoveryLow").setVisible(!0)}else t.lookup("recoveryEmpty").setVisible(!0),t.lookup("recoveryKey").setVisible(!1);if(a.webauthn&&0===o){t.loginWebauthn()}else if(a.u2f&&3===o){t.loginU2F()}},control:{tabpanel:{tabchange:function(e,t,i){let a=i.down("field");a&&a.setDisabled(!0);let n=t.down("field");n&&(n.setDisabled(!1),n.focus(),n.validate());let o=t.confirmText||gettext("Confirm Second Factor");this.getViewModel().set("confirmText",o),this.saveLastTabUsed(e,t)}},field:{validitychange:function(e,t){this.getViewModel().set("canConfirm",t)},afterrender:e=>e.focus()}},saveLastTabUsed:function(e,t){let i=e.items.indexOf(t);window.localStorage.setItem("Proxmox.TFALogin.lastTab",JSON.stringify({id:i}))},getLastTabUsed:function(){let e=window.localStorage.getItem("Proxmox.TFALogin.lastTab");if("string"==typeof e){return JSON.parse(e).id}return null},onClose:function(){let e=this.getView();e.cancelled&&e.onReject()},cancel:function(){this.getView().close()},loginTotp:function(){let e=this.lookup("totp").getValue();this.finishChallenge(`totp:${e}`)},loginYubico:function(){let e=this.lookup("yubico").getValue();this.finishChallenge(`yubico:${e}`)},loginWebauthn:async function(){let e=this,t=e.getView();e.lookup("webAuthnWaiting").setVisible(!0),e.lookup("webAuthnError").setVisible(!1);let i=t.challenge.webauthn;if("string"!=typeof i.string){i.string=i.publicKey.challenge,i.publicKey.challenge=Proxmox.Utils.base64url_to_bytes(i.string);for(const e of i.publicKey.allowCredentials)e.id=Proxmox.Utils.base64url_to_bytes(e.id)}let a,n=new AbortController;i.signal=n.signal;try{a=await navigator.credentials.get(i)}catch(t){return this.getViewModel().set("canConfirm",!0),e.lookup("webAuthnError").setData({error:Ext.htmlEncode(t.toString())}),void e.lookup("webAuthnError").setVisible(!0)}finally{let t=e.lookup("webAuthnWaiting");t&&t.setVisible(!1)}let o={id:a.id,type:a.type,challenge:i.string,rawId:Proxmox.Utils.bytes_to_base64url(a.rawId),response:{authenticatorData:Proxmox.Utils.bytes_to_base64url(a.response.authenticatorData),clientDataJSON:Proxmox.Utils.bytes_to_base64url(a.response.clientDataJSON),signature:Proxmox.Utils.bytes_to_base64url(a.response.signature)}};await e.finishChallenge("webauthn:"+JSON.stringify(o))},loginU2F:async function(){let e,t=this,i=t.getView();t.lookup("u2fWaiting").setVisible(!0),t.lookup("u2fError").setVisible(!1);try{if(e=await new Promise((e,t)=>{try{let t=i.challenge.u2f,a=t.challenge;u2f.sign(a.appId,a.challenge,t.keys,e)}catch(e){t(e)}}),e.errorCode)throw Proxmox.Utils.render_u2f_error(e.errorCode);delete e.errorCode}catch(e){return this.getViewModel().set("canConfirm",!0),t.lookup("u2fError").setData({error:Ext.htmlEncode(e.toString())}),void t.lookup("u2fError").setVisible(!0)}finally{let e=t.lookup("u2fWaiting");e&&e.setVisible(!1)}await t.finishChallenge("u2f:"+JSON.stringify(e))},loginRecovery:function(){let e=this.lookup("recoveryKey").getValue();this.finishChallenge(`recovery:${e}`)},loginTFA:function(){let e=this;e.getViewModel().set("canConfirm",!1);let t=e.getView();e[t.down("tabpanel").getActiveTab().handler]()},finishChallenge:function(e){let t=this.getView();t.cancelled=!1;let i={username:t.userid,"tfa-challenge":t.ticket,password:e},a=t.onResolve,n=t.onReject;return t.close(),Proxmox.Async.api2({url:"/api2/extjs/access/ticket",method:"POST",params:i}).then(a).catch(n)}},listeners:{close:"onClose"},items:[{xtype:"tabpanel",region:"center",layout:"fit",bodyPadding:10,items:[{xtype:"panel",title:"WebAuthn",iconCls:"fa fa-fw fa-shield",confirmText:gettext("Start WebAuthn challenge"),handler:"loginWebauthn",bind:{disabled:"{!availableChallenge.webauthn}"},items:[{xtype:"box",html:gettext("Please insert your authentication device and press its button")},{xtype:"box",html:gettext("Waiting for second factor.")+'<i class="fa fa-refresh fa-spin fa-fw"></i>',reference:"webAuthnWaiting",hidden:!0},{xtype:"box",data:{error:""},tpl:'<i class="fa fa-warning warning"></i> {error}',reference:"webAuthnError",hidden:!0}]},{xtype:"panel",title:gettext("TOTP App"),iconCls:"fa fa-fw fa-clock-o",handler:"loginTotp",bind:{disabled:"{!availableChallenge.totp}"},items:[{xtype:"textfield",fieldLabel:gettext("Please enter your TOTP verification code"),labelWidth:300,name:"totp",disabled:!0,reference:"totp",allowBlank:!1,regex:/^[0-9]{2,16}$/,regexText:gettext("TOTP codes usually consist of six decimal digits"),inputAttrTpl:"autocomplete=one-time-code"}]},{xtype:"panel",title:gettext("Recovery Key"),iconCls:"fa fa-fw fa-file-text-o",handler:"loginRecovery",bind:{disabled:"{!availableChallenge.recovery}"},items:[{xtype:"box",reference:"cannotLogin",hidden:!0,html:'<i class="fa fa-exclamation-triangle warning"></i>'+Ext.String.format(gettext("No second factor left! Please contact an administrator!"),4)},{xtype:"box",reference:"recoveryEmpty",hidden:!0,html:'<i class="fa fa-exclamation-triangle warning"></i>'+Ext.String.format(gettext("No more recovery keys left! Please generate a new set!"),4)},{xtype:"box",reference:"recoveryLow",hidden:!0,html:'<i class="fa fa-exclamation-triangle warning"></i>'+Ext.String.format(gettext("Less than {0} recovery keys available. Please generate a new set after login!"),4)},{xtype:"box",reference:"availableRecovery",hidden:!0},{xtype:"textfield",fieldLabel:gettext("Please enter one of your single-use recovery keys"),labelWidth:300,name:"recoveryKey",disabled:!0,reference:"recoveryKey",allowBlank:!1,regex:/^[0-9a-f]{4}(-[0-9a-f]{4}){3}$/,regexText:gettext("Does not look like a valid recovery key")}]},{xtype:"panel",title:"U2F",iconCls:"fa fa-fw fa-shield",confirmText:gettext("Start U2F challenge"),handler:"loginU2F",bind:{disabled:"{!availableChallenge.u2f}"},tabConfig:{bind:{hidden:"{!availableChallenge.u2f}"}},items:[{xtype:"box",html:gettext("Please insert your authentication device and press its button")},{xtype:"box",html:gettext("Waiting for second factor.")+'<i class="fa fa-refresh fa-spin fa-fw"></i>',reference:"u2fWaiting",hidden:!0},{xtype:"box",data:{error:""},tpl:'<i class="fa fa-warning warning"></i> {error}',reference:"u2fError",hidden:!0}]},{xtype:"panel",title:gettext("Yubico OTP"),iconCls:"fa fa-fw fa-yahoo",handler:"loginYubico",bind:{disabled:"{!availableChallenge.yubico}"},tabConfig:{bind:{hidden:"{!availableChallenge.yubico}"}},items:[{xtype:"textfield",fieldLabel:gettext("Please enter your Yubico OTP code"),labelWidth:300,name:"yubico",disabled:!0,reference:"yubico",allowBlank:!1,regex:/^[a-z0-9]{30,60}$/,regexText:gettext("TOTP codes consist of six decimal digits")}]}]}],buttons:[{handler:"loginTFA",reference:"tfaButton",disabled:!0,bind:{text:"{confirmText}",disabled:"{!canConfirm}"}}]}),Ext.define("Proxmox.window.AddTfaRecovery",{extend:"Proxmox.window.Edit",alias:"widget.pmxAddTfaRecovery",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",isCreate:!0,isAdd:!0,subject:gettext("TFA recovery keys"),width:512,method:"POST",fixedUser:!1,url:"/api2/extjs/access/tfa",submitUrl:function(e,t){let i=t.userid;return delete t.userid,`${e}/${i}`},apiCallDone:function(e,t){if(!e)return;let i=t.result.data.recovery.map((e,t)=>`${t}: ${e}`).join("\n");Ext.create("Proxmox.window.TfaRecoveryShow",{autoShow:!0,userid:this.getViewModel().get("userid"),values:i})},viewModel:{data:{has_entry:!1,userid:null}},controller:{xclass:"Ext.app.ViewController",hasEntry:async function(e){let t=this.getView();try{return await Proxmox.Async.api2({url:`${t.url}/${e}/recovery`,method:"GET"}),!0}catch(e){return!1}},init:function(e){this.onUseridChange(null,Proxmox.UserName)},onUseridChange:async function(e,t){let i=this,a=i.getViewModel();i.userid=t,a.set("userid",t);let n=await i.hasEntry(t);a.set("has_entry",n)}},items:[{xtype:"pmxDisplayEditField",name:"userid",cbind:{editable:e=>!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1,validator:function(e){return!this.up("window").getViewModel().get("has_entry")}},renderer:Ext.String.htmlEncode,listeners:{change:"onUseridChange"}},{xtype:"hiddenfield",name:"type",value:"recovery"},{xtype:"displayfield",bind:{hidden:"{!has_entry}"},hidden:!0,userCls:"pmx-hint",value:gettext("User already has recovery keys.")},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}),Ext.define("Proxmox.window.TfaRecoveryShow",{extend:"Ext.window.Window",alias:["widget.pmxTfaRecoveryShow"],mixins:["Proxmox.Mixin.CBind"],width:600,modal:!0,resizable:!1,title:gettext("Recovery Keys"),onEsc:Ext.emptyFn,items:[{xtype:"form",layout:"anchor",bodyPadding:10,border:!1,fieldDefaults:{anchor:"100%"},items:[{xtype:"textarea",editable:!1,inputId:"token-secret-value",cbind:{value:"{values}"},fieldStyle:{fontFamily:"monospace"},height:"160px"},{xtype:"displayfield",border:!1,padding:"5 0 0 0",userCls:"pmx-hint",value:gettext("Please record recovery keys - they will only be displayed now")}]}],buttons:[{handler:function(e){document.getElementById("token-secret-value").select(),document.execCommand("copy")},iconCls:"fa fa-clipboard",text:gettext("Copy Recovery Keys")},{handler:function(e){let t=this.up("window");t.paperkeys(t.values,t.userid)},iconCls:"fa fa-print",text:gettext("Print Recovery Keys")}],paperkeys:function(e,t){let i=document.createElement("iframe");Object.assign(i.style,{position:"fixed",right:"0",bottom:"0",width:"0",height:"0",border:"0"});const a=document.location.host,n=`<html><head><script>\n\t    window.addEventListener('DOMContentLoaded', (ev) => window.print());\n\t<\/script><style>@media print and (max-height: 150mm) {\n\t  h4, p { margin: 0; font-size: 1em; }\n\t}</style></head><body style="padding: 5px;">\n\t<h4>Recovery Keys for '${t}' - ${document.title} (${a})</h4>\n<p style="font-size:1.5em;line-height:1.5em;font-family:monospace;\n   white-space:pre-wrap;overflow-wrap:break-word;">\n${e}\n</p>\n\t</body></html>`;i.src="data:text/html;base64,"+btoa(n),document.body.appendChild(i),this.on("destroy",()=>document.body.removeChild(i))}}),Ext.define("Proxmox.window.AddTotp",{extend:"Proxmox.window.Edit",alias:"widget.pmxAddTotp",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Add a TOTP login factor"),width:512,layout:{type:"vbox",align:"stretch"},isAdd:!0,userid:void 0,tfa_id:void 0,issuerName:`Proxmox - ${document?.location?.hostname||"unknown"}`,fixedUser:!1,updateQrCode:function(){let e=this,t=e.lookup("totp_form").getValues(),i=t.algorithm;i||(i="SHA1");let a="otpauth://totp/"+encodeURIComponent(t.issuer)+":"+encodeURIComponent(t.userid)+"?secret="+t.secret+"&period="+t.step+"&digits="+t.digits+"&algorithm="+i+"&issuer="+encodeURIComponent(t.issuer);e.getController().getViewModel().set("otpuri",a),e.qrcode.makeCode(a),e.lookup("challenge").setVisible(!0),e.down("#qrbox").setVisible(!0)},viewModel:{data:{valid:!1,secret:"",otpuri:"",userid:null},formulas:{secretEmpty:function(e){return 0===e("secret").length}}},controller:{xclass:"Ext.app.ViewController",control:{"field[qrupdate=true]":{change:function(){this.getView().updateQrCode()}},field:{validitychange:function(e,t){let i=this,a=i.getViewModel(),n=i.lookup("totp_form"),o=i.lookup("challenge"),r=i.lookup("password");a.set("valid",n.isValid()&&o.isValid()&&r.isValid())}},"#":{show:function(){let e=this.getView();e.qrdiv=document.createElement("div"),e.qrcode=new QRCode(e.qrdiv,{width:256,height:256,correctLevel:QRCode.CorrectLevel.M}),e.down("#qrbox").getEl().appendChild(e.qrdiv),e.getController().randomizeSecret()}}},randomizeSecret:function(){let e=new Uint8Array(32);window.crypto.getRandomValues(e);let t="";e.forEach(function(e){t+=(e&=31)<26?String.fromCharCode(e+65):String.fromCharCode(e-26+50)}),this.getViewModel().set("secret",t)}},items:[{xtype:"form",layout:"anchor",border:!1,reference:"totp_form",fieldDefaults:{anchor:"100%"},items:[{xtype:"pmxDisplayEditField",name:"userid",cbind:{editable:e=>e("isAdd")&&!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},renderer:Ext.String.htmlEncode,listeners:{change:function(e,t,i){this.up("window").getViewModel().set("userid",t)}},qrupdate:!0},{xtype:"textfield",fieldLabel:gettext("Description"),emptyText:gettext("For example: TFA device ID, required to identify multiple factors."),allowBlank:!1,name:"description",maxLength:256},{layout:"hbox",border:!1,padding:"0 0 5 0",items:[{xtype:"textfield",fieldLabel:gettext("Secret"),emptyText:gettext("Unchanged"),name:"secret",reference:"tfa_secret",regex:/^[A-Z2-7=]+$/,regexText:"Must be base32 [A-Z2-7=]",maskRe:/[A-Z2-7=]/,qrupdate:!0,bind:{value:"{secret}"},flex:4,padding:"0 5 0 0"},{xtype:"button",text:gettext("Randomize"),reference:"randomize_button",handler:"randomizeSecret",flex:1}]},{xtype:"numberfield",fieldLabel:gettext("Time period"),name:"step",hidden:!0,value:30,minValue:10,qrupdate:!0},{xtype:"numberfield",fieldLabel:gettext("Digits"),name:"digits",value:6,hidden:!0,minValue:6,maxValue:8,qrupdate:!0},{xtype:"textfield",fieldLabel:gettext("Issuer Name"),name:"issuer",cbind:{value:"{issuerName}"},qrupdate:!0},{xtype:"box",itemId:"qrbox",visible:!1,bind:{visible:"{!secretEmpty}"},style:{margin:"16px auto",padding:"16px",width:"288px",height:"288px","background-color":"white"}},{xtype:"textfield",fieldLabel:gettext("Verify Code"),allowBlank:!1,reference:"challenge",name:"challenge",bind:{disabled:"{!showTOTPVerifiction}",visible:"{showTOTPVerifiction}"},emptyText:gettext("Scan QR code in a TOTP app and enter an auth. code here")},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}],initComponent:function(){let e=this;e.url="/api2/extjs/access/tfa/",e.method="POST",e.callParent()},getValues:function(e){let t=this,i=t.getController().getViewModel(),a=t.callParent(arguments),n=encodeURIComponent(a.userid);t.url=`/api2/extjs/access/tfa/${n}`,delete a.userid;let o={description:a.description,type:"totp",totp:i.get("otpuri"),value:a.challenge};return a.password&&(o.password=a.password),o}}),Ext.define("Proxmox.window.AddWebauthn",{extend:"Ext.window.Window",alias:"widget.pmxAddWebauthn",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Add a Webauthn login token"),width:512,user:void 0,fixedUser:!1,initComponent:function(){this.callParent(),Ext.GlobalEvents.fireEvent("proxmoxShowHelp",this.onlineHelp)},viewModel:{data:{valid:!1,userid:null}},controller:{xclass:"Ext.app.ViewController",control:{field:{validitychange:function(e,t){let i=this.getViewModel(),a=this.lookup("webauthn_form");i.set("valid",a.isValid())}},"#":{show:function(){let e=this.getView();"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0))}}},registerWebauthn:async function(){let e=this,t=e.lookup("webauthn_form").getValues();t.type="webauthn";let i=t.user;delete t.user,e.getView().mask(gettext("Please wait..."),"x-mask-loading");try{let e=(await Proxmox.Async.api2({url:`/api2/extjs/access/tfa/${i}`,method:"POST",params:t})).result.data;if(!e.challenge)throw"server did not respond with a challenge";let a=JSON.parse(e.challenge),n=a.publicKey.challenge;a.publicKey.challenge=Proxmox.Utils.base64url_to_bytes(n),a.publicKey.user.id=Proxmox.Utils.base64url_to_bytes(a.publicKey.user.id),a.publicKey.excludeCredentials=(a.publicKey.excludeCredentials||[]).map(e=>({id:Proxmox.Utils.base64url_to_bytes(e.id),type:e.type}));let o,r=Ext.Msg.show({title:`Webauthn: ${gettext("Setup")}`,message:gettext("Please press the button on your Webauthn Device"),buttons:[]});try{o=await navigator.credentials.create(a)}catch(e){let t=e.message;throw"InvalidStateError"===e.name&&(t=gettext("Is this token already registered?")),gettext("An error occurred during token registration.")+`<br>${e.name}: ${t}`}let l={id:o.id,type:o.type,rawId:Proxmox.Utils.bytes_to_base64url(o.rawId),response:{attestationObject:Proxmox.Utils.bytes_to_base64url(o.response.attestationObject),clientDataJSON:Proxmox.Utils.bytes_to_base64url(o.response.clientDataJSON)}};r.close();let s={type:"webauthn",challenge:n,value:JSON.stringify(l)};t.password&&(s.password=t.password),await Proxmox.Async.api2({url:`/api2/extjs/access/tfa/${i}`,method:"POST",params:s})}catch(e){let t=e;console.error(t),"object"==typeof t&&(t=t.result?.message),Ext.Msg.alert(gettext("Error"),t)}e.getView().close()}},items:[{xtype:"form",reference:"webauthn_form",layout:"anchor",border:!1,bodyPadding:10,fieldDefaults:{anchor:"100%"},items:[{xtype:"pmxDisplayEditField",name:"user",cbind:{editable:e=>!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},renderer:Ext.String.htmlEncode,listeners:{change:function(e,t,i){this.up("window").getViewModel().set("userid",t)}}},{xtype:"textfield",fieldLabel:gettext("Description"),allowBlank:!1,name:"description",maxLength:256,emptyText:gettext("For example: TFA device ID, required to identify multiple factors.")},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}],buttons:[{xtype:"proxmoxHelpButton"},"->",{xtype:"button",text:gettext("Register Webauthn Device"),handler:"registerWebauthn",bind:{disabled:"{!valid}"}}]}),Ext.define("Proxmox.window.AddYubico",{extend:"Proxmox.window.Edit",alias:"widget.pmxAddYubico",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Add a Yubico OTP key"),width:512,isAdd:!0,userid:void 0,fixedUser:!1,initComponent:function(){let e=this;e.url="/api2/extjs/access/tfa/",e.method="POST",e.callParent()},viewModel:{data:{valid:!1,userid:null}},controller:{xclass:"Ext.app.ViewController",control:{field:{validitychange:function(e,t){let i=this.getViewModel(),a=this.lookup("yubico_form");i.set("valid",a.isValid())}},"#":{show:function(){let e=this.getView();"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0))}}}},items:[{xtype:"form",reference:"yubico_form",layout:"anchor",border:!1,bodyPadding:10,fieldDefaults:{anchor:"100%"},items:[{xtype:"pmxDisplayEditField",name:"userid",cbind:{editable:e=>!e("fixedUser"),value:()=>Proxmox.UserName},fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},renderer:Ext.String.htmlEncode,listeners:{change:function(e,t,i){this.up("window").getViewModel().set("userid",t)}}},{xtype:"textfield",fieldLabel:gettext("Description"),allowBlank:!1,name:"description",maxLength:256,emptyText:gettext("For example: TFA device ID, required to identify multiple factors.")},{xtype:"textfield",fieldLabel:gettext("Yubico OTP Key"),emptyText:gettext("A currently valid Yubico OTP value"),name:"otp_value",maxLength:44,enforceMaxLength:!0,regex:/^[a-zA-Z0-9]{44}$/,regexText:"44 characters",maskRe:/^[a-zA-Z0-9]$/},{xtype:"textfield",name:"password",reference:"password",fieldLabel:gettext("Verify Password"),inputType:"password",minLength:5,allowBlank:!1,validateBlank:!0,cbind:{hidden:()=>"root@pam"===Proxmox.UserName,disabled:()=>"root@pam"===Proxmox.UserName,emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}},{xtype:"box",html:`<span class='pmx-hint'>${gettext("Tip:")}</span> `+gettext("YubiKeys also support WebAuthn, which is often a better alternative.")}]}],getValues:function(e){let t=this.callParent(arguments),i=encodeURIComponent(t.userid);this.url=`/api2/extjs/access/tfa/${i}`,delete t.userid;let a={description:t.description,type:"yubico",value:t.otp_value};return t.password&&(a.password=t.password),a}}),Ext.define("Proxmox.window.TfaEdit",{extend:"Proxmox.window.Edit",alias:"widget.pmxTfaEdit",mixins:["Proxmox.Mixin.CBind"],onlineHelp:"user_mgmt",modal:!0,resizable:!1,title:gettext("Modify a TFA entry's description"),width:512,layout:{type:"vbox",align:"stretch"},cbindData:function(e){let t=this,i=e["tfa-id"];return t.tfa_id=i,t.defaultFocus="textfield[name=description]",t.url=`/api2/extjs/access/tfa/${i}`,t.method="PUT",t.autoLoad=!0,{}},initComponent:function(){let e=this;e.callParent(),"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0));let t=e.tfa_id.split("/")[0];e.lookup("userid").setValue(t)},items:[{xtype:"displayfield",reference:"userid",editable:!1,fieldLabel:gettext("User"),editConfig:{xtype:"pmxUserSelector",allowBlank:!1},cbind:{value:()=>Proxmox.UserName}},{xtype:"proxmoxtextfield",name:"description",allowBlank:!1,fieldLabel:gettext("Description")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Enabled"),name:"enable",uncheckedValue:0,defaultValue:1,checked:!0},{xtype:"textfield",inputType:"password",fieldLabel:gettext("Password"),minLength:5,reference:"password",name:"password",allowBlank:!1,validateBlank:!0,emptyText:gettext("verify current password")}],getValues:function(){var e=this.callParent(arguments);return delete e.userid,e}}),Ext.define("Proxmox.tfa.confirmRemove",{extend:"Proxmox.window.Edit",mixins:["Proxmox.Mixin.CBind"],title:gettext("Confirm TFA Removal"),modal:!0,resizable:!1,width:600,isCreate:!0,isRemove:!0,url:"/access/tfa",initComponent:function(){let e=this;if("string"!=typeof e.type)throw"missing type";if(!e.callback)throw"missing callback";e.callParent(),"root@pam"===Proxmox.UserName&&(e.lookup("password").setVisible(!1),e.lookup("password").setDisabled(!0))},submit:function(){let e=this;"root@pam"===Proxmox.UserName?e.callback(null):e.callback(e.lookup("password").getValue()),e.close()},items:[{xtype:"box",padding:"0 0 10 0",html:Ext.String.format(gettext("Are you sure you want to remove this {0} entry?"),"TFA")},{xtype:"container",layout:{type:"hbox",align:"begin"},defaults:{border:!1,layout:"anchor",flex:1,padding:5},items:[{xtype:"container",layout:{type:"vbox"},padding:"0 10 0 0",items:[{xtype:"displayfield",fieldLabel:gettext("User"),cbind:{value:"{userid}"}},{xtype:"displayfield",fieldLabel:gettext("Type"),cbind:{value:"{type}"}}]},{xtype:"container",layout:{type:"vbox"},padding:"0 0 0 10",items:[{xtype:"displayfield",fieldLabel:gettext("Created"),renderer:e=>Proxmox.Utils.render_timestamp(e),cbind:{value:"{created}"}},{xtype:"textfield",fieldLabel:gettext("Description"),cbind:{value:"{description}"},emptyText:Proxmox.Utils.NoneText,submitValue:!1,editable:!1}]}]},{xtype:"textfield",inputType:"password",fieldLabel:gettext("Password"),minLength:5,reference:"password",name:"password",allowBlank:!1,validateBlank:!0,padding:"10 0 0 0",cbind:{emptyText:()=>Ext.String.format(gettext("Confirm your ({0}) password"),Proxmox.UserName)}}]}),Ext.define("Proxmox.window.NotesEdit",{extend:"Proxmox.window.Edit",title:gettext("Notes"),onlineHelp:"markdown_basics",width:800,height:600,resizable:!0,layout:"fit",autoLoad:!0,defaultButton:void 0,setMaxLength:function(e){let t=this.down('textarea[name="description"]');return t.maxLength=e,t.validate(),this},items:{xtype:"textarea",name:"description",height:"100%",value:"",hideLabel:!0,emptyText:gettext("You can use Markdown for rich text formatting."),fieldStyle:{"white-space":"pre-wrap","font-family":"monospace"}}}),Ext.define("Proxmox.window.ThemeEditWindow",{extend:"Ext.window.Window",alias:"widget.pmxThemeEditWindow",viewModel:{parent:null,data:{}},controller:{xclass:"Ext.app.ViewController",init:function(e){let t="__default__",i=Ext.util.Cookies.get(e.cookieName);i&&i in Proxmox.Utils.theme_map&&(t=i),this.getViewModel().set("theme",t)},applyTheme:function(e){let t=this.getView(),i=this.getViewModel(),a=Ext.Date.add(new Date,Ext.Date.YEAR,10);Ext.util.Cookies.set(t.cookieName,i.get("theme"),a),t.mask(gettext("Please wait..."),"x-mask-loading"),window.location.reload()}},cookieName:"PVEThemeCookie",title:gettext("Color Theme"),modal:!0,bodyPadding:10,resizable:!1,items:[{xtype:"proxmoxThemeSelector",fieldLabel:gettext("Color Theme"),bind:{value:"{theme}"}}],buttons:[{text:gettext("Apply"),handler:"applyTheme"}]}),Ext.define("Proxmox.window.SyncWindow",{extend:"Ext.window.Window",title:gettext("Realm Sync"),width:600,bodyPadding:10,modal:!0,resizable:!1,controller:{xclass:"Ext.app.ViewController",control:{form:{validitychange:function(e,t){this.lookup("preview_btn").setDisabled(!t),this.lookup("sync_btn").setDisabled(!t)}},button:{click:function(e){this.sync_realm("preview_btn"===e.reference)}}},sync_realm:function(e){let t=this.getView(),i=this.lookup("ipanel").getValues(),a=[];["acl","entry","properties"].forEach(e=>{i[`remove-vanished-${e}`]&&a.push(e),delete i[`remove-vanished-${e}`]}),a.length>0&&(i["remove-vanished"]=a.join(";")),i["dry-run"]=e?1:0,Proxmox.Utils.API2Request({url:`/access/domains/${t.realm}/sync`,waitMsgTarget:t,method:"POST",params:i,failure:e=>{t.show(),Ext.Msg.alert(gettext("Error"),e.htmlStatus)},success:i=>{t.hide(),Ext.create("Proxmox.window.TaskViewer",{upid:i.result.data,listeners:{destroy:()=>{e?t.show():t.close()}}}).show()}})}},items:[{xtype:"form",reference:"form",border:!1,fieldDefaults:{labelWidth:100,anchor:"100%"},items:[{xtype:"inputpanel",reference:"ipanel",column1:[{xtype:"proxmoxKVComboBox",value:"true",deleteEmpty:!1,allowBlank:!1,comboItems:[["true",Proxmox.Utils.yesText],["false",Proxmox.Utils.noText]],name:"enable-new",fieldLabel:gettext("Enable new")}],column2:[],columnB:[{xtype:"fieldset",title:gettext("Remove Vanished Options"),items:[{xtype:"proxmoxcheckbox",fieldLabel:gettext("ACL"),name:"remove-vanished-acl",boxLabel:gettext("Remove ACLs of vanished users and groups.")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Entry"),name:"remove-vanished-entry",boxLabel:gettext("Remove vanished user and group entries.")},{xtype:"proxmoxcheckbox",fieldLabel:gettext("Properties"),name:"remove-vanished-properties",boxLabel:gettext("Remove vanished properties from synced users.")}]},{xtype:"displayfield",reference:"defaulthint",value:gettext("Default sync options can be set by editing the realm."),userCls:"pmx-hint",hidden:!0}]}]}],buttons:["->",{text:gettext("Preview"),reference:"preview_btn"},{text:gettext("Sync"),reference:"sync_btn"}],initComponent:function(){if(!this.realm)throw"no realm defined";if(!this.type)throw"no realm type defined";this.callParent(),Proxmox.Utils.API2Request({url:`/config/access/${this.type}/${this.realm}`,waitMsgTarget:this,method:"GET",failure:e=>{Ext.Msg.alert(gettext("Error"),e.htmlStatus),this.close()},success:e=>{let t=e.result.data["sync-defaults-options"];if(t){let e=Proxmox.Utils.parsePropertyString(t);if(e["remove-vanished"]){let t=e["remove-vanished"].split(";");for(const i of t)e[`remove-vanished-${i}`]=1}this.lookup("ipanel").setValues(e)}else this.lookup("defaulthint").setVisible(!0);this.lookup("form").isValid()}})}}),Ext.define("apt-pkglist",{extend:"Ext.data.Model",fields:["Package","Title","Description","Section","Arch","Priority","Version","OldVersion","Origin"],idProperty:"Package"}),Ext.define("Proxmox.node.APT",{extend:"Ext.grid.GridPanel",xtype:"proxmoxNodeAPT",upgradeBtn:void 0,columns:[{header:gettext("Package"),width:200,sortable:!0,dataIndex:"Package"},{text:gettext("Version"),columns:[{header:gettext("current"),width:100,sortable:!1,dataIndex:"OldVersion"},{header:gettext("new"),width:100,sortable:!1,dataIndex:"Version"}]},{header:gettext("Description"),sortable:!1,dataIndex:"Title",flex:1}],initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";let t=Ext.create("Ext.data.Store",{model:"apt-pkglist",groupField:"Origin",proxy:{type:"proxmox",url:`/api2/json/nodes/${e.nodename}/apt/update`},sorters:[{property:"Package",direction:"ASC"}]});Proxmox.Utils.monStoreErrors(e,t,!0);let i=Ext.create("Ext.grid.feature.Grouping",{groupHeaderTpl:'{[ "Origin: " + values.name ]} ({rows.length} Item{[values.rows.length > 1 ? "s" : ""]})',enableGroupingMenu:!1}),a=Ext.create("Ext.grid.feature.RowBody",{getAdditionalData:function(t,i,a,n){let o=this.view.headerCt.getColumnCount();return{rowBody:`<div style="padding: 1em">${Ext.htmlEncode(t.Description)}</div>`,rowBodyCls:e.full_description?"":Ext.baseCSSPrefix+"grid-row-body-hidden",rowBodyColspan:o}}}),n=Ext.create("Ext.selection.RowModel",{}),o=new Ext.Button({text:gettext("Refresh"),handler:()=>Proxmox.Utils.checked_command(function(){var i;i="update",Proxmox.Utils.API2Request({url:`/nodes/${e.nodename}/apt/${i}`,method:"POST",success:({result:e})=>Ext.create("Proxmox.window.TaskViewer",{autoShow:!0,upid:e.data,listeners:{close:()=>t.load()}})})})}),r=function(t){if(!t?.data?.Package)return void console.debug("cannot show changelog, missing Package",t);let i=Ext.createWidget("component",{autoScroll:!0,style:{"white-space":"pre","font-family":"monospace",padding:"5px"}}),a=Ext.create("Ext.window.Window",{title:gettext("Changelog")+": "+t.data.Package,width:800,height:600,layout:"fit",modal:!0,items:[i]});Proxmox.Utils.API2Request({waitMsgTarget:e,url:"/nodes/"+e.nodename+"/apt/changelog",params:{name:t.data.Package,version:t.data.Version},method:"GET",failure:function(e,t){a.close(),Ext.Msg.alert(gettext("Error"),e.htmlStatus)},success:function(e,t){a.show(),i.update(Ext.htmlEncode(e.result.data))}})},l=new Proxmox.button.Button({text:gettext("Changelog"),selModel:n,disabled:!0,enableFn:e=>!!e?.data?.Package,handler:(e,t,i)=>r(i)}),s=new Ext.form.field.Checkbox({boxLabel:gettext("Show details"),value:!1,listeners:{change:(t,i)=>{e.full_description=i,e.getView().refresh()}}});e.upgradeBtn?e.tbar=[o,e.upgradeBtn,l,"->",s]:e.tbar=[o,l,"->",s],Ext.apply(e,{store:t,stateful:!0,stateId:"grid-update",selModel:n,viewConfig:{stripeRows:!1,emptyText:`<div style="display:flex;justify-content:center;"><p>${gettext("No updates available.")}</p></div>`},features:[i,a],listeners:{activate:()=>t.load(),itemdblclick:(e,t)=>r(t)}}),e.callParent()}}),Ext.define("apt-repolist",{extend:"Ext.data.Model",fields:["Path","Index","Origin","FileType","Enabled","Comment","Types","URIs","Suites","Components","Options"]}),Ext.define("Proxmox.window.APTRepositoryAdd",{extend:"Proxmox.window.Edit",alias:"widget.pmxAPTRepositoryAdd",isCreate:!0,isAdd:!0,subject:gettext("Repository"),width:600,initComponent:function(){let e=this;if(!e.repoInfo||0===e.repoInfo.length)throw"repository information not initialized";let t=Ext.create("Ext.form.field.Display",{fieldLabel:gettext("Description"),name:"description"}),i=Ext.create("Ext.form.field.Display",{fieldLabel:gettext("Status"),name:"status",renderer:function(e){let t=gettext("Not yet configured");return""!==e&&(t=Ext.String.format("{0}: {1}",gettext("Configured"),e?gettext("enabled"):gettext("disabled"))),t}}),a=Ext.create("Proxmox.form.KVComboBox",{fieldLabel:gettext("Repository"),xtype:"proxmoxKVComboBox",name:"handle",allowBlank:!1,comboItems:e.repoInfo.map(e=>[e.handle,e.name]),validator:function(t){let i=this.value,a=Proxmox.form.KVComboBox.prototype.validator.call(this,t);if(!a||!i)return!1;const n=e.repoInfo.find(e=>e.handle===i);return!!n&&(n.status?Ext.String.format(gettext("{0} is already configured"),t):a)},listeners:{change:function(a,n){const o=e.repoInfo.find(e=>e.handle===n);t.setValue(o.description),i.setValue(o.status)}}});a.setValue(e.repoInfo[0].handle),Ext.apply(e,{items:[a,t,i],repoSelector:a}),e.callParent()}}),Ext.define("Proxmox.node.APTRepositoriesErrors",{extend:"Ext.grid.GridPanel",xtype:"proxmoxNodeAPTRepositoriesErrors",store:{},scrollable:!0,viewConfig:{stripeRows:!1,getRowClass:e=>{switch(e.data.status){case"warning":return"proxmox-warning-row";case"critical":return"proxmox-invalid-row";default:return""}}},hideHeaders:!0,columns:[{dataIndex:"status",renderer:e=>`<i class="fa fa-fw ${Proxmox.Utils.get_health_icon(e,!0)}"></i>`,width:50},{dataIndex:"message",flex:1}]}),Ext.define("Proxmox.node.APTRepositoriesGrid",{extend:"Ext.grid.GridPanel",xtype:"proxmoxNodeAPTRepositoriesGrid",mixins:["Proxmox.Mixin.CBind"],title:gettext("APT Repositories"),cls:"proxmox-apt-repos",border:!1,tbar:[{text:gettext("Reload"),iconCls:"fa fa-refresh",handler:function(){this.up("proxmoxNodeAPTRepositories").reload()}},{text:gettext("Add"),name:"addRepo",disabled:!0,repoInfo:void 0,cbind:{onlineHelp:"{onlineHelp}"},handler:function(e,t,i){Proxmox.Utils.checked_command(()=>{let e=this,t=e.up("proxmoxNodeAPTRepositories"),i={};void 0!==t.digest&&(i.digest=t.digest),Ext.create("Proxmox.window.APTRepositoryAdd",{repoInfo:e.repoInfo,url:`/api2/extjs/nodes/${t.nodename}/apt/repositories`,method:"PUT",extraRequestParams:i,onlineHelp:e.onlineHelp,listeners:{destroy:function(){t.reload()}}}).show()})}},"-",{xtype:"proxmoxAltTextButton",defaultText:gettext("Enable"),altText:gettext("Disable"),name:"repoEnable",disabled:!0,bind:{text:"{enableButtonText}"},handler:function(e,t,i){let a=this.up("proxmoxNodeAPTRepositories"),n={path:i.data.Path,index:i.data.Index,enabled:i.data.Enabled?0:1};void 0!==a.digest&&(n.digest=a.digest),Proxmox.Utils.API2Request({url:`/nodes/${a.nodename}/apt/repositories`,method:"POST",params:n,failure:function(e,t){Ext.Msg.alert(gettext("Error"),e.htmlStatus),a.reload()},success:function(e,t){a.reload()}})}}],sortableColumns:!1,viewConfig:{stripeRows:!1,getRowClass:(e,t)=>e.get("Enabled")?"":"proxmox-disabled-row"},columns:[{header:gettext("Enabled"),dataIndex:"Enabled",align:"center",renderer:Proxmox.Utils.renderEnabledIcon,width:90},{header:gettext("Types"),dataIndex:"Types",renderer:function(e,t,i){return e.join(" ")},width:100},{header:gettext("URIs"),dataIndex:"URIs",renderer:function(e,t,i){return e.join(" ")},width:350},{header:gettext("Suites"),dataIndex:"Suites",renderer:function(e,t,i){let a="";if(i.data.warnings&&i.data.warnings.length>0){let e=[gettext("Warning")];i.data.warnings.forEach(t=>{"Suites"===t.property&&e.push(Ext.htmlEncode(t.message))}),t.tdAttr=`data-qtip="${Ext.htmlEncode(e.join("<br>"))}"`,i.data.Enabled?(t.tdCls="proxmox-invalid-row",a='<i class="fa fa-fw critical fa-exclamation-circle"></i> '):(t.tdCls="proxmox-warning-row",a='<i class="fa fa-fw warning fa-exclamation-circle"></i> ')}return e.join(" ")+a},width:130},{header:gettext("Components"),dataIndex:"Components",renderer:function(e,t,i){if(void 0===e)return"";let a="";if(1===e.length&&e[0].match(/\w+(-no-subscription|test)\s*$/i)){t.tdCls="proxmox-warning-row",a='<i class="fa fa-fw warning fa-exclamation-circle"></i> ';let i=e[0].match(/no-subscription/)?gettext("The no-subscription repository is NOT production-ready"):gettext("The test repository may contain unstable updates");t.tdAttr=`data-qtip="${Ext.htmlEncode(Ext.htmlEncode(i))}"`}return e.join(" ")+a},width:170},{header:gettext("Options"),dataIndex:"Options",renderer:function(e,t,i){if(!e)return"";let a=i.data.FileType,n="";return e.forEach(function(e){let t=e.Key;if("list"===a){let i=e.Values.join(",");n+=`${t}=${i} `}else{if("sources"!==a)throw"unknown file type";{let i=e.Values.join(" ");n+=`${t}: ${i}<br>`}}}),n},flex:1},{header:gettext("Origin"),dataIndex:"Origin",width:120,renderer:function(e,t,i){"string"==typeof e&&0!==e.length||(e=gettext("Other"));let a="fa fa-fw fa-question-circle-o",n=this.up("proxmoxNodeAPTRepositories").classifyOrigin(e);return"Proxmox"===n?a="pmx-itype-icon pmx-itype-icon-proxmox-x":"Debian"===n&&(a="pmx-itype-icon pmx-itype-icon-debian-swirl"),`<i class='${a}'></i> ${e}`}},{header:gettext("Comment"),dataIndex:"Comment",flex:2,renderer:Ext.String.htmlEncode}],features:[{ftype:"grouping",groupHeaderTpl:'{[ "File: " + values.name ]} ({rows.length} repositor{[values.rows.length > 1 ? "ies" : "y"]})',enableGroupingMenu:!1}],store:{model:"apt-repolist",groupField:"Path",sorters:[{property:"Index",direction:"ASC"}]},initComponent:function(){if(!this.nodename)throw"no node name specified";this.callParent()}}),Ext.define("Proxmox.node.APTRepositories",{extend:"Ext.panel.Panel",xtype:"proxmoxNodeAPTRepositories",mixins:["Proxmox.Mixin.CBind"],digest:void 0,onlineHelp:void 0,product:"Proxmox VE",classifyOrigin:function(e){return e||="",e.match(/^\s*Proxmox\s*$/i)?"Proxmox":e.match(/^\s*Debian\s*(:?Backports)?$/i)?"Debian":"Other"},controller:{xclass:"Ext.app.ViewController",selectionChange:function(e,t){if(!t||t.length<1)return;let i=t[0],a=this.getViewModel();a.set("selectionenabled",i.get("Enabled")),a.notify()},updateState:function(){let e=this.getViewModel(),t=e.get("errorstore");t.removeAll();let i="good",a=gettext("All OK, you have production-ready repositories configured!"),n=e=>t.add({status:"good",message:e}),o=(e,n)=>{"critical"!==i&&(i="warning",a=n?e:gettext("Warning")),t.add({status:"warning",message:e})},r=(e,n)=>{i="critical",a=n?e:gettext("Error"),t.add({status:"critical",message:e})},l=e.get("errors");l.forEach(e=>r(`${e.path} - ${e.error}`));let s=e.get("subscriptionActive"),d=e.get("enterpriseRepo"),u=e.get("noSubscriptionRepo"),c=e.get("testRepo"),x={enterprise:e.get("cephEnterpriseRepo"),nosubscription:e.get("cephNoSubscriptionRepo"),test:e.get("cephTestRepo")},m=e.get("suitesWarning"),p=e.get("mixedSuites");d||u||c?l.length>0||(d&&!u&&!c&&s?n(Ext.String.format(gettext("You get supported updates for {0}"),e.get("product"))):(u||c)&&n(Ext.String.format(gettext("You get updates for {0}"),e.get("product")))):r(Ext.String.format(gettext("No {0} repository is enabled, you do not get any updates!"),e.get("product"))),m&&o(gettext("Some suites are misconfigured")),p&&o(gettext("Detected mixed suites before upgrade"));let f=(e,t,i)=>{!s&&e.enterprise&&o(Ext.String.format(gettext("The {0}enterprise repository is enabled, but there is no active subscription!"),t)),e.nosubscription&&o(Ext.String.format(gettext("The {0}no-subscription{1} repository is not recommended for production use!"),t,i)),e.test&&o(Ext.String.format(gettext("The {0}test repository may pull in unstable updates and is not recommended for production use!"),t))};f({enterprise:d,nosubscription:u,test:c},"",""),f(x,"Ceph ","/main"),l.length>0&&(a=gettext("Fatal parsing error for at least one repository"));let g=Proxmox.Utils.get_health_icon(i,!0);e.set("state",{iconCls:g,text:a})}},viewModel:{data:{product:"Proxmox VE",errors:[],suitesWarning:!1,mixedSuites:!1,subscriptionActive:"",noSubscriptionRepo:"",enterpriseRepo:"",testRepo:"",cephEnterpriseRepo:"",cephNoSubscriptionRepo:"",cephTestRepo:"",selectionenabled:!1,state:{}},formulas:{enableButtonText:e=>e("selectionenabled")?gettext("Disable"):gettext("Enable")},stores:{errorstore:{fields:["status","message"]}}},scrollable:!0,layout:{type:"vbox",align:"stretch"},items:[{xtype:"panel",border:!1,layout:{type:"hbox",align:"stretch"},height:200,title:gettext("Status"),items:[{xtype:"box",flex:2,margin:10,data:{iconCls:Proxmox.Utils.get_health_icon(void 0,!0),text:""},bind:{data:"{state}"},tpl:['<center class="centered-flex-column" style="font-size:15px;line-height: 25px;">','<i class="fa fa-4x {iconCls}"></i>',"{text}","</center>"]},{xtype:"proxmoxNodeAPTRepositoriesErrors",name:"repositoriesErrors",flex:7,margin:10,bind:{store:"{errorstore}"}}]},{xtype:"proxmoxNodeAPTRepositoriesGrid",name:"repositoriesGrid",flex:1,cbind:{nodename:"{nodename}",onlineHelp:"{onlineHelp}"},majorUpgradeAllowed:!1,listeners:{selectionchange:"selectionChange"}}],check_subscription:function(){let e=this,t=e.getViewModel();Proxmox.Utils.API2Request({url:`/nodes/${e.nodename}/subscription`,method:"GET",failure:(e,t)=>Ext.Msg.alert(gettext("Error"),e.htmlStatus),success:function(i,a){const n=i.result,o=!(!n||!n.data||"active"!==n.data.status.toLowerCase());t.set("subscriptionActive",o),e.getController().updateState()}})},updateStandardRepos:function(e){let t=this,i=t.getViewModel(),a=t.down("button[name=addRepo]");a.repoInfo=[];for(const n of e){const e=n.handle,o=n.status;"enterprise"===e?i.set("enterpriseRepo",o):"no-subscription"===e?i.set("noSubscriptionRepo",o):"test"===e?i.set("testRepo",o):e.match(/^ceph-[a-zA-Z]+-enterprise$/)?i.set("cephEnterpriseRepo",o):e.match(/^ceph-[a-zA-Z]+-no-subscription$/)?i.set("cephNoSubscriptionRepo",o):e.match(/^ceph-[a-zA-Z]+-test$/)&&i.set("cephTestRepo",o),t.getController().updateState(),a.repoInfo.push(n),a.digest=t.digest}a.setDisabled(!1)},reload:function(){let e=this,t=e.getViewModel(),i=e.down("proxmoxNodeAPTRepositoriesGrid");e.store.load(function(a,n,o){let r,l=[],s=[],d=!1,u=!1,c=!1;if(o&&a.length>0){let t=a[0].data,n=t.files;s=t.errors,r=t.digest;let o={};for(const e of t.infos){let t=e.path,a=e.index;o[t]||(o[t]={}),o[t][a]||(o[t][a]={origin:"",warnings:[],gotIgnorePreUpgradeWarning:!1}),"origin"===e.kind?o[t][a].origin=e.message:"warning"===e.kind?o[t][a].warnings.push(e):"ignore-pre-upgrade-warning"===e.kind&&(o[t][a].gotIgnorePreUpgradeWarning=!0,i.majorUpgradeAllowed?u=!0:o[t][a].warnings.push(e))}n.forEach(function(t){for(let i=0;i<t.repositories.length;i++){let a=t.repositories[i];if(a.Path=t.path,a.Index=i,o[t.path]&&o[t.path][i]&&(a.Origin=o[t.path][i].origin||Proxmox.Utils.unknownText,a.warnings=o[t.path][i].warnings||[],a.Enabled)){a.warnings.some(e=>"Suites"===e.property)&&(d=!0);let n=e.classifyOrigin(a.Origin);!u||!a.Types.includes("deb")||"Proxmox"!==n&&"Debian"!==n||o[t.path][i].gotIgnorePreUpgradeWarning||(c=!0)}l.push(a)}}),i.store.loadData(l),e.updateStandardRepos(t["standard-repos"])}e.digest=r,t.set("errors",s),t.set("suitesWarning",d),t.set("mixedSuites",c),e.getController().updateState()}),e.check_subscription()},listeners:{activate:function(){this.reload()}},initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";let t=Ext.create("Ext.data.Store",{proxy:{type:"proxmox",url:`/api2/json/nodes/${e.nodename}/apt/repositories`}});Ext.apply(e,{store:t}),Proxmox.Utils.monStoreErrors(e,e.store,!0),e.callParent(),e.getViewModel().set("product",e.product)}}),Ext.define("Proxmox.node.NetworkEdit",{extend:"Proxmox.window.Edit",alias:["widget.proxmoxNodeNetworkEdit"],enableBridgeVlanIds:!1,initComponent:function(){let e,t=this;if(!t.nodename)throw"no node name specified";if(!t.iftype)throw"no network device type specified";if(t.isCreate=!t.iface,"bridge"===t.iftype)e="BridgeName";else if("bond"===t.iftype)e="BondName";else if("eth"!==t.iftype||t.isCreate)if("vlan"===t.iftype)e="VlanName";else if("OVSBridge"===t.iftype)e="BridgeName";else if("OVSBond"===t.iftype)e="BondName";else if("OVSIntPort"===t.iftype)e="InterfaceName";else{if("OVSPort"!==t.iftype)throw console.log(t.iftype),"unknown network device type specified";e="InterfaceName"}else e="InterfaceName";t.subject=Proxmox.Utils.render_network_iface_type(t.iftype);let i,a,n=[],o=[],r=[],l=[],s=[];if("OVSIntPort"!==t.iftype&&"OVSPort"!==t.iftype&&"OVSBond"!==t.iftype&&o.push({xtype:"proxmoxcheckbox",fieldLabel:gettext("Autostart"),name:"autostart",uncheckedValue:0,checked:!!t.isCreate||void 0}),"bridge"===t.iftype){let e=t.enableBridgeVlanIds?Ext.create("Ext.form.field.Text",{fieldLabel:gettext("VLAN IDs"),name:"bridge_vids",emptyText:"2-4094",disabled:!0,autoEl:{tag:"div","data-qtip":gettext("List of VLAN IDs and ranges, useful for NICs with restricted VLAN offloading support. For example: '2 4 100-200'")},validator:function(e){if(!e)return!0;for(const t of e.split(/\s+[,;]?/)){if(!t)continue;let e=t.match(/^(\d+)(?:-(\d+))?$/);if(!e)return Ext.String.format(gettext("not a valid bridge VLAN ID entry: {0}"),t);let i=Number(e[1]),a=Number(e[2]??e[1]);if(Number.isNaN(i)||Number.isNaN(a))return Ext.String.format(gettext("VID range includes not-a-number: {0}"),t);if(i>a)return Ext.String.format(gettext("VID range must go from lower to higher tag: {0}"),t);if(i<2||a>4094)return Ext.String.format(gettext("VID range outside of allowed 2 and 4094 limit: {0}"),t)}return!0}}):void 0;o.push({xtype:"proxmoxcheckbox",fieldLabel:gettext("VLAN aware"),name:"bridge_vlan_aware",deleteEmpty:!t.isCreate,listeners:{change:function(t,i){e&&e.setDisabled(!i)}}}),o.push({xtype:"textfield",fieldLabel:gettext("Bridge ports"),name:"bridge_ports",autoEl:{tag:"div","data-qtip":gettext("Space-separated list of interfaces, for example: enp0s0 enp1s0")}}),e&&s.push(e)}else if("OVSBridge"===t.iftype)o.push({xtype:"textfield",fieldLabel:gettext("Bridge ports"),name:"ovs_ports",autoEl:{tag:"div","data-qtip":gettext("Space-separated list of interfaces, for example: enp0s0 enp1s0")}}),o.push({xtype:"textfield",fieldLabel:gettext("OVS options"),name:"ovs_options"});else if("OVSPort"===t.iftype||"OVSIntPort"===t.iftype)o.push({xtype:t.isCreate?"PVE.form.BridgeSelector":"displayfield",fieldLabel:Proxmox.Utils.render_network_iface_type("OVSBridge"),allowBlank:!1,nodename:t.nodename,bridgeType:"OVSBridge",name:"ovs_bridge"}),o.push({xtype:"proxmoxvlanfield",deleteEmpty:!t.isCreate,name:"ovs_tag",value:""}),o.push({xtype:"textfield",fieldLabel:gettext("OVS options"),name:"ovs_options"});else if("vlan"===t.iftype){if(t.isCreate)t.disablevlanid=!0,t.disablevlanrawdevice=!0;else if(t.disablevlanid=!1,t.disablevlanrawdevice=!1,t.vlanrawdevicevalue="",t.vlanidvalue="",Proxmox.Utils.VlanInterface_match.test(t.iface)){t.disablevlanid=!0,t.disablevlanrawdevice=!0;let e=Proxmox.Utils.VlanInterface_match.exec(t.iface);t.vlanrawdevicevalue=e[1],t.vlanidvalue=e[2]}else if(Proxmox.Utils.Vlan_match.test(t.iface)){t.disablevlanid=!0;let e=Proxmox.Utils.Vlan_match.exec(t.iface);t.vlanidvalue=e[1]}o.push({xtype:"textfield",fieldLabel:gettext("Vlan raw device"),name:"vlan-raw-device",value:t.vlanrawdevicevalue,disabled:t.disablevlanrawdevice,allowBlank:!1}),o.push({xtype:"proxmoxvlanfield",name:"vlan-id",value:t.vlanidvalue,disabled:t.disablevlanid}),r.push({xtype:"label",userCls:"pmx-hint",text:"Either add the VLAN number to an existing interface name, or choose your own name and set the VLAN raw device (for the latter ifupdown1 supports vlanXY naming only)"})}else if("bond"===t.iftype){o.push({xtype:"textfield",fieldLabel:gettext("Slaves"),name:"slaves"});let e=Ext.createWidget("bondPolicySelector",{fieldLabel:gettext("Hash policy"),name:"bond_xmit_hash_policy",deleteEmpty:!t.isCreate,disabled:!0}),i=Ext.createWidget("textfield",{fieldLabel:"bond-primary",name:"bond-primary",value:"",disabled:!0});o.push({xtype:"bondModeSelector",fieldLabel:gettext("Mode"),name:"bond_mode",value:t.isCreate?"balance-rr":void 0,listeners:{change:function(t,a){"balance-xor"===a||"802.3ad"===a?(e.setDisabled(!1),i.setDisabled(!0),i.setValue("")):"active-backup"===a?(i.setDisabled(!1),e.setDisabled(!0),e.setValue("")):(e.setDisabled(!0),e.setValue(""),i.setDisabled(!0),i.setValue(""))}},allowBlank:!1}),o.push(e),o.push(i)}else"OVSBond"===t.iftype&&(o.push({xtype:t.isCreate?"PVE.form.BridgeSelector":"displayfield",fieldLabel:Proxmox.Utils.render_network_iface_type("OVSBridge"),allowBlank:!1,nodename:t.nodename,bridgeType:"OVSBridge",name:"ovs_bridge"}),o.push({xtype:"proxmoxvlanfield",deleteEmpty:!t.isCreate,name:"ovs_tag",value:""}),o.push({xtype:"textfield",fieldLabel:gettext("OVS options"),name:"ovs_options"}));o.push({xtype:"textfield",fieldLabel:gettext("Comment"),allowBlank:!0,nodename:t.nodename,name:"comments"}),t.isCreate?(i="/api2/extjs/nodes/"+t.nodename+"/network",a="POST"):(i="/api2/extjs/nodes/"+t.nodename+"/network/"+t.iface,a="PUT"),n.push({xtype:"hiddenfield",name:"type",value:t.iftype},{xtype:t.isCreate?"textfield":"displayfield",fieldLabel:gettext("Name"),name:"iface",value:t.iface,vtype:e,allowBlank:!1,maxLength:"BridgeName"===e?10:15,autoEl:{tag:"div","data-qtip":gettext("For example, vmbr0.100, vmbr0, vlan0.100, vlan0")},listeners:{change:function(i,a){if(t.isCreate&&"VlanName"===e){let e=t.down("field[name=vlan-id]"),i=t.down("field[name=vlan-raw-device]");Proxmox.Utils.VlanInterface_match.test(a)?(e.setDisabled(!0),i.setDisabled(!0),e.setValue(a.match(Proxmox.Utils.VlanInterface_match)[2]),i.setValue(a.match(Proxmox.Utils.VlanInterface_match)[1])):Proxmox.Utils.Vlan_match.test(a)?(e.setDisabled(!0),e.setValue(a.match(Proxmox.Utils.Vlan_match)[1]),i.setDisabled(!1)):(e.setDisabled(!1),i.setDisabled(!1))}}}}),"OVSBond"===t.iftype?n.push({xtype:"bondModeSelector",fieldLabel:gettext("Mode"),name:"bond_mode",openvswitch:!0,value:t.isCreate?"active-backup":void 0,allowBlank:!1},{xtype:"textfield",fieldLabel:gettext("Slaves"),name:"ovs_bonds"}):n.push({xtype:"proxmoxtextfield",deleteEmpty:!t.isCreate,fieldLabel:"IPv4/CIDR",vtype:"IPCIDRAddress",name:"cidr"},{xtype:"proxmoxtextfield",deleteEmpty:!t.isCreate,fieldLabel:gettext("Gateway")+" (IPv4)",vtype:"IPAddress",name:"gateway"},{xtype:"proxmoxtextfield",deleteEmpty:!t.isCreate,fieldLabel:"IPv6/CIDR",vtype:"IP6CIDRAddress",name:"cidr6"},{xtype:"proxmoxtextfield",deleteEmpty:!t.isCreate,fieldLabel:gettext("Gateway")+" (IPv6)",vtype:"IP6Address",name:"gateway6"}),l.push({xtype:"proxmoxintegerfield",minValue:1280,maxValue:65520,deleteEmpty:!t.isCreate,emptyText:1500,fieldLabel:"MTU",name:"mtu"}),Ext.applyIf(t,{url:i,method:a,items:{xtype:"inputpanel",column1:n,column2:o,columnB:r,advancedColumn1:l,advancedColumn2:s}}),t.callParent(),t.isCreate?t.down("field[name=iface]").setValue(t.iface_default):t.load({success:function(e,i){let a=e.result.data;if(a.type!==t.iftype){let e="Got unexpected device type";return void Ext.Msg.alert(gettext("Error"),e,function(){t.close()})}t.setValues(a),t.isValid()}})}}),Ext.define("proxmox-networks",{extend:"Ext.data.Model",fields:["active","address","address6","autostart","bridge_ports","cidr","cidr6","comments","gateway","gateway6","iface","netmask","netmask6","slaves","type","vlan-id","vlan-raw-device"],idProperty:"iface"}),Ext.define("Proxmox.node.NetworkView",{extend:"Ext.panel.Panel",alias:["widget.proxmoxNodeNetworkView"],types:["bridge","bond","vlan","ovs"],showApplyBtn:!1,editOptions:{},initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";let t=`/nodes/${e.nodename}/network`,i=Ext.create("Ext.data.Store",{model:"proxmox-networks",proxy:{type:"proxmox",url:"/api2/json"+t},sorters:[{property:"iface",direction:"ASC"}]}),a=function(){let a=e.down("#changes"),n=e.down("#apply"),o=e.down("#revert");Proxmox.Utils.API2Request({url:t,failure:function(t,n){i.loadData({}),Proxmox.Utils.setErrorMask(e,t.htmlStatus),a.update(""),a.setHidden(!0)},success:function(e,t){let r=Ext.decode(e.responseText);i.loadData(r.data);let l=r.changes;void 0===l||""===l?(l=gettext("No changes"),a.setHidden(!0),n.setDisabled(!0),o.setDisabled(!0)):(a.update("<pre>"+Ext.htmlEncode(l)+"</pre>"),a.setHidden(!1),n.setDisabled(!1),o.setDisabled(!1))}})},n=function(){let t=e.down("gridpanel").getSelectionModel().getSelection()[0];t&&Ext.create("Proxmox.node.NetworkEdit",{autoShow:!0,nodename:e.nodename,iface:t.data.iface,iftype:t.data.type,...e.editOptions,listeners:{destroy:()=>a()}})},o=new Ext.Button({text:gettext("Edit"),disabled:!0,handler:n}),r=Ext.create("Ext.selection.RowModel",{}),l=new Proxmox.button.StdRemoveButton({selModel:r,getUrl:({data:e})=>`${t}/${e.iface}`,callback:()=>a()}),s=Ext.create("Proxmox.button.Button",{text:gettext("Apply Configuration"),itemId:"apply",disabled:!0,confirmMsg:"Do you want to apply pending network changes?",hidden:!e.showApplyBtn,handler:function(){Proxmox.Utils.API2Request({url:t,method:"PUT",waitMsgTarget:e,success:function({result:e},t){Ext.create("Proxmox.window.TaskProgress",{autoShow:!0,taskDone:a,upid:e.data})},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})}}),d=function(e){for(let t=0;t<=9999;t++){let a=`${e}${t.toString()}`;if(!i.getById(a))return a}return Ext.Msg.alert("Error",`No free ID for ${e} found!`),""},u=[],c=(t,i)=>{u.push({text:Proxmox.Utils.render_network_iface_type(t),handler:()=>Ext.create("Proxmox.node.NetworkEdit",{autoShow:!0,nodename:e.nodename,iftype:t,iface_default:d(i??t),...e.editOptions,onlineHelp:"sysadmin_network_configuration",listeners:{destroy:()=>a()}})})};-1!==e.types.indexOf("bridge")&&c("bridge","vmbr"),-1!==e.types.indexOf("bond")&&c("bond"),-1!==e.types.indexOf("vlan")&&c("vlan"),-1!==e.types.indexOf("ovs")&&(u.length>0&&u.push({xtype:"menuseparator"}),c("OVSBridge","vmbr"),c("OVSBond","bond"),u.push({text:Proxmox.Utils.render_network_iface_type("OVSIntPort"),handler:()=>Ext.create("Proxmox.node.NetworkEdit",{autoShow:!0,nodename:e.nodename,iftype:"OVSIntPort",listeners:{destroy:()=>a()}})}));let x=function(e){return function(t,i,a){let n=[];return a.data[e]&&n.push(a.data[e]),a.data[e+"6"]&&n.push(a.data[e+"6"]),n.join("<br>")||""}};Ext.apply(e,{layout:"border",tbar:[{text:gettext("Create"),menu:{plain:!0,items:u}},"-",{text:gettext("Revert"),itemId:"revert",handler:function(){Proxmox.Utils.API2Request({url:t,method:"DELETE",waitMsgTarget:e,callback:function(){a()},failure:e=>Ext.Msg.alert(gettext("Error"),e.htmlStatus)})}},o,l,"-",s],items:[{xtype:"gridpanel",stateful:!0,stateId:"grid-node-network",store:i,selModel:r,region:"center",border:!1,columns:[{header:gettext("Name"),sortable:!0,dataIndex:"iface"},{header:gettext("Type"),sortable:!0,width:120,renderer:Proxmox.Utils.render_network_iface_type,dataIndex:"type"},{xtype:"booleancolumn",header:gettext("Active"),width:80,sortable:!0,dataIndex:"active",trueText:Proxmox.Utils.yesText,falseText:Proxmox.Utils.noText,undefinedText:Proxmox.Utils.noText},{xtype:"booleancolumn",header:gettext("Autostart"),width:80,sortable:!0,dataIndex:"autostart",trueText:Proxmox.Utils.yesText,falseText:Proxmox.Utils.noText,undefinedText:Proxmox.Utils.noText},{xtype:"booleancolumn",header:gettext("VLAN aware"),width:80,sortable:!0,dataIndex:"bridge_vlan_aware",trueText:Proxmox.Utils.yesText,falseText:Proxmox.Utils.noText,undefinedText:Proxmox.Utils.noText},{header:gettext("Ports/Slaves"),dataIndex:"type",renderer:(e,t,{data:i})=>"bridge"===e?i.bridge_ports:"bond"===e?i.slaves:"OVSBridge"===e?i.ovs_ports:"OVSBond"===e?i.ovs_bonds:""},{header:gettext("Bond Mode"),dataIndex:"bond_mode",renderer:Proxmox.Utils.render_bond_mode},{header:gettext("Hash Policy"),hidden:!0,dataIndex:"bond_xmit_hash_policy"},{header:gettext("IP address"),sortable:!0,width:120,hidden:!0,dataIndex:"address",renderer:x("address")},{header:gettext("Subnet mask"),width:120,sortable:!0,hidden:!0,dataIndex:"netmask",renderer:x("netmask")},{header:gettext("CIDR"),width:150,sortable:!0,dataIndex:"cidr",renderer:x("cidr")},{header:gettext("Gateway"),width:150,sortable:!0,dataIndex:"gateway",renderer:x("gateway")},{header:gettext("VLAN ID"),hidden:!0,sortable:!0,dataIndex:"vlan-id"},{header:gettext("VLAN raw device"),hidden:!0,sortable:!0,dataIndex:"vlan-raw-device"},{header:"MTU",hidden:!0,sortable:!0,dataIndex:"mtu"},{header:gettext("Comment"),dataIndex:"comments",flex:1,renderer:Ext.String.htmlEncode}],listeners:{selectionchange:function(){let e=r.getSelection()[0];o.setDisabled(!e),l.setDisabled(!e)},itemdblclick:n}},{border:!1,region:"south",autoScroll:!0,hidden:!0,itemId:"changes",tbar:[gettext("Pending changes")+" ("+gettext("Either reboot or use 'Apply Configuration' (needs ifupdown2) to activate")+")"],split:!0,bodyPadding:5,flex:.6,html:gettext("No changes")}]}),e.callParent(),a()}}),Ext.define("Proxmox.node.DNSEdit",{extend:"Proxmox.window.Edit",alias:["widget.proxmoxNodeDNSEdit"],deleteEmpty:!1,initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";e.items=[{xtype:"textfield",fieldLabel:gettext("Search domain"),name:"search",allowBlank:!1},{xtype:"proxmoxtextfield",fieldLabel:gettext("DNS server")+" 1",vtype:"IP64Address",skipEmptyText:!0,deleteEmpty:e.deleteEmpty,name:"dns1"},{xtype:"proxmoxtextfield",fieldLabel:gettext("DNS server")+" 2",vtype:"IP64Address",skipEmptyText:!0,deleteEmpty:e.deleteEmpty,name:"dns2"},{xtype:"proxmoxtextfield",fieldLabel:gettext("DNS server")+" 3",vtype:"IP64Address",skipEmptyText:!0,deleteEmpty:e.deleteEmpty,name:"dns3"}],Ext.applyIf(e,{subject:gettext("DNS"),url:"/api2/extjs/nodes/"+e.nodename+"/dns",fieldDefaults:{labelWidth:120}}),e.callParent(),e.load()}}),Ext.define("Proxmox.node.HostsView",{extend:"Ext.panel.Panel",xtype:"proxmoxNodeHostsView",reload:function(){this.store.load()},tbar:[{text:gettext("Save"),disabled:!0,itemId:"savebtn",handler:function(){let e=this.up("panel");Proxmox.Utils.API2Request({params:{digest:e.digest,data:e.down("#hostsfield").getValue()},method:"POST",url:"/nodes/"+e.nodename+"/hosts",waitMsgTarget:e,success:function(t,i){e.reload()},failure:function(e,t){Ext.Msg.alert("Error",e.htmlStatus)}})}},{text:gettext("Revert"),disabled:!0,itemId:"resetbtn",handler:function(){this.up("panel").down("#hostsfield").reset()}}],layout:"fit",items:[{xtype:"textarea",itemId:"hostsfield",fieldStyle:{"font-family":"monospace","white-space":"pre"},listeners:{dirtychange:function(e,t){let i=this.up("panel");i.down("#savebtn").setDisabled(!t),i.down("#resetbtn").setDisabled(!t)}}}],initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";e.store=Ext.create("Ext.data.Store",{proxy:{type:"proxmox",url:"/api2/json/nodes/"+e.nodename+"/hosts"}}),e.callParent(),Proxmox.Utils.monStoreErrors(e,e.store),e.mon(e.store,"load",function(t,i,a){if(!a||i.length<1)return;e.digest=i[0].data.digest;let n=i[0].data.data;e.down("#hostsfield").setValue(n),e.down("#hostsfield").resetOriginalValue()}),e.reload()}}),Ext.define("Proxmox.node.DNSView",{extend:"Proxmox.grid.ObjectGrid",alias:["widget.proxmoxNodeDNSView"],deleteEmpty:!1,initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";let t=()=>Ext.create("Proxmox.node.DNSEdit",{autoShow:!0,nodename:e.nodename,deleteEmpty:e.deleteEmpty});Ext.apply(e,{url:`/api2/json/nodes/${e.nodename}/dns`,cwidth1:130,interval:1e4,run_editor:t,rows:{search:{header:gettext("Search domain"),required:!0,renderer:Ext.htmlEncode},dns1:{header:gettext("DNS server")+" 1",required:!0,renderer:Ext.htmlEncode},dns2:{header:gettext("DNS server")+" 2",renderer:Ext.htmlEncode},dns3:{header:gettext("DNS server")+" 3",renderer:Ext.htmlEncode}},tbar:[{text:gettext("Edit"),handler:t}],listeners:{itemdblclick:t}}),e.callParent(),e.on("activate",e.rstore.startUpdate),e.on("deactivate",e.rstore.stopUpdate),e.on("destroy",e.rstore.stopUpdate)}}),Ext.define("Proxmox.node.Tasks",{extend:"Ext.grid.GridPanel",alias:"widget.proxmoxNodeTasks",stateful:!0,stateId:"pve-grid-node-tasks",loadMask:!0,sortableColumns:!1,extraFilter:[],preFilter:{},controller:{xclass:"Ext.app.ViewController",showTaskLog:function(){let e=this.getView().getSelection();if(e.length<1)return;let t=e[0];Ext.create("Proxmox.window.TaskViewer",{upid:t.data.upid,endtime:t.data.endtime}).show()},updateLayout:function(e,t,i,a){let n=this.getView().getView();Proxmox.Utils.setErrorMask(n,!1),this.getView().updateLayout(),i||Proxmox.Utils.setErrorMask(n,Proxmox.Utils.getResponseErrorMessage(a.getError()))},refresh:function(){let e=this.getView(),t=e.getSelection(),i=this.getViewModel().get("bufferedstore");t&&t.length>0&&(i.contains(t[0])||e.setSelection(void 0))},sinceChange:function(e,t){this.getViewModel().set("since",t)},untilChange:function(e,t,i){this.getViewModel().set("until",t)},reload:function(){this.getView().getStore().load()},showFilter:function(e,t){this.getViewModel().set("showFilter",t)},clearFilter:function(){this.lookup("filtertoolbar").query("field").forEach(e=>{e.setValue(void 0)})}},listeners:{itemdblclick:"showTaskLog"},viewModel:{data:{typefilter:"",statusfilter:"",showFilter:!1,extraFilter:{},since:null,until:null},formulas:{filterIcon:e=>"fa fa-filter"+(e("showFilter")?" info-blue":""),extraParams:function(e){let t={};if(e("typefilter")&&(t.typefilter=e("typefilter")),e("statusfilter")&&(t.statusfilter=e("statusfilter")),e("extraFilter")){let i=e("extraFilter");for(const[e,a]of Object.entries(i))null!=a&&""!==a&&(t[e]=a)}if(e("since")&&(t.since=e("since").valueOf()/1e3),e("until")){let i=new Date(e("until").getTime());i.setDate(i.getDate()+1),t.until=i.valueOf()/1e3}return this.getView().getStore().load(),t},filterCount:function(e){let t=0;e("typefilter")&&t++;let i=e("statusfilter");if((Ext.isArray(i)&&i.length>0||!Ext.isArray(i)&&i)&&t++,e("since")&&t++,e("until")&&t++,e("extraFilter")){let i=e("preFilter")||{},a=e("extraFilter");for(const[e,n]of Object.entries(a))null!=n&&""!==n&&void 0===i[e]&&t++}return t},clearFilterText:function(e){let t=e("filterCount"),i="";return t>1?i=` (${t} ${gettext("Fields")})`:t>0&&(i=` (1 ${gettext("Field")})`),gettext("Clear Filter")+i}},stores:{bufferedstore:{type:"buffered",pageSize:500,autoLoad:!0,remoteFilter:!0,model:"proxmox-tasks",proxy:{type:"proxmox",startParam:"start",limitParam:"limit",extraParams:"{extraParams}",url:"{url}"},listeners:{prefetch:"updateLayout",refresh:"refresh"}}}},bind:{store:"{bufferedstore}"},dockedItems:[{xtype:"toolbar",items:[{xtype:"proxmoxButton",text:gettext("View Task"),iconCls:"fa fa-window-restore",disabled:!0,handler:"showTaskLog"},{xtype:"button",text:gettext("Reload"),iconCls:"fa fa-refresh",handler:"reload"},"->",{xtype:"button",bind:{text:"{clearFilterText}",disabled:"{!filterCount}"},text:gettext("Clear Filter"),enabled:!1,handler:"clearFilter"},{xtype:"button",enableToggle:!0,bind:{iconCls:"{filterIcon}"},text:gettext("Filter"),stateful:!0,stateId:"task-showfilter",stateEvents:["toggle"],applyState:function(e){void 0!==e.pressed&&this.setPressed(e.pressed)},getState:function(){return{pressed:this.pressed}},listeners:{toggle:"showFilter"}}]},{xtype:"toolbar",dock:"top",reference:"filtertoolbar",layout:{type:"hbox",align:"top"},bind:{hidden:"{!showFilter}"},items:[{xtype:"container",padding:10,layout:{type:"vbox",align:"stretch"},defaults:{labelWidth:80},items:[{xtype:"datefield",fieldLabel:gettext("Since"),format:"Y-m-d",bind:{maxValue:"{until}"},listeners:{change:"sinceChange"}},{xtype:"datefield",fieldLabel:gettext("Until"),format:"Y-m-d",bind:{minValue:"{since}"},listeners:{change:"untilChange"}}]},{xtype:"container",padding:10,layout:{type:"vbox",align:"stretch"},defaults:{labelWidth:80},items:[{xtype:"pmxTaskTypeSelector",fieldLabel:gettext("Task Type"),emptyText:gettext("All"),bind:{value:"{typefilter}"}},{xtype:"combobox",fieldLabel:gettext("Task Result"),emptyText:gettext("All"),multiSelect:!0,store:[["ok",gettext("OK")],["unknown",Proxmox.Utils.unknownText],["warning",gettext("Warnings")],["error",gettext("Errors")]],bind:{value:"{statusfilter}"}}]}]}],viewConfig:{trackOver:!1,stripeRows:!1,emptyText:gettext("No Tasks found"),getRowClass:function(e,t){let i=e.get("status");if(i){let e=Proxmox.Utils.parse_task_status(i);if("error"===e)return"proxmox-invalid-row";if("warning"===e)return"proxmox-warning-row"}return""}},columns:[{header:gettext("Start Time"),dataIndex:"starttime",width:130,renderer:function(e){return Ext.Date.format(e,"M d H:i:s")}},{header:gettext("End Time"),dataIndex:"endtime",width:130,renderer:function(e,t,i){return e?Ext.Date.format(e,"M d H:i:s"):(t.tdCls="x-grid-row-loading","")}},{header:gettext("Duration"),hidden:!0,width:80,renderer:function(e,t,i){let a=i.data.starttime;if(a){let e=(i.data.endtime||Date.now())-a;return e>0&&(e/=1e3),Proxmox.Utils.format_duration_human(e)}return Proxmox.Utils.unknownText}},{header:gettext("User name"),dataIndex:"user",width:150},{header:gettext("Description"),dataIndex:"upid",flex:1,renderer:Proxmox.Utils.render_upid},{header:gettext("Status"),dataIndex:"status",width:200,renderer:function(e,t,i){return void 0!==e||i.data.endtime?Proxmox.Utils.format_task_status(e):(t.tdCls="x-grid-row-loading","")}},{xtype:"actioncolumn",width:30,align:"center",tooltip:gettext("Actions"),items:[{iconCls:"fa fa-chevron-right",tooltip:gettext("View Task"),handler:function(e,t,i,a,n,o){Ext.create("Proxmox.window.TaskViewer",{autoShow:!0,upid:o.data.upid,endtime:o.data.endtime})}}]}],initComponent:function(){const e=this;let t=e.nodename||"localhost",i=e.url||`/api2/json/nodes/${t}/tasks`;e.getViewModel().set("url",i);let a=function(t,i){let a=e.getViewModel(),n=Ext.clone(a.get("extraFilter"));n[t]=i,a.set("extraFilter",n)};for(const[t,i]of Object.entries(e.preFilter))a(t,i);e.getViewModel().set("preFilter",e.preFilter),e.callParent();let n=function(t){e.lookup("filtertoolbar").add({xtype:"container",padding:10,layout:{type:"vbox",align:"stretch"},defaults:{labelWidth:80},items:t})};e.extraFilter=[{xtype:"textfield",fieldLabel:gettext("User name"),changeOptions:{buffer:500},name:"userfilter"},...e.extraFilter];let o=[];for(const t of e.extraFilter){let e=Ext.clone(t);e.listeners=e.listeners||{},e.listeners.change=Ext.apply(e.changeOptions||{},{fn:function(t,i){a(e.name,i)}}),o.push(e),2===o.length&&(n(o),o=[])}n(o)}}),Ext.define("proxmox-services",{extend:"Ext.data.Model",fields:["service","name","desc","state","unit-state","active-state"],idProperty:"service"}),Ext.define("Proxmox.node.ServiceView",{extend:"Ext.grid.GridPanel",alias:["widget.proxmoxNodeServiceView"],startOnlyServices:{},restartCommand:"restart",initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";let t=Ext.create("Proxmox.data.UpdateStore",{interval:1e3,model:"proxmox-services",proxy:{type:"proxmox",url:`/api2/json/nodes/${e.nodename}/services`}}),i=e=>"not-found"!==e.get("unit-state"),a=Ext.create("Proxmox.data.DiffStore",{rstore:t,sortAfterUpdate:!0,sorters:[{property:"name",direction:"ASC"}],filters:[i]}),n=Ext.create("Ext.form.field.Checkbox",{boxLabel:gettext("Show only installed services"),value:!0,boxLabelAlign:"before",listeners:{change:function(e,t){t?a.addFilter([i]):a.clearFilter()}}}),o=function(){let{data:{service:t}}=e.getSelectionModel().getSelection()[0];Ext.create("Ext.window.Window",{title:gettext("Syslog")+": "+t,modal:!0,width:800,height:400,layout:"fit",items:{xtype:"proxmoxLogView",url:`/api2/extjs/nodes/${e.nodename}/syslog?service=${t}`,log_select_timespan:1},autoShow:!0})},r=function(i){let{data:{service:a}}=e.getSelectionModel().getSelection()[0];Proxmox.Utils.API2Request({url:`/nodes/${e.nodename}/services/${a}/${i}`,method:"POST",failure:function(t,i){Ext.Msg.alert(gettext("Error"),t.htmlStatus),e.loading=!0},success:function(e,i){t.startUpdate(),Ext.create("Proxmox.window.TaskProgress",{upid:e.result.data,autoShow:!0})}})},l=new Ext.Button({text:gettext("Start"),disabled:!0,handler:()=>r("start")}),s=new Ext.Button({text:gettext("Stop"),disabled:!0,handler:()=>r("stop")}),d=new Ext.Button({text:gettext("Restart"),disabled:!0,handler:()=>r(e.restartCommand||"restart")}),u=new Ext.Button({text:gettext("Syslog"),disabled:!0,handler:o}),c=function(){let t=e.getSelectionModel().getSelection()[0];if(!t)return l.disable(),s.disable(),d.disable(),void u.disable();let i=t.data.service,a=t.data.state,n=t.data["unit-state"];u.enable(),"running"===a?e.startOnlyServices[i]?(s.disable(),d.enable()):(s.enable(),d.enable(),l.disable()):void 0===n||"masked"!==n&&"unknown"!==n&&"not-found"!==n?(l.enable(),s.disable(),d.disable()):(l.disable(),d.disable(),s.disable())};e.mon(a,"refresh",c),Proxmox.Utils.monStoreErrors(e,t),Ext.apply(e,{viewConfig:{trackOver:!1,stripeRows:!1,getRowClass:function(e,t){let i=e.get("unit-state");return i?"masked"===i||"not-found"===i?"proxmox-disabled-row":"unknown"===i?"syslog"===e.get("name")?"proxmox-disabled-row":"proxmox-warning-row":"":""}},store:a,stateful:!1,tbar:[l,s,d,"-",u,"->",n],columns:[{header:gettext("Name"),flex:1,sortable:!0,dataIndex:"name"},{header:gettext("Status"),width:100,sortable:!0,dataIndex:"state",renderer:(e,t,i)=>{const a=i.get("unit-state");return"masked"===a?gettext("disabled"):"not-found"===a?gettext("not installed"):e}},{header:gettext("Active"),width:100,sortable:!0,hidden:!0,dataIndex:"active-state"},{header:gettext("Unit"),width:120,sortable:!0,hidden:!Ext.Array.contains(["PVEAuthCookie","PBSAuthCookie"],Proxmox?.Setup?.auth_cookie_name),dataIndex:"unit-state"},{header:gettext("Description"),renderer:Ext.String.htmlEncode,dataIndex:"desc",flex:2}],listeners:{selectionchange:c,itemdblclick:o,activate:t.startUpdate,destroy:t.stopUpdate}}),e.callParent()}}),Ext.define("Proxmox.node.TimeEdit",{extend:"Proxmox.window.Edit",alias:["widget.proxmoxNodeTimeEdit"],subject:gettext("Time zone"),width:400,autoLoad:!0,fieldDefaults:{labelWidth:70},items:{xtype:"combo",fieldLabel:gettext("Time zone"),name:"timezone",queryMode:"local",store:Ext.create("Proxmox.data.TimezoneStore"),displayField:"zone",editable:!0,anyMatch:!0,forceSelection:!0,allowBlank:!1},initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";e.url="/api2/extjs/nodes/"+e.nodename+"/time",e.callParent()}}),Ext.define("Proxmox.node.TimeView",{extend:"Proxmox.grid.ObjectGrid",alias:["widget.proxmoxNodeTimeView"],initComponent:function(){let e=this;if(!e.nodename)throw"no node name specified";let t=6e4*(new Date).getTimezoneOffset(),i=()=>Ext.create("Proxmox.node.TimeEdit",{autoShow:!0,nodename:e.nodename});Ext.apply(e,{url:`/api2/json/nodes/${e.nodename}/time`,cwidth1:150,interval:1e3,run_editor:i,rows:{timezone:{header:gettext("Time zone"),required:!0},localtime:{header:gettext("Server time"),required:!0,renderer:function(e){let i=new Date(1e3*e+t);return Ext.Date.format(i,"Y-m-d H:i:s")}}},tbar:[{text:gettext("Edit"),handler:i}],listeners:{itemdblclick:i}}),e.callParent(),e.on("activate",e.rstore.startUpdate),e.on("deactivate",e.rstore.stopUpdate),e.on("destroy",e.rstore.stopUpdate)}}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).marked={})}(this,function(e){"use strict";function t(e,t){for(var i=0;i<t.length;i++){var a=t[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,n(a.key),a)}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,a=new Array(t);i<t;i++)a[i]=e[i];return a}function a(e,t){var a="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(a)return(a=a.call(e)).next.bind(a);if(Array.isArray(e)||(a=function(e,t){if(e){if("string"==typeof e)return i(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){a&&(e=a);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function n(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var a=i.call(e,t||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function o(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}e.defaults={async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1};var r=/[&<>"']/,l=new RegExp(r.source,"g"),s=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,d=new RegExp(s.source,"g"),u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},c=function(e){return u[e]};function x(e,t){if(t){if(r.test(e))return e.replace(l,c)}else if(s.test(e))return e.replace(d,c);return e}var m=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function p(e){return e.replace(m,function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""})}var f=/(^|[^\[])\^/g;function g(e,t){e="string"==typeof e?e:e.source,t=t||"";var i={replace:function(t,a){return a=(a=a.source||a).replace(f,"$1"),e=e.replace(t,a),i},getRegex:function(){return new RegExp(e,t)}};return i}var h=/[^\w:]/g,b=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function y(e,t,i){if(e){var a;try{a=decodeURIComponent(p(i)).replace(h,"").toLowerCase()}catch(e){return null}if(0===a.indexOf("javascript:")||0===a.indexOf("vbscript:")||0===a.indexOf("data:"))return null}t&&!b.test(i)&&(i=function(e,t){w[" "+e]||(E.test(e)?w[" "+e]=e+"/":w[" "+e]=A(e,"/",!0));e=w[" "+e];var i=-1===e.indexOf(":");return"//"===t.substring(0,2)?i?t:e.replace(D,"$1")+t:"/"===t.charAt(0)?i?t:e.replace(v,"$1")+t:e+t}(t,i));try{i=encodeURI(i).replace(/%25/g,"%")}catch(e){return null}return i}var w={},E=/^[^:]+:\/*[^/]*$/,D=/^([^:]+:)[\s\S]*$/,v=/^([^:]+:\/*[^/]*)[\s\S]*$/;var P={exec:function(){}};function C(e){for(var t,i,a=1;a<arguments.length;a++)for(i in t=arguments[a])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}function k(e,t){var i=e.replace(/\|/g,function(e,t,i){for(var a=!1,n=t;--n>=0&&"\\"===i[n];)a=!a;return a?"|":" |"}).split(/ \|/),a=0;if(i[0].trim()||i.shift(),i.length>0&&!i[i.length-1].trim()&&i.pop(),i.length>t)i.splice(t);else for(;i.length<t;)i.push("");for(;a<i.length;a++)i[a]=i[a].trim().replace(/\\\|/g,"|");return i}function A(e,t,i){var a=e.length;if(0===a)return"";for(var n=0;n<a;){var o=e.charAt(a-n-1);if(o!==t||i){if(o===t||!i)break;n++}else n++}return e.slice(0,a-n)}function S(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function T(e,t){if(t<1)return"";for(var i="";t>1;)1&t&&(i+=e),t>>=1,e+=e;return i+e}function F(e,t,i,a){var n=t.href,o=t.title?x(t.title):null,r=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){a.state.inLink=!0;var l={type:"link",raw:i,href:n,title:o,text:r,tokens:a.inlineTokens(r)};return a.state.inLink=!1,l}return{type:"image",raw:i,href:n,title:o,text:x(r)}}var V=function(){function t(t){this.options=t||e.defaults}var i=t.prototype;return i.space=function(e){var t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}},i.code=function(e){var t=this.rules.block.code.exec(e);if(t){var i=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?i:A(i,"\n")}}},i.fences=function(e){var t=this.rules.block.fences.exec(e);if(t){var i=t[0],a=function(e,t){var i=e.match(/^(\s+)(?:```)/);if(null===i)return t;var a=i[1];return t.split("\n").map(function(e){var t=e.match(/^\s+/);return null===t?e:t[0].length>=a.length?e.slice(a.length):e}).join("\n")}(i,t[3]||"");return{type:"code",raw:i,lang:t[2]?t[2].trim().replace(this.rules.inline._escapes,"$1"):t[2],text:a}}},i.heading=function(e){var t=this.rules.block.heading.exec(e);if(t){var i=t[2].trim();if(/#$/.test(i)){var a=A(i,"#");this.options.pedantic?i=a.trim():a&&!/ $/.test(a)||(i=a.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:i,tokens:this.lexer.inline(i)}}},i.hr=function(e){var t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}},i.blockquote=function(e){var t=this.rules.block.blockquote.exec(e);if(t){var i=t[0].replace(/^ *>[ \t]?/gm,"");return{type:"blockquote",raw:t[0],tokens:this.lexer.blockTokens(i,[]),text:i}}},i.list=function(e){var t=this.rules.block.list.exec(e);if(t){var i,n,o,r,l,s,d,u,c,x,m,p,f=t[1].trim(),g=f.length>1,h={type:"list",raw:"",ordered:g,start:g?+f.slice(0,-1):"",loose:!1,items:[]};f=g?"\\d{1,9}\\"+f.slice(-1):"\\"+f,this.options.pedantic&&(f=g?f:"[*+-]");for(var b=new RegExp("^( {0,3}"+f+")((?:[\t ][^\\n]*)?(?:\\n|$))");e&&(p=!1,t=b.exec(e))&&!this.rules.block.hr.test(e);){if(i=t[0],e=e.substring(i.length),u=t[2].split("\n",1)[0],c=e.split("\n",1)[0],this.options.pedantic?(r=2,m=u.trimLeft()):(r=(r=t[2].search(/[^ ]/))>4?1:r,m=u.slice(r),r+=t[1].length),s=!1,!u&&/^ *$/.test(c)&&(i+=c+"\n",e=e.substring(c.length+1),p=!0),!p)for(var y=new RegExp("^ {0,"+Math.min(3,r-1)+"}(?:[*+-]|\\d{1,9}[.)])((?: [^\\n]*)?(?:\\n|$))"),w=new RegExp("^ {0,"+Math.min(3,r-1)+"}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)"),E=new RegExp("^ {0,"+Math.min(3,r-1)+"}(?:```|~~~)"),D=new RegExp("^ {0,"+Math.min(3,r-1)+"}#");e&&(u=x=e.split("\n",1)[0],this.options.pedantic&&(u=u.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!E.test(u))&&!D.test(u)&&!y.test(u)&&!w.test(e);){if(u.search(/[^ ]/)>=r||!u.trim())m+="\n"+u.slice(r);else{if(s)break;m+="\n"+u}s||u.trim()||(s=!0),i+=x+"\n",e=e.substring(x.length+1)}h.loose||(d?h.loose=!0:/\n *\n *$/.test(i)&&(d=!0)),this.options.gfm&&(n=/^\[[ xX]\] /.exec(m))&&(o="[ ] "!==n[0],m=m.replace(/^\[[ xX]\] +/,"")),h.items.push({type:"list_item",raw:i,task:!!n,checked:o,loose:!1,text:m}),h.raw+=i}h.items[h.items.length-1].raw=i.trimRight(),h.items[h.items.length-1].text=m.trimRight(),h.raw=h.raw.trimRight();var v=h.items.length;for(l=0;l<v;l++){this.lexer.state.top=!1,h.items[l].tokens=this.lexer.blockTokens(h.items[l].text,[]);var P=h.items[l].tokens.filter(function(e){return"space"===e.type}),C=P.every(function(e){for(var t,i=0,n=a(e.raw.split(""));!(t=n()).done;){if("\n"===t.value&&(i+=1),i>1)return!0}return!1});!h.loose&&P.length&&C&&(h.loose=!0,h.items[l].loose=!0)}return h}},i.html=function(e){var t=this.rules.block.html.exec(e);if(t){var i={type:"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:t[0]};if(this.options.sanitize){var a=this.options.sanitizer?this.options.sanitizer(t[0]):x(t[0]);i.type="paragraph",i.text=a,i.tokens=this.lexer.inline(a)}return i}},i.def=function(e){var t=this.rules.block.def.exec(e);if(t){var i=t[1].toLowerCase().replace(/\s+/g," "),a=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",n=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline._escapes,"$1"):t[3];return{type:"def",tag:i,raw:t[0],href:a,title:n}}},i.table=function(e){var t=this.rules.block.table.exec(e);if(t){var i={type:"table",header:k(t[1]).map(function(e){return{text:e}}),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(i.header.length===i.align.length){i.raw=t[0];var a,n,o,r,l=i.align.length;for(a=0;a<l;a++)/^ *-+: *$/.test(i.align[a])?i.align[a]="right":/^ *:-+: *$/.test(i.align[a])?i.align[a]="center":/^ *:-+ *$/.test(i.align[a])?i.align[a]="left":i.align[a]=null;for(l=i.rows.length,a=0;a<l;a++)i.rows[a]=k(i.rows[a],i.header.length).map(function(e){return{text:e}});for(l=i.header.length,n=0;n<l;n++)i.header[n].tokens=this.lexer.inline(i.header[n].text);for(l=i.rows.length,n=0;n<l;n++)for(r=i.rows[n],o=0;o<r.length;o++)r[o].tokens=this.lexer.inline(r[o].text);return i}}},i.lheading=function(e){var t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}},i.paragraph=function(e){var t=this.rules.block.paragraph.exec(e);if(t){var i="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:i,tokens:this.lexer.inline(i)}}},i.text=function(e){var t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}},i.escape=function(e){var t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:x(t[1])}},i.tag=function(e){var t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):x(t[0]):t[0]}},i.link=function(e){var t=this.rules.inline.link.exec(e);if(t){var i=t[2].trim();if(!this.options.pedantic&&/^</.test(i)){if(!/>$/.test(i))return;var a=A(i.slice(0,-1),"\\");if((i.length-a.length)%2==0)return}else{var n=function(e,t){if(-1===e.indexOf(t[1]))return-1;for(var i=e.length,a=0,n=0;n<i;n++)if("\\"===e[n])n++;else if(e[n]===t[0])a++;else if(e[n]===t[1]&&--a<0)return n;return-1}(t[2],"()");if(n>-1){var o=(0===t[0].indexOf("!")?5:4)+t[1].length+n;t[2]=t[2].substring(0,n),t[0]=t[0].substring(0,o).trim(),t[3]=""}}var r=t[2],l="";if(this.options.pedantic){var s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r);s&&(r=s[1],l=s[3])}else l=t[3]?t[3].slice(1,-1):"";return r=r.trim(),/^</.test(r)&&(r=this.options.pedantic&&!/>$/.test(i)?r.slice(1):r.slice(1,-1)),F(t,{href:r?r.replace(this.rules.inline._escapes,"$1"):r,title:l?l.replace(this.rules.inline._escapes,"$1"):l},t[0],this.lexer)}},i.reflink=function(e,t){var i;if((i=this.rules.inline.reflink.exec(e))||(i=this.rules.inline.nolink.exec(e))){var a=(i[2]||i[1]).replace(/\s+/g," ");if(!(a=t[a.toLowerCase()])){var n=i[0].charAt(0);return{type:"text",raw:n,text:n}}return F(i,a,i[0],this.lexer)}},i.emStrong=function(e,t,i){void 0===i&&(i="");var a=this.rules.inline.emStrong.lDelim.exec(e);if(a&&(!a[3]||!i.match(/(?:[0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xBC-\xBE\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u0660-\u0669\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07C0-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0966-\u096F\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09E6-\u09F1\u09F4-\u09F9\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A66-\u0A6F\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AE6-\u0AEF\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B66-\u0B6F\u0B71-\u0B77\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0BE6-\u0BF2\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C66-\u0C6F\u0C78-\u0C7E\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CE6-\u0CEF\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D58-\u0D61\u0D66-\u0D78\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DE6-\u0DEF\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F20-\u0F33\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F-\u1049\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u1090-\u1099\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A16\u1A20-\u1A54\u1A80-\u1A89\u1A90-\u1A99\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B50-\u1B59\u1B83-\u1BA0\u1BAE-\u1BE5\u1C00-\u1C23\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA830-\uA835\uA840-\uA873\uA882-\uA8B3\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA900-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF-\uA9D9\uA9E0-\uA9E4\uA9E6-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA50-\uAA59\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDE80-\uDE9C\uDEA0-\uDED0\uDEE1-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE40-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE4\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD23\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF27\uDF30-\uDF45\uDF51-\uDF54\uDF70-\uDF81\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC52-\uDC6F\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD03-\uDD26\uDD36-\uDD3F\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDD0-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE2B\uDE3F\uDE40\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDEF0-\uDEF9\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC50-\uDC59\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE50-\uDE59\uDE80-\uDEAA\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF2\uDF02\uDF04-\uDF10\uDF12-\uDF33\uDF50-\uDF59\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC41-\uDC46]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD834[\uDEC0-\uDED3\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC30-\uDC6D\uDD00-\uDD2C\uDD37-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB\uDEF0-\uDEF9]|\uD839[\uDCD0-\uDCEB\uDCF0-\uDCF9\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCCF\uDD00-\uDD43\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF])/))){var n=a[1]||a[2]||"";if(!n||n&&(""===i||this.rules.inline.punctuation.exec(i))){var o,r,l=a[0].length-1,s=l,d=0,u="*"===a[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(u.lastIndex=0,t=t.slice(-1*e.length+l);null!=(a=u.exec(t));)if(o=a[1]||a[2]||a[3]||a[4]||a[5]||a[6])if(r=o.length,a[3]||a[4])s+=r;else if(!((a[5]||a[6])&&l%3)||(l+r)%3){if(!((s-=r)>0)){r=Math.min(r,r+s+d);var c=e.slice(0,l+a.index+(a[0].length-o.length)+r);if(Math.min(l,r)%2){var x=c.slice(1,-1);return{type:"em",raw:c,text:x,tokens:this.lexer.inlineTokens(x)}}var m=c.slice(2,-2);return{type:"strong",raw:c,text:m,tokens:this.lexer.inlineTokens(m)}}}else d+=r}}},i.codespan=function(e){var t=this.rules.inline.code.exec(e);if(t){var i=t[2].replace(/\n/g," "),a=/[^ ]/.test(i),n=/^ /.test(i)&&/ $/.test(i);return a&&n&&(i=i.substring(1,i.length-1)),i=x(i,!0),{type:"codespan",raw:t[0],text:i}}},i.br=function(e){var t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}},i.del=function(e){var t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}},i.autolink=function(e,t){var i,a,n=this.rules.inline.autolink.exec(e);if(n)return a="@"===n[2]?"mailto:"+(i=x(this.options.mangle?t(n[1]):n[1])):i=x(n[1]),{type:"link",raw:n[0],text:i,href:a,tokens:[{type:"text",raw:i,text:i}]}},i.url=function(e,t){var i;if(i=this.rules.inline.url.exec(e)){var a,n;if("@"===i[2])n="mailto:"+(a=x(this.options.mangle?t(i[0]):i[0]));else{var o;do{o=i[0],i[0]=this.rules.inline._backpedal.exec(i[0])[0]}while(o!==i[0]);a=x(i[0]),n="www."===i[1]?"http://"+a:a}return{type:"link",raw:i[0],text:a,href:n,tokens:[{type:"text",raw:a,text:a}]}}},i.inlineText=function(e,t){var i,a=this.rules.inline.text.exec(e);if(a)return i=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(a[0]):x(a[0]):a[0]:x(this.options.smartypants?t(a[0]):a[0]),{type:"text",raw:a[0],text:i}},t}(),_={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:P,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};_.def=g(_.def).replace("label",_._label).replace("title",_._title).getRegex(),_.bullet=/(?:[*+-]|\d{1,9}[.)])/,_.listItemStart=g(/^( *)(bull) */).replace("bull",_.bullet).getRegex(),_.list=g(_.list).replace(/bull/g,_.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+_.def.source+")").getRegex(),_._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",_._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,_.html=g(_.html,"i").replace("comment",_._comment).replace("tag",_._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),_.paragraph=g(_._paragraph).replace("hr",_.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_._tag).getRegex(),_.blockquote=g(_.blockquote).replace("paragraph",_.paragraph).getRegex(),_.normal=C({},_),_.gfm=C({},_.normal,{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),_.gfm.table=g(_.gfm.table).replace("hr",_.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_._tag).getRegex(),_.gfm.paragraph=g(_._paragraph).replace("hr",_.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",_.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_._tag).getRegex(),_.pedantic=C({},_.normal,{html:g("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",_._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:P,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:g(_.normal._paragraph).replace("hr",_.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",_.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()});var U={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:P,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:P,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function B(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function M(e){var t,i,a="",n=e.length;for(t=0;t<n;t++)i=e.charCodeAt(t),Math.random()>.5&&(i="x"+i.toString(16)),a+="&#"+i+";";return a}U._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",U.punctuation=g(U.punctuation).replace(/punctuation/g,U._punctuation).getRegex(),U.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,U.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g,U._comment=g(_._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),U.emStrong.lDelim=g(U.emStrong.lDelim).replace(/punct/g,U._punctuation).getRegex(),U.emStrong.rDelimAst=g(U.emStrong.rDelimAst,"g").replace(/punct/g,U._punctuation).getRegex(),U.emStrong.rDelimUnd=g(U.emStrong.rDelimUnd,"g").replace(/punct/g,U._punctuation).getRegex(),U._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,U._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,U._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,U.autolink=g(U.autolink).replace("scheme",U._scheme).replace("email",U._email).getRegex(),U._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,U.tag=g(U.tag).replace("comment",U._comment).replace("attribute",U._attribute).getRegex(),U._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,U._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,U._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,U.link=g(U.link).replace("label",U._label).replace("href",U._href).replace("title",U._title).getRegex(),U.reflink=g(U.reflink).replace("label",U._label).replace("ref",_._label).getRegex(),U.nolink=g(U.nolink).replace("ref",_._label).getRegex(),U.reflinkSearch=g(U.reflinkSearch,"g").replace("reflink",U.reflink).replace("nolink",U.nolink).getRegex(),U.normal=C({},U),U.pedantic=C({},U.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:g(/^!?\[(label)\]\((.*?)\)/).replace("label",U._label).getRegex(),reflink:g(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",U._label).getRegex()}),U.gfm=C({},U.normal,{escape:g(U.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),U.gfm.url=g(U.gfm.url,"i").replace("email",U.gfm._extended_email).getRegex(),U.breaks=C({},U.gfm,{br:g(U.br).replace("{2,}","*").getRegex(),text:g(U.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});var I=function(){function i(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||e.defaults,this.options.tokenizer=this.options.tokenizer||new V,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};var i={block:_.normal,inline:U.normal};this.options.pedantic?(i.block=_.pedantic,i.inline=U.pedantic):this.options.gfm&&(i.block=_.gfm,this.options.breaks?i.inline=U.breaks:i.inline=U.gfm),this.tokenizer.rules=i}i.lex=function(e,t){return new i(t).lex(e)},i.lexInline=function(e,t){return new i(t).inlineTokens(e)};var a,n,o,r=i.prototype;return r.lex=function(e){var t;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens},r.blockTokens=function(e,t){var i,a,n,o,r=this;for(void 0===t&&(t=[]),e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,function(e,t,i){return t+"    ".repeat(i.length)});e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(function(a){return!!(i=a.call({lexer:r},e,t))&&(e=e.substring(i.raw.length),t.push(i),!0)})))if(i=this.tokenizer.space(e))e=e.substring(i.raw.length),1===i.raw.length&&t.length>0?t[t.length-1].raw+="\n":t.push(i);else if(i=this.tokenizer.code(e))e=e.substring(i.raw.length),!(a=t[t.length-1])||"paragraph"!==a.type&&"text"!==a.type?t.push(i):(a.raw+="\n"+i.raw,a.text+="\n"+i.text,this.inlineQueue[this.inlineQueue.length-1].src=a.text);else if(i=this.tokenizer.fences(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.heading(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.hr(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.blockquote(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.list(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.html(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.def(e))e=e.substring(i.raw.length),!(a=t[t.length-1])||"paragraph"!==a.type&&"text"!==a.type?this.tokens.links[i.tag]||(this.tokens.links[i.tag]={href:i.href,title:i.title}):(a.raw+="\n"+i.raw,a.text+="\n"+i.raw,this.inlineQueue[this.inlineQueue.length-1].src=a.text);else if(i=this.tokenizer.table(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.lheading(e))e=e.substring(i.raw.length),t.push(i);else if(n=e,this.options.extensions&&this.options.extensions.startBlock&&function(){var t=1/0,i=e.slice(1),a=void 0;r.options.extensions.startBlock.forEach(function(e){"number"==typeof(a=e.call({lexer:this},i))&&a>=0&&(t=Math.min(t,a))}),t<1/0&&t>=0&&(n=e.substring(0,t+1))}(),this.state.top&&(i=this.tokenizer.paragraph(n)))a=t[t.length-1],o&&"paragraph"===a.type?(a.raw+="\n"+i.raw,a.text+="\n"+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=a.text):t.push(i),o=n.length!==e.length,e=e.substring(i.raw.length);else if(i=this.tokenizer.text(e))e=e.substring(i.raw.length),(a=t[t.length-1])&&"text"===a.type?(a.raw+="\n"+i.raw,a.text+="\n"+i.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=a.text):t.push(i);else if(e){var l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}return this.state.top=!0,t},r.inline=function(e,t){return void 0===t&&(t=[]),this.inlineQueue.push({src:e,tokens:t}),t},r.inlineTokens=function(e,t){var i,a,n,o=this;void 0===t&&(t=[]);var r,l,s,d=e;if(this.tokens.links){var u=Object.keys(this.tokens.links);if(u.length>0)for(;null!=(r=this.tokenizer.rules.inline.reflinkSearch.exec(d));)u.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(d=d.slice(0,r.index)+"["+T("a",r[0].length-2)+"]"+d.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(r=this.tokenizer.rules.inline.blockSkip.exec(d));)d=d.slice(0,r.index)+"["+T("a",r[0].length-2)+"]"+d.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(r=this.tokenizer.rules.inline.escapedEmSt.exec(d));)d=d.slice(0,r.index+r[0].length-2)+"++"+d.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;e;)if(l||(s=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(function(a){return!!(i=a.call({lexer:o},e,t))&&(e=e.substring(i.raw.length),t.push(i),!0)})))if(i=this.tokenizer.escape(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.tag(e))e=e.substring(i.raw.length),(a=t[t.length-1])&&"text"===i.type&&"text"===a.type?(a.raw+=i.raw,a.text+=i.text):t.push(i);else if(i=this.tokenizer.link(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(i.raw.length),(a=t[t.length-1])&&"text"===i.type&&"text"===a.type?(a.raw+=i.raw,a.text+=i.text):t.push(i);else if(i=this.tokenizer.emStrong(e,d,s))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.codespan(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.br(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.del(e))e=e.substring(i.raw.length),t.push(i);else if(i=this.tokenizer.autolink(e,M))e=e.substring(i.raw.length),t.push(i);else if(this.state.inLink||!(i=this.tokenizer.url(e,M))){if(n=e,this.options.extensions&&this.options.extensions.startInline&&function(){var t=1/0,i=e.slice(1),a=void 0;o.options.extensions.startInline.forEach(function(e){"number"==typeof(a=e.call({lexer:this},i))&&a>=0&&(t=Math.min(t,a))}),t<1/0&&t>=0&&(n=e.substring(0,t+1))}(),i=this.tokenizer.inlineText(n,B))e=e.substring(i.raw.length),"_"!==i.raw.slice(-1)&&(s=i.raw.slice(-1)),l=!0,(a=t[t.length-1])&&"text"===a.type?(a.raw+=i.raw,a.text+=i.text):t.push(i);else if(e){var c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(i.raw.length),t.push(i);return t},a=i,o=[{key:"rules",get:function(){return{block:_,inline:U}}}],(n=null)&&t(a.prototype,n),o&&t(a,o),Object.defineProperty(a,"prototype",{writable:!1}),i}(),R=function(){function t(t){this.options=t||e.defaults}var i=t.prototype;return i.code=function(e,t,i){var a=(t||"").match(/\S*/)[0];if(this.options.highlight){var n=this.options.highlight(e,a);null!=n&&n!==e&&(i=!0,e=n)}return e=e.replace(/\n$/,"")+"\n",a?'<pre><code class="'+this.options.langPrefix+x(a)+'">'+(i?e:x(e,!0))+"</code></pre>\n":"<pre><code>"+(i?e:x(e,!0))+"</code></pre>\n"},i.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},i.html=function(e){return e},i.heading=function(e,t,i,a){return this.options.headerIds?"<h"+t+' id="'+(this.options.headerPrefix+a.slug(i))+'">'+e+"</h"+t+">\n":"<h"+t+">"+e+"</h"+t+">\n"},i.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},i.list=function(e,t,i){var a=t?"ol":"ul";return"<"+a+(t&&1!==i?' start="'+i+'"':"")+">\n"+e+"</"+a+">\n"},i.listitem=function(e){return"<li>"+e+"</li>\n"},i.checkbox=function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},i.paragraph=function(e){return"<p>"+e+"</p>\n"},i.table=function(e,t){return t&&(t="<tbody>"+t+"</tbody>"),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"},i.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},i.tablecell=function(e,t){var i=t.header?"th":"td";return(t.align?"<"+i+' align="'+t.align+'">':"<"+i+">")+e+"</"+i+">\n"},i.strong=function(e){return"<strong>"+e+"</strong>"},i.em=function(e){return"<em>"+e+"</em>"},i.codespan=function(e){return"<code>"+e+"</code>"},i.br=function(){return this.options.xhtml?"<br/>":"<br>"},i.del=function(e){return"<del>"+e+"</del>"},i.link=function(e,t,i){if(null===(e=y(this.options.sanitize,this.options.baseUrl,e)))return i;var a='<a href="'+e+'"';return t&&(a+=' title="'+t+'"'),a+=">"+i+"</a>"},i.image=function(e,t,i){if(null===(e=y(this.options.sanitize,this.options.baseUrl,e)))return i;var a='<img src="'+e+'" alt="'+i+'"';return t&&(a+=' title="'+t+'"'),a+=this.options.xhtml?"/>":">"},i.text=function(e){return e},t}(),L=function(){function e(){}var t=e.prototype;return t.strong=function(e){return e},t.em=function(e){return e},t.codespan=function(e){return e},t.del=function(e){return e},t.html=function(e){return e},t.text=function(e){return e},t.link=function(e,t,i){return""+i},t.image=function(e,t,i){return""+i},t.br=function(){return""},e}(),N=function(){function e(){this.seen={}}var t=e.prototype;return t.serialize=function(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")},t.getNextSafeSlug=function(e,t){var i=e,a=0;if(this.seen.hasOwnProperty(i)){a=this.seen[e];do{i=e+"-"+ ++a}while(this.seen.hasOwnProperty(i))}return t||(this.seen[e]=a,this.seen[i]=0),i},t.slug=function(e,t){void 0===t&&(t={});var i=this.serialize(e);return this.getNextSafeSlug(i,t.dryrun)},e}(),$=function(){function t(t){this.options=t||e.defaults,this.options.renderer=this.options.renderer||new R,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new L,this.slugger=new N}t.parse=function(e,i){return new t(i).parse(e)},t.parseInline=function(e,i){return new t(i).parseInline(e)};var i=t.prototype;return i.parse=function(e,t){void 0===t&&(t=!0);var i,a,n,o,r,l,s,d,u,c,x,m,f,g,h,b,y,w,E,D="",v=e.length;for(i=0;i<v;i++)if(c=e[i],!(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[c.type])||!1===(E=this.options.extensions.renderers[c.type].call({parser:this},c))&&["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(c.type))switch(c.type){case"space":continue;case"hr":D+=this.renderer.hr();continue;case"heading":D+=this.renderer.heading(this.parseInline(c.tokens),c.depth,p(this.parseInline(c.tokens,this.textRenderer)),this.slugger);continue;case"code":D+=this.renderer.code(c.text,c.lang,c.escaped);continue;case"table":for(d="",s="",o=c.header.length,a=0;a<o;a++)s+=this.renderer.tablecell(this.parseInline(c.header[a].tokens),{header:!0,align:c.align[a]});for(d+=this.renderer.tablerow(s),u="",o=c.rows.length,a=0;a<o;a++){for(s="",r=(l=c.rows[a]).length,n=0;n<r;n++)s+=this.renderer.tablecell(this.parseInline(l[n].tokens),{header:!1,align:c.align[n]});u+=this.renderer.tablerow(s)}D+=this.renderer.table(d,u);continue;case"blockquote":u=this.parse(c.tokens),D+=this.renderer.blockquote(u);continue;case"list":for(x=c.ordered,m=c.start,f=c.loose,o=c.items.length,u="",a=0;a<o;a++)b=(h=c.items[a]).checked,y=h.task,g="",h.task&&(w=this.renderer.checkbox(b),f?h.tokens.length>0&&"paragraph"===h.tokens[0].type?(h.tokens[0].text=w+" "+h.tokens[0].text,h.tokens[0].tokens&&h.tokens[0].tokens.length>0&&"text"===h.tokens[0].tokens[0].type&&(h.tokens[0].tokens[0].text=w+" "+h.tokens[0].tokens[0].text)):h.tokens.unshift({type:"text",text:w}):g+=w),g+=this.parse(h.tokens,f),u+=this.renderer.listitem(g,y,b);D+=this.renderer.list(u,x,m);continue;case"html":D+=this.renderer.html(c.text);continue;case"paragraph":D+=this.renderer.paragraph(this.parseInline(c.tokens));continue;case"text":for(u=c.tokens?this.parseInline(c.tokens):c.text;i+1<v&&"text"===e[i+1].type;)u+="\n"+((c=e[++i]).tokens?this.parseInline(c.tokens):c.text);D+=t?this.renderer.paragraph(u):u;continue;default:var P='Token with "'+c.type+'" type was not found.';if(this.options.silent)return void console.error(P);throw new Error(P)}else D+=E||"";return D},i.parseInline=function(e,t){t=t||this.renderer;var i,a,n,o="",r=e.length;for(i=0;i<r;i++)if(a=e[i],!(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type])||!1===(n=this.options.extensions.renderers[a.type].call({parser:this},a))&&["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type))switch(a.type){case"escape":case"text":o+=t.text(a.text);break;case"html":o+=t.html(a.text);break;case"link":o+=t.link(a.href,a.title,this.parseInline(a.tokens,t));break;case"image":o+=t.image(a.href,a.title,a.text);break;case"strong":o+=t.strong(this.parseInline(a.tokens,t));break;case"em":o+=t.em(this.parseInline(a.tokens,t));break;case"codespan":o+=t.codespan(a.text);break;case"br":o+=t.br();break;case"del":o+=t.del(this.parseInline(a.tokens,t));break;default:var l='Token with "'+a.type+'" type was not found.';if(this.options.silent)return void console.error(l);throw new Error(l)}else o+=n||"";return o},t}();function O(e,t,i){if(null==e)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if("function"==typeof t&&(i=t,t=null),S(t=C({},O.defaults,t||{})),i){var a,n=t.highlight;try{a=I.lex(e,t)}catch(e){return i(e)}var o=function(e){var o;if(!e)try{t.walkTokens&&O.walkTokens(a,t.walkTokens),o=$.parse(a,t)}catch(t){e=t}return t.highlight=n,e?i(e):i(null,o)};if(!n||n.length<3)return o();if(delete t.highlight,!a.length)return o();var r=0;return O.walkTokens(a,function(e){"code"===e.type&&(r++,setTimeout(function(){n(e.text,e.lang,function(t,i){if(t)return o(t);null!=i&&i!==e.text&&(e.text=i,e.escaped=!0),0===--r&&o()})},0))}),void(0===r&&o())}function l(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+x(e.message+"",!0)+"</pre>";throw e}try{var s=I.lex(e,t);if(t.walkTokens){if(t.async)return Promise.all(O.walkTokens(s,t.walkTokens)).then(function(){return $.parse(s,t)}).catch(l);O.walkTokens(s,t.walkTokens)}return $.parse(s,t)}catch(e){l(e)}}O.options=O.setOptions=function(t){var i;return C(O.defaults,t),i=O.defaults,e.defaults=i,O},O.getDefaults=o,O.defaults=e.defaults,O.use=function(){for(var e=O.defaults.extensions||{renderers:{},childTokens:{}},t=arguments.length,i=new Array(t),a=0;a<t;a++)i[a]=arguments[a];i.forEach(function(t){var i=C({},t);if(i.async=O.defaults.async||i.async,t.extensions&&(t.extensions.forEach(function(t){if(!t.name)throw new Error("extension name required");if(t.renderer){var i=e.renderers[t.name];e.renderers[t.name]=i?function(){for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];var o=t.renderer.apply(this,a);return!1===o&&(o=i.apply(this,a)),o}:t.renderer}if(t.tokenizer){if(!t.level||"block"!==t.level&&"inline"!==t.level)throw new Error("extension level must be 'block' or 'inline'");e[t.level]?e[t.level].unshift(t.tokenizer):e[t.level]=[t.tokenizer],t.start&&("block"===t.level?e.startBlock?e.startBlock.push(t.start):e.startBlock=[t.start]:"inline"===t.level&&(e.startInline?e.startInline.push(t.start):e.startInline=[t.start]))}t.childTokens&&(e.childTokens[t.name]=t.childTokens)}),i.extensions=e),t.renderer&&function(){var e=O.defaults.renderer||new R,a=function(i){var a=e[i];e[i]=function(){for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];var l=t.renderer[i].apply(e,o);return!1===l&&(l=a.apply(e,o)),l}};for(var n in t.renderer)a(n);i.renderer=e}(),t.tokenizer&&function(){var e=O.defaults.tokenizer||new V,a=function(i){var a=e[i];e[i]=function(){for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];var l=t.tokenizer[i].apply(e,o);return!1===l&&(l=a.apply(e,o)),l}};for(var n in t.tokenizer)a(n);i.tokenizer=e}(),t.walkTokens){var a=O.defaults.walkTokens;i.walkTokens=function(e){var i=[];return i.push(t.walkTokens.call(this,e)),a&&(i=i.concat(a.call(this,e))),i}}O.setOptions(i)})},O.walkTokens=function(e,t){for(var i,n=[],o=function(){var e=i.value;switch(n=n.concat(t.call(O,e)),e.type){case"table":for(var o,r=a(e.header);!(o=r()).done;){var l=o.value;n=n.concat(O.walkTokens(l.tokens,t))}for(var s,d=a(e.rows);!(s=d()).done;)for(var u,c=a(s.value);!(u=c()).done;){var x=u.value;n=n.concat(O.walkTokens(x.tokens,t))}break;case"list":n=n.concat(O.walkTokens(e.items,t));break;default:O.defaults.extensions&&O.defaults.extensions.childTokens&&O.defaults.extensions.childTokens[e.type]?O.defaults.extensions.childTokens[e.type].forEach(function(i){n=n.concat(O.walkTokens(e[i],t))}):e.tokens&&(n=n.concat(O.walkTokens(e.tokens,t)))}},r=a(e);!(i=r()).done;)o();return n},O.parseInline=function(e,t){if(null==e)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");S(t=C({},O.defaults,t||{}));try{var i=I.lexInline(e,t);return t.walkTokens&&O.walkTokens(i,t.walkTokens),$.parseInline(i,t)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+x(e.message+"",!0)+"</pre>";throw e}},O.Parser=$,O.parser=$.parse,O.Renderer=R,O.TextRenderer=L,O.Lexer=I,O.lexer=I.lex,O.Tokenizer=V,O.Slugger=N,O.parse=O;var z=O.options,j=O.setOptions,H=O.use,W=O.walkTokens,q=O.parseInline,K=O,G=$.parse,Z=I.lex;e.Lexer=I,e.Parser=$,e.Renderer=R,e.Slugger=N,e.TextRenderer=L,e.Tokenizer=V,e.getDefaults=o,e.lexer=Z,e.marked=O,e.options=z,e.parse=K,e.parseInline=q,e.parser=G,e.setOptions=j,e.use=H,e.walkTokens=W});